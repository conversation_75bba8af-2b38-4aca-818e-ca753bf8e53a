import 'package:get/get.dart';

import '../features/auth/bindings/auth_binding.dart';
import '../features/auth/views/login_view.dart';
import '../features/auth/views/register_view.dart';
import '../features/auth/views/splash_view.dart';
import '../features/avatar/bindings/avatar_binding.dart';
import '../features/avatar/bindings/voice_selection_binding.dart';

import '../features/avatar/bindings/intimate_companion_binding.dart';
import '../features/avatar/views/avatar_complete_view.dart';
import '../features/avatar/views/avatar_invite_view.dart';
import '../features/avatar/views/avatar_basic_info_view.dart';
import '../features/avatar/views/avatar_abilities_view.dart';
import '../features/avatar/views/avatar_dialogue_view.dart';
import '../features/avatar/views/avatar_chat_file_view.dart';
import '../features/avatar/views/avatar_preview_view.dart';
import '../features/avatar/views/avatar_selection_view.dart';
import '../features/avatar/views/avatar_test_guide_view.dart';
import '../features/avatar/views/avatar_voice_view.dart';
import '../features/avatar/views/voice_tab_view.dart';
import '../features/avatar/views/voice_clone_view.dart';
import '../features/avatar/views/intimate_companion_complete_view.dart';
import '../features/avatar/views/intimate_companion_form_view.dart';
import '../features/avatar/views/voice_print_recording_view.dart';
import '../features/device/bindings/bluetooth_scan_binding.dart';
import '../features/device/bindings/device_binding.dart';
import '../features/device/bindings/device_detail_binding.dart';
import '../features/device/bindings/device_management_binding.dart';
import '../features/device/bindings/wifi_config_binding.dart';
import '../features/device/views/bluetooth_scan_screen.dart';
import '../features/device/views/connection_failed_screen.dart';
import '../features/device/views/device_detail_view.dart';
import '../features/device/views/device_management_view.dart';
import '../features/device/views/device_view.dart';
import '../features/device/views/wifi_config_screen.dart';
import '../features/home/<USER>';
import '../features/home/<USER>';
import '../features/profile/bindings/profile_binding.dart';
import '../features/profile/views/profile_edit_view.dart';
import '../features/profile/views/profile_view.dart';
import '../features/settings/bindings/language_settings_binding.dart';
import '../features/settings/bindings/member_management_binding.dart';
import '../features/settings/bindings/settings_binding.dart';
import '../features/settings/views/language_settings_view.dart';
import '../features/settings/views/member_management_view.dart';
import '../features/settings/views/system_settings_view.dart';
import '../features/webview/webview_screen.dart';
import 'app_routes.dart';

/// 应用页面路由配置
///
/// 定义所有路由页面及其绑定
class AppPages {
  // 禁止实例化
  AppPages._();

  /// 初始路由
  static const INITIAL = Routes.SPLASH;

  /// 全局路由页面列表
  static final routes = [
    // 认证相关路由
    GetPage(
      name: Routes.SPLASH,
      page: () => const SplashView(),
      binding: AuthBinding(),
    ),
    GetPage(
      name: Routes.LOGIN,
      page: () => const LoginView(),
      binding: AuthBinding(),
    ),
    GetPage(
      name: Routes.REGISTER,
      page: () => const RegisterView(),
      binding: AuthBinding(),
    ),

    // 主要页面路由
    GetPage(
      name: Routes.HOME,
      page: () => const HomeScreen(),
      binding: HomeBinding(),
    ),
    GetPage(
      name: Routes.DEVICE_MANAGEMENT,
      page: () => const DeviceManagementView(),
      binding: DeviceManagementBinding(),
    ),
    GetPage(
      name: Routes.DEVICE_DETAIL,
      page: () => const DeviceDetailView(),
      binding: DeviceDetailBinding(),
    ),
    GetPage(
      name: Routes.WIFI_CONFIG,
      page: () => const WifiConfigScreen(),
      binding: WifiConfigBinding(),
    ),
    GetPage(
      name: Routes.CONNECTION_FAILED,
      page: () => const ConnectionFailedScreen(),
    ),

    // 分身相关路由
    GetPage(
      name: Routes.AVATAR_SELECTION,
      page: () => const AvatarSelectionView(),
      binding: AvatarBinding(),
    ),

    // GetPage(
    //   name: Routes.AVATAR_FORM,
    //   page: () => const AvatarFormView(),
    //   binding: AvatarBinding(),
    // ),
    GetPage(
      name: Routes.AVATAR_BASIC_INFO,
      page: () => const AvatarBasicInfoView(),
      binding: AvatarBinding(),
    ),
    GetPage(
      name: Routes.AVATAR_ABILITIES,
      page: () =>  AvatarAbilitiesView(),
      binding: AvatarBinding(),
    ),
    GetPage(
      name: Routes.AVATAR_DIALOGUE,
      page: () => const AvatarDialogueView(),
      binding: AvatarBinding(),
    ),
    GetPage(
      name: Routes.AVATAR_VOICE,
      page: () => const AvatarVoiceView(),
      binding: VoiceSelectionBinding(),
    ),
    GetPage(
      name: Routes.VOICE_SELECTION,
      page: () => const VoiceTabView(),
      binding: VoiceSelectionBinding(),
    ),
    GetPage(
      name: Routes.VOICE_CLONE,
      page: () =>  VoiceCloneView(),
      binding: AvatarBinding(),
    ),
    // GetPage(
    //   name: Routes.AVATAR_VOICE_CLONE,
    //   page: () => const AvatarVoiceCloneView(),
    //   binding: AvatarBinding(),
    // ),
    GetPage(
      name: Routes.AVATAR_PREVIEW,
      page: () => const AvatarPreviewView(),
      binding: AvatarBinding(),
    ),
    GetPage(
      name: Routes.AVATAR_UPLOAD_FILE,
      page: () =>  const AvatarChatFileView(),
      binding: AvatarBinding(),
    ),
    GetPage(
      name: Routes.AVATAR_COMPLETE,
      page: () => const AvatarCompleteView(),
      binding: AvatarBinding(),
    ),
    GetPage(
      name: Routes.AVATAR_TEST_GUIDE,
      page: () => const AvatarTestGuideView(),
      binding: AvatarBinding(),
    ),

    // 亲密陪伴者相关路由
    GetPage(
      name: Routes.INTIMATE_COMPANION_FORM,
      page: () => const IntimateCompanionFormView(),
      binding: IntimateCompanionBinding(),
    ),
    GetPage(
      name: Routes.VOICE_PRINT_RECORDING,
      page: () => const VoicePrintRecordingView(),
      binding: IntimateCompanionBinding(),
    ),
    GetPage(
      name: Routes.INTIMATE_COMPANION_COMPLETE,
      page: () => const IntimateCompanionCompleteView(),
    ),

    // 配网相关路由
    GetPage(
      name: Routes.BLUETOOTH_SCAN,
      page: () => const BluetoothScanScreen(),
      binding: BluetoothScanBinding(),
    ),
    GetPage(
      name: Routes.DEVICE_SETUP,
      page: () => const DeviceView(),
      binding: DeviceBinding(),
    ),

    // 个人资料相关路由
    GetPage(
      name: Routes.PROFILE,
      page: () => const ProfileDetailView(),
      binding: ProfileBinding(),
    ),
    GetPage(
      name: Routes.EDIT_PROFILE,
      page: () => const ProfileEditView(),
      binding: ProfileBinding(),
    ),
    GetPage(
      name: Routes.AVATAR_INVITE,
      page: () => const AvatarInviteView(),
      binding: AvatarBinding(),
    ),

    // 设置相关路由
    GetPage(
      name: Routes.SETTINGS,
      page: () => const SystemSettingsView(),
      binding: SettingsBinding(),
    ),
    GetPage(
      name: Routes.LANGUAGE_SETTINGS,
      page: () => const LanguageSettingsView(),
      binding: LanguageSettingsBinding(),
    ),
    GetPage(
      name: Routes.SYSTEM_SETTINGS,
      page: () => const SystemSettingsView(),
      binding: SettingsBinding(),
    ),
    GetPage(
      name: Routes.MEMBER_MANAGEMENT,
      page: () => const MemberManagementView(),
      binding: MemberManagementBinding(),
    ),
    // 其他路由
    GetPage(
      name: Routes.WEBVIEW,
      page: () => const WebViewScreen(),
    ),
  ];
}

import 'package:get/get.dart';

/// 路由常量
/// 定义应用程序中所有路由的名称常量
class Routes {
  /// 认证相关路由
  static const String SPLASH = '/splash';
  static const String LOGIN = '/login';
  static const String REGISTER = '/register';

  /// 主要页面路由
  static const String HOME = '/home';
  static const String DEVICE_MANAGEMENT = '/device-management';
  static const String DEVICE_DETAIL = '/device-detail';
  static const String WIFI_CONFIG = '/wifi-config';
  static const String CONNECTION_FAILED = '/connection-failed';
  static const String BLUETOOTH_SCAN = '/bluetooth-scan';

  /// 分身相关路由
  static const String AVATAR_SELECTION = '/avatar-selection';
  static const String AVATAR_CREATION_FLOW = '/avatar-creation-flow';

  // static const String AVATAR_FORM = '/avatar-form';
  static const String AVATAR_VOICE = '/avatar-voice';
  static const String AVATAR_VOICE_CLONE = '/avatar-voice-clone';
  static const String VOICE_SELECTION = '/voice-selection';
  static const String VOICE_CLONE = '/voice-clone';
  static const String AVATAR_PREVIEW = '/avatar-preview';
  static const String AVATAR_UPLOAD_FILE = '/avatar-upload-file';
  static const String AVATAR_COMPLETE = '/avatar-complete';
  static const String AVATAR_TEST_GUIDE = '/avatar-test-guide';
  static const String AVATAR_INVITE = '/avatar-invite';
  static const String AVATAR_BASIC_INFO = '/avatar-basic-info';
  static const String AVATAR_ABILITIES = '/avatar-abilities';
  static const String AVATAR_DIALOGUE = '/avatar-dialogue';

  /// 亲密陪伴者相关路由
  static const String INTIMATE_COMPANION_FORM = '/intimate-companion-form';
  static const String VOICE_PRINT_RECORDING = '/voice-print-recording';
  static const String INTIMATE_COMPANION_COMPLETE = '/intimate-companion-complete';

  /// 配网相关路由
  static const String DEVICE_SETUP = '/device-setup';
  static const String BLUFI_TEST = '/blufi-test';

  /// 设置相关路由
  static const String SETTINGS = '/settings';
  static const String LANGUAGE_SETTINGS = '/language-settings';
  static const String SYSTEM_SETTINGS = '/system-settings';
  static const String MEMBER_MANAGEMENT = '/member-management';
  static const String PHONE_SETTINGS = '/phone-settings';
  static const String DATA_INIT = '/data-init';
  static const String SUPPORT = '/support';
  static const String LANGUAGE = '/language';

  /// 个人资料相关路由
  static const String PROFILE = '/profile';
  static const String EDIT_PROFILE = '/edit-profile';

  /// 其他路由
  static const String WEBVIEW = '/webview';
}

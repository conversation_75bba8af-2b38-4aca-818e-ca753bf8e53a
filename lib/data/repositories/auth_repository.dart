import '../../core/network/api_config.dart';
import '../../core/network/http_service.dart';
import '../../core/utils/logger.dart';
import '../models/user_model.dart';
import '../models/current_info_model.dart';

/// 身份认证仓库
/// 处理用户认证相关的API请求
class AuthRepository {
  final HttpService _http = HttpService.instance;

  /// 用户登录
  Future<UserModel?> login(String email, String password) async {
    AppLogger.info('尝试登录: $email');

    return _http.get<UserModel>(
      path: ApiConfig.login,
      queryParams: {
        'email': email,
        'password': password,
      },
      fromJson: (json) => UserModel.fromJson(json),
    );
  }

  /// 用户登录 - 带 Loading
  Future<UserModel?> loginWithLoading(String email, String password) async {
    AppLogger.info('尝试登录: $email');

    return _http.getWithLoading<UserModel>(
      path: ApiConfig.login,
      queryParams: {
        'email': email,
        'password': password,
      },
      fromJson: (json) => UserModel.fromJson(json),
      loadingText: '登录中...',
    );
  }

  /// 用户注册
  Future<UserModel?> register({
    required String username,
    required String email,
    required String password,
  }) async {
    AppLogger.info('尝试注册: $email');

    return _http.postWithLoading<UserModel>(
      path: ApiConfig.register,
      data: {
        'userName': username,
        'email': email,
        'password': password,
      },
      fromJson: (json) => UserModel.fromJson(json),
      loadingText: '注册中...',
    );
  }


  /// 获取用户基本信息
  Future<UserModel?> getUserInfoByUserId(int userId) async {
    AppLogger.info('获取用户基本信息: userId=$userId');

    return _http.post<UserModel>(
      path: ApiConfig.getUserInfoByUserId,
      queryParams: {'id': userId},
      fromJson: (json) => UserModel.fromJson(json),
    );
  }

  /// 获取用户当前进度信息
  Future<CurrentInfoModel?> getCurrentInfo(int userId) async {
    AppLogger.info('获取用户当前进度信息: userId=$userId');

    return _http.get<CurrentInfoModel>(
      path: ApiConfig.getCurrentInfo,
      queryParams: {'userId': userId},
      fromJson: (json) => CurrentInfoModel.fromJson(json),
    );
  }

  /// 获取用户信息 - 兼容旧版本
  Future<UserModel?> getUserInfo(int userId) async {
    return getUserInfoByUserId(userId);
  }
}

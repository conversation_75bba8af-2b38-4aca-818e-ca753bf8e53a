import 'dart:io';
import 'package:dio/dio.dart' as dio;

import '../../core/network/api_config.dart';
import '../../core/network/http_service.dart';
import '../../core/utils/logger.dart';
import '../models/avatar_model.dart';
import '../models/doll_profile_info.dart';

import '../models/partner_model.dart';
import '../models/user_file_model.dart';

/// 分身数据仓库
class AvatarRepository {
  final HttpService _http = HttpService.instance;

  /// 创建分身 - 完成分身创建流程
  Future<DollProfileInfo?> createCloneInfo(DollProfileInfo dollProfile,
      {required int userId}) async {
    AppLogger.info('创建分身: ${dollProfile.dollName}, userId: $userId');

    final data = dollProfile.toJson();
    data['userId'] = userId;

    return _http.post<DollProfileInfo>(
      path: ApiConfig.createCloneInfo,
      data: data,
      fromJson: (json) =>
          DollProfileInfo.fromJson(json as Map<String, dynamic>),
    );
  }

  /// 更新分身
  Future<bool?> updateCloneInfo(DollProfileInfo dollProfile) async {
    AppLogger.info('更新分身: ${dollProfile.dollName}');

    return _http.post<bool>(
      path: ApiConfig.updateCloneInfo,
      data: dollProfile.toJson(),
      fromJson: (json) => json as bool,
    );
  }

  /// 拉取分身信息
  Future<DollProfileInfo?> getCloneInfoByAgentId(String agentId) async {
    AppLogger.info('拉取分身信息: agentId=$agentId');

    return _http.post<DollProfileInfo>(
      path: ApiConfig.getCloneInfoByAgentId,
      queryParams: {
        'agentId': agentId,
      },
      fromJson: (json) =>
          DollProfileInfo.fromJson(json as Map<String, dynamic>),
    );
  }

  /// 获取我的分身信息
  Future<List<Map<String, dynamic>>?> getMyCloneInfo(int userId) async {
    AppLogger.info('获取我的分身信息: userId=$userId');

    return _http.get<List<Map<String, dynamic>>>(
      path: ApiConfig.getMyCloneInfo,
      queryParams: {
        'userId': userId,
      },
      fromJson: (json) {
        final List<dynamic> list = json as List<dynamic>;
        return list.cast<Map<String, dynamic>>();
      },
    );
  }

  /// 邀请他人创建分身
  Future<bool?> invitation(List<Map<String, dynamic>> invitations) async {
    AppLogger.info('邀请他人创建分身');

    return _http.post<bool>(
      path: ApiConfig.invitation,
      data: invitations,
      fromJson: (json) => json as bool? ?? false,
    );
  }

  /// 获取邀请列表
  Future<List<Map<String, dynamic>>?> getInvitationList(String email) async {
    AppLogger.info('获取邀请列表: email=$email');

    return _http.get<List<Map<String, dynamic>>>(
      path: ApiConfig.getInvitationList,
      queryParams: {
        'email': email,
      },
      fromJson: (json) {
        final List<dynamic> list = json as List<dynamic>;
        return list.cast<Map<String, dynamic>>();
      },
    );
  }

  /// 获取分身使用者列表
  Future<List<DollUserProfileInfo>?> getIntimateCompanionInfos(
      String agentId) async {
    return _http.get<List<DollUserProfileInfo>>(
      path: ApiConfig.getIntimateCompanionInfos,
      queryParams: {'agentId': agentId},
      fromJson: (json) {
        final List<dynamic> list = json as List<dynamic>;
        return list
            .map((item) =>
                DollUserProfileInfo.fromJson(item as Map<String, dynamic>))
            .toList();
      },
    );
  }

  /// 获取音色列表
  Future<List<VoiceDTO>?> getVoiceList(int type) async {
    AppLogger.info('获取音色列表: type=$type');

    return _http.get<List<VoiceDTO>>(
      path: ApiConfig.getVoiceList,
      queryParams: {'type': type},
      fromJson: (json) {
        final List<dynamic> list = json as List<dynamic>;
        return list
            .map((item) => VoiceDTO.fromJson(item as Map<String, dynamic>))
            .toList();
      },
    );
  }

  /// 获取朗读内容
  Future<String?> getReadingContent() async {
    AppLogger.info('获取朗读内容');

    return _http.get<String>(
      path: ApiConfig.getReadingContent,
      fromJson: (json) {
        if (json is String) {
          return json;
        } else if (json is Map<String, dynamic> &&
            json.containsKey('content')) {
          return json['content'] as String;
        } else {
          return json.toString();
        }
      },
    );
  }

  /// 上传音频文件进行音色克隆
  Future<Map<String, bool>?> uploadVoiceCloneFile({
    required String agentId,
    required int type,
    required int userId,
    String? ttsVoice,
    File? file,
  }) async {
    // type=1选择音色时使用POST请求，不上传文件
    if (type == 1) {
      return _http.post<Map<String, bool>>(
        path: ApiConfig.uploadVoiceCloneFile,
        data: dio.FormData.fromMap({
          'agentId': agentId,
          'userId': userId.toString(),
          'type': type.toString(),
          if (ttsVoice != null) 'ttsVoice': ttsVoice,
        }),
        fromJson: (json) {
          final Map<String, dynamic> data = json as Map<String, dynamic>;
          return data.map((key, value) => MapEntry(key, value as bool));
        },
      );
    } else {
      // type=2,3需要上传文件
      if (file == null) {
        throw Exception('文件不能为空');
      }

      return _http.upload<Map<String, bool>>(
        path: ApiConfig.uploadVoiceCloneFile,
        filePath: file.path,
        fileKey: 'file',
        extraData: {
          'agentId': agentId,
          'userId': userId.toString(),
          'type': type,
        },
        fromJson: (json) {
          final Map<String, dynamic> data = json as Map<String, dynamic>;
          return data.map((key, value) => MapEntry(key, value as bool));
        },
      );
    }
  }

  /// 上传声纹
  Future<Map<String, bool>?> uploadVoicePrint({
    required String dollProfileUserId,
    required File file,
  }) async {
    return _http.upload<Map<String, bool>>(
      path: ApiConfig.uploadVoicePrint,
      filePath: file.path,
      fileKey: 'file',
      extraData: {
        'dollProfileUserId': dollProfileUserId,
      },
      fromJson: (json) {
        final Map<String, dynamic> data = json as Map<String, dynamic>;
        return data.map((key, value) => MapEntry(key, value as bool));
      },
    );
  }

  /// 上传聊天记录
  Future<Map<String, bool>?> uploadChatFile({
    required int userId,
    required File file,
  }) async {
    return _http.upload<Map<String, bool>>(
      path: ApiConfig.uploadChatFile,
      filePath: file.path,
      fileKey: 'file',
      extraData: {
        'userId': userId,
      },
      fromJson: (json) {
        final Map<String, dynamic> data = json as Map<String, dynamic>;
        return data.map((key, value) => MapEntry(key, value as bool));
      },
    );
  }

  /// 获取文件上传列表
  Future<List<UserFile>?> getUploadChatFiles({
    required int userId,
  }) async {
    AppLogger.info('获取文件上传列表: userId=$userId');

    return _http.get<List<UserFile>>(
      path: ApiConfig.getUploadChatFiles,
      queryParams: {
        'userId': userId,
      },
      fromJson: (json) {
        final List<dynamic> list = json as List<dynamic>;
        return list
            .map((item) => UserFile.fromJson(item as Map<String, dynamic>))
            .toList();
      },
    );
  }

  /// 删除上传聊天文件
  Future<Map<String, bool>?> delUploadChatFile({
    required int id,
  }) async {
    AppLogger.info('删除上传聊天文件: id=$id');

    return _http.post<Map<String, bool>>(
      path: ApiConfig.delUploadChatFile,
      data: {
        'id': id,
      },
      fromJson: (json) {
        final Map<String, dynamic> data = json as Map<String, dynamic>;
        return data.map((key, value) => MapEntry(key, value as bool));
      },
    );
  }

  /// 更新上传聊天记录
  Future<Map<String, bool>?> updateUploadChatFile({
    required int userId,
    required File file,
  }) async {
    return _http.upload<Map<String, bool>>(
      path: ApiConfig.updateUploadChatFile,
      filePath: file.path,
      fileKey: 'file',
      extraData: {
        'userId': userId,
      },
      fromJson: (json) {
        final Map<String, dynamic> data = json as Map<String, dynamic>;
        return data.map((key, value) => MapEntry(key, value as bool));
      },
    );
  }

  /// 创建用户资料信息
  Future<DollUserProfileInfo?> createIntimateCompanionInfo(
      DollUserProfileInfoDTO profileData) async {
    AppLogger.info('创建用户资料信息: agentId=${profileData.agentId}');
    AppLogger.info('API路径: ${ApiConfig.createIntimateCompanionInfo}');
    AppLogger.info('请求数据: ${profileData.toJson()}');

    try {
      final result = await _http.post<DollUserProfileInfo>(
        path: ApiConfig.createIntimateCompanionInfo,
        data: profileData.toJson(),
        fromJson: (json) =>
            DollUserProfileInfo.fromJson(json as Map<String, dynamic>),
      );
      AppLogger.info('API调用成功，返回结果: $result');
      return result;
    } catch (e) {
      AppLogger.error('API调用失败', e);
      rethrow;
    }
  }

  /// 获取亲密陪伴者邀请列表
  Future<List<PartnerModel>?> getInviteIntimateCompanionInfo(int userId) async {
    AppLogger.info('获取亲密陪伴者邀请列表: userId=$userId');

    return _http.get<List<PartnerModel>>(
      path: ApiConfig.getInviteIntimateCompanionInfo,
      queryParams: {
        'userId': userId,
      },
      fromJson: (json) {
        if (json is List) {
          return json
              .map(
                  (item) => PartnerModel.fromJson(item as Map<String, dynamic>))
              .toList();
        }
        return <PartnerModel>[];
      },
    );
  }

  /// 邀请亲密陪伴者
  Future<Map<String, bool>?> inviteIntimateCompanionInfo(
      List<PartnerModel> partners) async {
    AppLogger.info('邀请亲密陪伴者: ${partners.map((p) => p.email).join(", ")}');

    return _http.post<Map<String, bool>>(
      path: ApiConfig.inviteIntimateCompanionInfo,
      data: partners.map((partner) => partner.toJson()).toList(),
      fromJson: (json) {
        final Map<String, dynamic> data = json as Map<String, dynamic>;
        return data.map((key, value) => MapEntry(key, value as bool));
      },
    );
  }

  /// 取消亲密陪伴者邀请
  Future<Map<String, bool>?> cancelInviteIntimateCompanionInfo(
      List<PartnerModel> partners) async {
    AppLogger.info('取消亲密陪伴者邀请: ${partners.map((p) => p.email).join(", ")}');
    AppLogger.info(
        '取消亲密陪伴者邀请userId: ${partners.map((p) => p.userId).join(", ")}');

    return _http.post<Map<String, bool>>(
      path: ApiConfig.cancelInviteIntimateCompanionInfo,
      data: partners.map((partner) => partner.toJson()).toList(),
      fromJson: (json) {
        final Map<String, dynamic> data = json as Map<String, dynamic>;
        return data.map((key, value) => MapEntry(key, value as bool));
      },
    );
  }

  /// 获取克隆音色 - 测试模式使用
  Future<TtsVoiceClone?> getVoiceCloneFile(String agentId) async {
    AppLogger.info('获取克隆音色: agentId=$agentId');

    return _http.get<TtsVoiceClone>(
      path: ApiConfig.getVoiceCloneFile,
      queryParams: {'agentId': agentId},
      fromJson: (json) => TtsVoiceClone.fromJson(json as Map<String, dynamic>),
    );
  }

  /// 编辑克隆音色 - 测试模式更新使用
  Future<Map<String, bool>?> editVoiceCloneFile({
    required int id,
    required String agentId,
    required int type,
    String? ttsVoice,
    File? file,
  }) async {
    AppLogger.info('编辑克隆音色: agentId=$agentId, type=$type');

    // type=1选择音色时使用POST请求，不上传文件
    if (type == 1) {
      return _http.post<Map<String, bool>>(
        path: ApiConfig.editVoiceCloneFile,
        data: dio.FormData.fromMap({
          'id':id,
          'type': type.toString(),
          if (ttsVoice != null) 'ttsVoice': ttsVoice,
        }),
        fromJson: (json) {
          final Map<String, dynamic> data = json as Map<String, dynamic>;
          return data.map((key, value) => MapEntry(key, value as bool));
        },
      );
    } else {
      // type=2,3需要上传文件
      if (file == null) {
        throw Exception('文件不能为空');
      }

      return _http.upload<Map<String, bool>>(
        path: ApiConfig.editVoiceCloneFile,
        filePath: file.path,
        fileKey: 'file',
        extraData: {
          'id':id,
          'type': type,
        },
        fromJson: (json) {
          final Map<String, dynamic> data = json as Map<String, dynamic>;
          return data.map((key, value) => MapEntry(key, value as bool));
        },
      );
    }
  }
}

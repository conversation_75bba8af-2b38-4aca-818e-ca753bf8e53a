
import 'package:smartai/data/models/member_model.dart';

import '../../core/network/api_config.dart';
import '../../core/network/http_service.dart';
import '../models/user_model.dart';

/// 用户仓库
/// 处理用户相关的API请求
class UserRepository {
  final HttpService _http = HttpService.instance;

  /// 获取用户信息 - 基础方法
  Future<UserModel?> getUserInfo(int userId) async {
    return _http.get<UserModel>(
      path: ApiConfig.getUserInfoByUserId,
      queryParams: {'id': userId},
      fromJson: (json) => UserModel.fromJson(json),
    );
  }





  /// 上传用户头像
  Future<String?> uploadAvatar(String filePath) async {
    // 注意：此处路径需要根据实际API配置修改
    const String uploadAvatarPath = '/app/uploadAvatar';
    return _http.upload<String>(
      path: uploadAvatarPath,
      filePath: filePath,
      fileKey: 'file',
      fromJson: (json) => json as String,
    );
  }

  /// 获取创建者信息 - 临时方法
  Future<MemberModel?> getCreator(String agentId) async {
    // 暂时返回模拟数据
 /*   final mockUser = UserModel(
    );
*/
/*    return _http.get<List<MemberModel>>(
      path: ApiConfig.getIntimateCompanionInfos,
      queryParams: {
        'agentId':agentId,
        'intimacyType': 100,
      },
        fromJson: (json) {
          final List<dynamic> list = json as List<dynamic>;
          return list
              .map((item) =>
              MemberModel.fromJson(item as Map<String, dynamic>))
              .toList();
        }
    );*/
    return _http.get<MemberModel?>(
        path: ApiConfig.getIntimateCompanionInfos,
        queryParams: {
          'agentId':agentId,
          'intimacyType': 300,//创建者 - 300
        },
        fromJson: (json) {
          // 处理空数组的情况
          if (json == null) {
            return null;
          }
          
          final List<dynamic> list = json as List<dynamic>;
          if (list.isNotEmpty) { 
            return MemberModel.fromJson(list.first as Map<String, dynamic>);
          } else {
            // 空数组表示没有创建者，这是正常情况，返回null让上层处理
            return null;
          }
        }
    );
  }

  /// 获取亲密陪伴者列表 - 临时方法
  Future<List<MemberModel>?> getIntimateMembers(agentId) async {
    return _http.get<List<MemberModel>>(
        path: ApiConfig.getIntimateCompanionInfos,
        queryParams: {
          'agentId':agentId,
          'intimacyType': 100,//亲密陪伴者
        },
        fromJson: (json) {
          // 处理空数组的情况
          if (json == null) {
            return <MemberModel>[];
          }
          
          final List<dynamic> list = json as List<dynamic>;
          // 如果是空数组，直接返回空列表，这是正常情况
          if (list.isEmpty) {
            return <MemberModel>[];
          }
          
          return list
              .map((item) =>
              MemberModel.fromJson(item as Map<String, dynamic>))
              .toList();
        }
    );
  }

  /// 获取普通陪伴者列表 - 临时方法
  Future<List<MemberModel>?> getRegularMembers(agentId) async {
    return _http.get<List<MemberModel>>(
        path: ApiConfig.getIntimateCompanionInfos,
        queryParams: {
          'agentId':agentId,
          'intimacyType': 200,//普通陪伴者
        },
        fromJson: (json) {
          // 处理空数组的情况
          if (json == null) {
            return <MemberModel>[];
          }
          
          final List<dynamic> list = json as List<dynamic>;
          // 如果是空数组，直接返回空列表，这是正常情况
          if (list.isEmpty) {
            return <MemberModel>[];
          }
          
          return list
              .map((item) =>
              MemberModel.fromJson(item as Map<String, dynamic>))
              .toList();
        }
    );
  }

  /// 获取当前用户类型 - 临时方法
  Future<String?> getCurrentUserType() async {
    return 'regular';
  }

  /// 添加亲密陪伴者 - 临时方法
  Future<bool?> addIntimateMember(String invitationCode) async {
    return true;
  }

  /// 添加普通陪伴者 - 临时方法
  Future<bool?> addRegularMember(String name) async {
    return true;
  }

  /// 移除亲密陪伴者 - 临时方法
  Future<bool?> removeIntimateMember(String memberId) async {
    return _http.get<bool>(
        path: ApiConfig.deleteIntimateCompanionInfos,
        queryParams: {
          'id':memberId,
        },
      fromJson: (json) => json as bool? ?? false,
    );
  }

  /// 移除普通陪伴者 - 临时方法
  Future<bool?> removeRegularMember(String memberId) async {
    return true;
  }

  /// 获取邀请码 - 临时方法
  Future<String?> getInvitationCode() async {
    return 'INVITE123';
  }

  /// 验证邀请码 - 临时方法
  Future<bool?> verifyInvitationCode(String code) async {
    return true;
  }
}

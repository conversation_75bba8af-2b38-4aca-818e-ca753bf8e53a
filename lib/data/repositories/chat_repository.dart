import 'dart:io';


import '../../core/network/api_config.dart';
import '../../core/network/http_service.dart';
import '../../core/utils/logger.dart';

/// 聊天数据仓库
/// 处理聊天相关的API请求
class ChatRepository {
  final HttpService _http = HttpService.instance;

  /// 查询对话历史 - 消息页面调用
  Future<Map<String, dynamic>?> getChatHistory({
    required String agentId,
    int page = 1,
    int limit = 20,
  }) async {
    AppLogger.info('查询对话历史: agentId=$agentId, page=$page, limit=$limit');
    
    return _http.get<Map<String, dynamic>>(
      path: ApiConfig.getChatHistory,
      queryParams: {
        'agentId': agentId,
        'page': page,
        'limit': limit,
      },
      fromJson: (json) => json as Map<String, dynamic>,
    );
  }
}





import '../../core/network/api_config.dart';
import '../../core/network/http_service.dart';

import '../../core/utils/logger.dart';
import '../models/device_info_model.dart';
import '../models/clone_device_info_model.dart';
import '../models/device_model.dart';

/// 设备数据仓库
class DeviceRepository {
  final HttpService _http = HttpService.instance;

  /// 设备绑定 - 扫码配网成功后调用
  Future<T?> bindDevice<T>(Map<String, dynamic> deviceData) async {
    AppLogger.info('绑定设备: ${deviceData['macAddress']}');

    return _http.postWithLoading<T>(
      path: ApiConfig.bindDevice,
      data: deviceData,
      loadingText: '绑定设备中...',
    );
  }

  /// 解绑设备
  Future<bool?> unbindDevice({
    required String macAddress,
    required String userId,
  }) async {
    AppLogger.info('解绑设备: macAddress=$macAddress, userId=$userId');

    return _http.postWithLoading<bool>(
      path: ApiConfig.unbindDevice,
      data: {
        'macAddress': macAddress,
        'userId': userId,
      },
      fromJson: (json) => json as bool? ?? false,
      loadingText: '解绑设备中...',
    );
  }

  /// 更新设备信息
  Future<bool?> updateDeviceInfo(Map<String, dynamic> deviceInfo) async {
    AppLogger.info('更新设备信息');

    return _http.post<bool>(
      path: ApiConfig.updateDeviceInfo,
      data: deviceInfo,
      fromJson: (json) => json as bool? ?? false,
    );
  }


  
  /// 获取设备绑定信息 - 首页调用
  Future<DeviceModel?> getBindInfo({
    String? macAddress,
    int? userId,
  }) async {
    AppLogger.info('获取设备绑定信息: macAddress=$macAddress, userId=$userId');

    final Map<String, dynamic> params = {};
    if (macAddress != null) params['macAddress'] = macAddress;
    if (userId != null) params['userId'] = userId;

    return _http.get<DeviceModel>(
      path: ApiConfig.getBindInfo,
      queryParams: params,
      fromJson: (json) => DeviceModel.fromJson(json as Map<String, dynamic>),
    );
  }




  /// 获取用户设备列表 - 临时方法，返回空列表
  Future<List<CloneDeviceInfo>> getUserDevicesList(int? userId) async {
    AppLogger.info('获取用户设备列表');

    return await _http.get<List<CloneDeviceInfo>>(
        path: ApiConfig.getCloneList,
        queryParams: {
          'userId': userId,
        },
        fromJson: (json) {
          // 处理空数组的情况
          if (json == null) {
            return <CloneDeviceInfo>[];
          }
          final List<dynamic> list = json as List<dynamic>;
          // 如果是空数组，直接返回空列表，这是正常情况
          if (list.isEmpty) {
            return <CloneDeviceInfo>[];
          }
          return list
              .map((item) =>
              CloneDeviceInfo.fromJson(item as Map<String, dynamic>))
              .toList();
        }
    ) ?? <CloneDeviceInfo>[];
  }



  /// 获取用户设备列表 - 临时方法，返回空列表
  Future<List<DeviceModel>> getUserDevices() async {
    AppLogger.info('获取用户设备列表');

    // 暂时返回空列表，因为API文档中没有这个接口
    return <DeviceModel>[];
  }

  /// 根据ID获取设备详情 - 临时方法
  Future<DeviceInfoModel?> getDeviceById(String agentId) async {
    AppLogger.info('获取设备详情: agentId=$agentId');

    return _http.get<DeviceInfoModel>(

      path: ApiConfig.getDeviceInfo,
      queryParams: {
        'agentId': agentId,
      },
      fromJson: (json) => DeviceInfoModel.fromJson(json as Map<String, dynamic>),
    );

  }

  /// 获取我的分身信息 - 临时方法
  Future<List<DeviceModel>> getMyCloneInfo(int userId) async {
    AppLogger.info('获取我的分身信息: userId=$userId');

    // 暂时返回空列表
    return <DeviceModel>[];
  }

  /// 设置夜灯模式 - 临时方法
  Future<bool?> setNightLightMode(bool enabled) async {
    AppLogger.info('设置夜灯模式: $enabled');
    return true;
  }

  /// 设置灯光模式 - 临时方法
  Future<bool?> setLightMode(String mode) async {
    AppLogger.info('设置灯光模式: $mode');
    return true;
  }

  /// 设置屏幕亮度 - 临时方法
  Future<bool?> setScreenBrightness(int brightness) async {
    AppLogger.info('设置屏幕亮度: $brightness');
    return true;
  }

  /// 设置勿扰模式 - 临时方法
  Future<bool?> setDoNotDisturbMode(bool enabled) async {
    AppLogger.info('设置勿扰模式: $enabled');
    return true;
  }

  /// 设置灯光亮度 - 临时方法
  Future<bool?> setLightBrightness(int brightness) async {
    AppLogger.info('设置灯光亮度: $brightness');
    return true;
  }

  /// 设置音量 - 临时方法
  Future<bool?> setVolume(int volume) async {
    AppLogger.info('设置音量: $volume');
    return true;
  }

  /// 发送系统消息 - 临时方法
  Future<bool?> sendSystemMessage({
    required String deviceId,
    required String message,
  }) async {
    AppLogger.info('发送系统消息: deviceId=$deviceId, message=$message');
    return true;
  }
}

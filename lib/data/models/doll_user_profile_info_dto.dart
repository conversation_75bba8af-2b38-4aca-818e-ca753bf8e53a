/// 分身用户资料信息DTO - 与API文档DollUserProfileInfoDTO对应
class DollUserProfileInfoRequest {
  String? id;
  String? agentId;
  String? dollId;
  int? userId;
  int? intimacyType; // 300表示创建者
  String? createBy;
  DateTime? createTime;
  String? updateBy;
  DateTime? updateTime;
  int? delFlag;

  DollUserProfileInfoRequest({
    this.id,
    this.agentId,
    this.dollId,
    this.userId,
    this.intimacyType,
    this.createBy,
    this.createTime,
    this.updateBy,
    this.updateTime,
    this.delFlag,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'agentId': agentId,
      'dollId': dollId,
      'userId': userId,
      'intimacyType': intimacyType,
      'createBy': createBy,
      'createTime': createTime?.toIso8601String(),
      'updateBy': updateBy,
      'updateTime': updateTime?.toIso8601String(),
      'delFlag': delFlag,
    };
  }

  factory DollUserProfileInfoRequest.fromJson(Map<String, dynamic> json) {
    return DollUserProfileInfoRequest(
      id: json['id'],
      agentId: json['agentId'],
      dollId: json['dollId'],
      userId: json['userId'],
      intimacyType: json['intimacyType'],
      createBy: json['createBy'],
      createTime: json['createTime'] != null
          ? DateTime.parse(json['createTime'])
          : null,
      updateBy: json['updateBy'],
      updateTime: json['updateTime'] != null
          ? DateTime.parse(json['updateTime'])
          : null,
      delFlag: json['delFlag'],
    );
  }

  DollUserProfileInfoRequest copyWith({
    String? id,
    String? agentId,
    String? dollId,
    int? userId,
    int? intimacyType,
    String? createBy,
    DateTime? createTime,
    String? updateBy,
    DateTime? updateTime,
    int? delFlag,
  }) {
    return DollUserProfileInfoRequest(
      id: id ?? this.id,
      agentId: agentId ?? this.agentId,
      dollId: dollId ?? this.dollId,
      userId: userId ?? this.userId,
      intimacyType: intimacyType ?? this.intimacyType,
      createBy: createBy ?? this.createBy,
      createTime: createTime ?? this.createTime,
      updateBy: updateBy ?? this.updateBy,
      updateTime: updateTime ?? this.updateTime,
      delFlag: delFlag ?? this.delFlag,
    );
  }
}

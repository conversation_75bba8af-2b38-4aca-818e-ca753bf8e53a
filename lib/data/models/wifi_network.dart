/// WiFi网络信息模型
class WifiNetwork {
  final String ssid;
  final bool isSecured;
  final int rssi;

  WifiNetwork({
    required this.ssid,
    required this.isSecured,
    required this.rssi,
  });

  factory WifiNetwork.fromJson(Map<String, dynamic> json) {
    return WifiNetwork(
      ssid: json['ssid'] ?? '',
      rssi: json['rssi'] ?? -100,
      isSecured: json['auth'] != 'OPEN',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'ssid': ssid,
      'rssi': rssi,
      'auth': isSecured ? 'WPA' : 'OPEN',
    };
  }

  /// 信号强度等级 (1-4)
  int get signalLevel {
    if (rssi >= -50) return 4;
    if (rssi >= -60) return 3;
    if (rssi >= -70) return 2;
    return 1;
  }

  @override
  String toString() {
    return 'WifiNetwork(ssid: $ssid, isSecured: $isSecured, rssi: $rssi)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is WifiNetwork && other.ssid == ssid;
  }

  @override
  int get hashCode => ssid.hashCode;
}

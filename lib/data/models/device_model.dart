/// 设备基础信息模型 - 与 /app/getBaseInfo 接口返回数据对应
class DeviceModel {
  // 基础信息
  String? createTime;
  String? updateTime;
  int? userId;
  int? roleId;
  String? avatar;
  String? roleName;
  String? roleDesc;
  String? voiceName;
  String? state;
  String? ttsId;
  String? modelId;
  String? modelName;
  String? sttId;
  double? temperature;
  double? topP;
  String? vadEnergyTh;
  String? vadSpeechTh;
  String? vadSilenceTh;
  String? vadSilenceMs;
  String? modelProvider;
  String? ttsProvider;
  String? isDefault;
  String? totalDevice;
  String? summaryMemory;
  String? ttsVoice;

  // 设备信息
  String? deviceId;
  String? sessionId;
  String? deviceName;
  int? deviceType;
  String? totalMessage;
  String? audioPath;
  String? lastLogin;
  String? wifiName;
  String? ip;
  String? chipModelName;
  String? type;
  String? version;
  String? functionNames;

  // 兼容性字段 - 为了保持向后兼容
  String? id; // 映射到deviceId
  String? agentId; // 从roleId映射
  String? alias; // 映射到deviceName
  String? macAddress; // 映射到deviceId
  String? batteryLevel; // 电池电量
  String? appVersion; // 应用版本

  DeviceModel({
    this.createTime,
    this.updateTime,
    this.userId,
    this.roleId,
    this.avatar,
    this.roleName,
    this.roleDesc,
    this.voiceName,
    this.state,
    this.ttsId,
    this.modelId,
    this.modelName,
    this.sttId,
    this.temperature,
    this.topP,
    this.vadEnergyTh,
    this.vadSpeechTh,
    this.vadSilenceTh,
    this.vadSilenceMs,
    this.modelProvider,
    this.ttsProvider,
    this.isDefault,
    this.totalDevice,
    this.summaryMemory,
    this.ttsVoice,
    this.deviceId,
    this.sessionId,
    this.deviceName,
    this.deviceType,
    this.totalMessage,
    this.audioPath,
    this.lastLogin,
    this.wifiName,
    this.ip,
    this.chipModelName,
    this.type,
    this.version,
    this.functionNames,
    // 兼容性字段
    this.id,
    this.agentId,
    this.alias,
    this.macAddress,
    this.batteryLevel,
    this.appVersion,
  });

  DeviceModel.fromJson(Map<String, dynamic> json) {
    createTime = json['createTime'];
    updateTime = json['updateTime'];
    userId = json['userId'];
    roleId = json['roleId'];
    avatar = json['avatar'];
    roleName = json['roleName'];
    roleDesc = json['roleDesc'];
    voiceName = json['voiceName'];
    state = json['state'];
    ttsId = json['ttsId'];
    modelId = json['modelId'];
    modelName = json['modelName'];
    sttId = json['sttId'];
    temperature = json['temperature']?.toDouble();
    topP = json['topP']?.toDouble();
    vadEnergyTh = json['vadEnergyTh'];
    vadSpeechTh = json['vadSpeechTh'];
    vadSilenceTh = json['vadSilenceTh'];
    vadSilenceMs = json['vadSilenceMs'];
    modelProvider = json['modelProvider'];
    ttsProvider = json['ttsProvider'];
    isDefault = json['isDefault'];
    totalDevice = json['totalDevice'];
    summaryMemory = json['summaryMemory'];
    ttsVoice = json['ttsVoice'];
    deviceId = json['deviceId'];
    sessionId = json['sessionId'];
    deviceName = json['deviceName'];
    deviceType = json['deviceType'];
    totalMessage = json['totalMessage'];
    audioPath = json['audioPath'];
    lastLogin = json['lastLogin'];
    wifiName = json['wifiName'];
    ip = json['ip'];
    chipModelName = json['chipModelName'];
    type = json['type'];
    version = json['version'];
    functionNames = json['functionNames'];

    // 兼容性字段映射
    id = deviceId; // id映射到deviceId
    agentId = roleId?.toString(); // agentId从roleId映射
    alias = deviceName; // alias映射到deviceName
    macAddress = deviceId; // macAddress映射到deviceId
    batteryLevel = json['batteryLevel']?.toString(); // 电池电量
    appVersion = json['appVersion'] ?? version; // 应用版本
  }

  DeviceModel copyWith({
    String? createTime,
    String? updateTime,
    int? userId,
    int? roleId,
    String? avatar,
    String? roleName,
    String? roleDesc,
    String? voiceName,
    String? state,
    String? ttsId,
    String? modelId,
    String? modelName,
    String? sttId,
    double? temperature,
    double? topP,
    String? vadEnergyTh,
    String? vadSpeechTh,
    String? vadSilenceTh,
    String? vadSilenceMs,
    String? modelProvider,
    String? ttsProvider,
    String? isDefault,
    String? totalDevice,
    String? summaryMemory,
    String? ttsVoice,
    String? deviceId,
    String? sessionId,
    String? deviceName,
    int? deviceType,
    String? totalMessage,
    String? audioPath,
    String? lastLogin,
    String? wifiName,
    String? ip,
    String? chipModelName,
    String? type,
    String? version,
    String? functionNames,
    // 兼容性字段
    String? id,
    String? agentId,
    String? alias,
    String? macAddress,
    String? batteryLevel,
    String? appVersion,
  }) {
    return DeviceModel(
      createTime: createTime ?? this.createTime,
      updateTime: updateTime ?? this.updateTime,
      userId: userId ?? this.userId,
      roleId: roleId ?? this.roleId,
      avatar: avatar ?? this.avatar,
      roleName: roleName ?? this.roleName,
      roleDesc: roleDesc ?? this.roleDesc,
      voiceName: voiceName ?? this.voiceName,
      state: state ?? this.state,
      ttsId: ttsId ?? this.ttsId,
      modelId: modelId ?? this.modelId,
      modelName: modelName ?? this.modelName,
      sttId: sttId ?? this.sttId,
      temperature: temperature ?? this.temperature,
      topP: topP ?? this.topP,
      vadEnergyTh: vadEnergyTh ?? this.vadEnergyTh,
      vadSpeechTh: vadSpeechTh ?? this.vadSpeechTh,
      vadSilenceTh: vadSilenceTh ?? this.vadSilenceTh,
      vadSilenceMs: vadSilenceMs ?? this.vadSilenceMs,
      modelProvider: modelProvider ?? this.modelProvider,
      ttsProvider: ttsProvider ?? this.ttsProvider,
      isDefault: isDefault ?? this.isDefault,
      totalDevice: totalDevice ?? this.totalDevice,
      summaryMemory: summaryMemory ?? this.summaryMemory,
      ttsVoice: ttsVoice ?? this.ttsVoice,
      deviceId: deviceId ?? this.deviceId,
      sessionId: sessionId ?? this.sessionId,
      deviceName: deviceName ?? this.deviceName,
      deviceType: deviceType ?? this.deviceType,
      totalMessage: totalMessage ?? this.totalMessage,
      audioPath: audioPath ?? this.audioPath,
      lastLogin: lastLogin ?? this.lastLogin,
      wifiName: wifiName ?? this.wifiName,
      ip: ip ?? this.ip,
      chipModelName: chipModelName ?? this.chipModelName,
      type: type ?? this.type,
      version: version ?? this.version,
      functionNames: functionNames ?? this.functionNames,
      // 兼容性字段
      id: id ?? this.id,
      agentId: agentId ?? this.agentId,
      alias: alias ?? this.alias,
      macAddress: macAddress ?? this.macAddress,
      batteryLevel: batteryLevel ?? this.batteryLevel,
      appVersion: appVersion ?? this.appVersion,
    );
  }
  Map<String, dynamic> toJson() {
    return {
      'createTime': createTime,
      'updateTime': updateTime,
      'userId': userId,
      'roleId': roleId,
      'avatar': avatar,
      'roleName': roleName,
      'roleDesc': roleDesc,
      'voiceName': voiceName,
      'state': state,
      'ttsId': ttsId,
      'modelId': modelId,
      'modelName': modelName,
      'sttId': sttId,
      'temperature': temperature,
      'topP': topP,
      'vadEnergyTh': vadEnergyTh,
      'vadSpeechTh': vadSpeechTh,
      'vadSilenceTh': vadSilenceTh,
      'vadSilenceMs': vadSilenceMs,
      'modelProvider': modelProvider,
      'ttsProvider': ttsProvider,
      'isDefault': isDefault,
      'totalDevice': totalDevice,
      'summaryMemory': summaryMemory,
      'ttsVoice': ttsVoice,
      'deviceId': deviceId,
      'sessionId': sessionId,
      'deviceName': deviceName,
      'deviceType': deviceType,
      'totalMessage': totalMessage,
      'audioPath': audioPath,
      'lastLogin': lastLogin,
      'wifiName': wifiName,
      'ip': ip,
      'chipModelName': chipModelName,
      'type': type,
      'version': version,
      'functionNames': functionNames,
      // 兼容性字段
      'id': id,
      'agentId': agentId,
      'alias': alias,
      'macAddress': macAddress,
      'batteryLevel': batteryLevel,
      'appVersion': appVersion,
    };
  }

  @override
  String toString() {
    return 'DeviceModel{deviceId: $deviceId, deviceName: $deviceName, deviceType: $deviceType, userId: $userId}';
  }

}
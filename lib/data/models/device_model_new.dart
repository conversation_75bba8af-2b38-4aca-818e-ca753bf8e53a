/// 设备信息模型 - 重构版本
/// 对应getBaseInfo接口返回的DeviceEntity
/// 专注于核心业务逻辑：设备绑定状态和分身创建状态判断
class DeviceModel {
  final String? id;
  final int? userId;
  final String? macAddress;
  final String? lastConnectedAt;
  final int? type;
  final int? autoUpdate;
  final String? board;
  final String? alias;
  final String? agentId; // 关键字段：用于判断是否已创建分身
  final String? appVersion;
  final int? sort;
  final int? updater;
  final String? updateDate;
  final int? creator;
  final String? createDate;
  final int? delFlag;
  final String? batteryLevel; // 关键字段：首页显示电量
  final String? systemVolume;
  final String? deviceMode;
  final String? tip;
  final String? remark;
  final DeviceReportModel? deviceReportReqDTO;

  DeviceModel({
    this.id,
    this.userId,
    this.macAddress,
    this.lastConnectedAt,
    this.type,
    this.autoUpdate,
    this.board,
    this.alias,
    this.agentId,
    this.appVersion,
    this.sort,
    this.updater,
    this.updateDate,
    this.creator,
    this.createDate,
    this.delFlag,
    this.batteryLevel,
    this.systemVolume,
    this.deviceMode,
    this.tip,
    this.remark,
    this.deviceReportReqDTO,
  });

  /// 核心业务状态判断方法
  
  /// 是否已绑定设备（data不为空表示已绑定）
  bool get isDeviceBound => id != null && id!.isNotEmpty;
  
  /// 是否已创建分身（agentId不为空表示已创建）
  bool get hasAvatar => agentId != null && agentId!.isNotEmpty;
  
  /// 获取电量百分比（用于首页显示）
  String get batteryPercentage => batteryLevel ?? '0%';
  
  /// 获取系统音量（用于首页显示）
  String get volumeLevel => systemVolume ?? '50%';

  /// 从JSON创建DeviceModel - 对应getBaseInfo接口返回格式
  factory DeviceModel.fromJson(Map<String, dynamic> json) {
    return DeviceModel(
      id: json['id']?.toString(),
      userId: json['userId'],
      macAddress: json['macAddress'],
      lastConnectedAt: json['lastConnectedAt'],
      type: json['type'],
      autoUpdate: json['autoUpdate'],
      board: json['board'],
      alias: json['alias'],
      agentId: json['agentId'], // 关键字段
      appVersion: json['appVersion'],
      sort: json['sort'],
      updater: json['updater'],
      updateDate: json['updateDate'],
      creator: json['creator'],
      createDate: json['createDate'],
      delFlag: json['delFlag'],
      batteryLevel: json['batteryLevel'], // 关键字段
      systemVolume: json['systemVolume'],
      deviceMode: json['deviceMode'],
      tip: json['tip'],
      remark: json['remark'],
      deviceReportReqDTO: json['deviceReportReqDTO'] != null
          ? DeviceReportModel.fromJson(json['deviceReportReqDTO'])
          : null,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'macAddress': macAddress,
      'lastConnectedAt': lastConnectedAt,
      'type': type,
      'autoUpdate': autoUpdate,
      'board': board,
      'alias': alias,
      'agentId': agentId,
      'appVersion': appVersion,
      'sort': sort,
      'updater': updater,
      'updateDate': updateDate,
      'creator': creator,
      'createDate': createDate,
      'delFlag': delFlag,
      'batteryLevel': batteryLevel,
      'systemVolume': systemVolume,
      'deviceMode': deviceMode,
      'tip': tip,
      'remark': remark,
      'deviceReportReqDTO': deviceReportReqDTO?.toJson(),
    };
  }

  /// 创建副本并更新特定字段
  DeviceModel copyWith({
    String? id,
    int? userId,
    String? macAddress,
    String? lastConnectedAt,
    int? type,
    int? autoUpdate,
    String? board,
    String? alias,
    String? agentId,
    String? appVersion,
    int? sort,
    int? updater,
    String? updateDate,
    int? creator,
    String? createDate,
    int? delFlag,
    String? batteryLevel,
    String? systemVolume,
    String? deviceMode,
    String? tip,
    String? remark,
    DeviceReportModel? deviceReportReqDTO,
  }) {
    return DeviceModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      macAddress: macAddress ?? this.macAddress,
      lastConnectedAt: lastConnectedAt ?? this.lastConnectedAt,
      type: type ?? this.type,
      autoUpdate: autoUpdate ?? this.autoUpdate,
      board: board ?? this.board,
      alias: alias ?? this.alias,
      agentId: agentId ?? this.agentId,
      appVersion: appVersion ?? this.appVersion,
      sort: sort ?? this.sort,
      updater: updater ?? this.updater,
      updateDate: updateDate ?? this.updateDate,
      creator: creator ?? this.creator,
      createDate: createDate ?? this.createDate,
      delFlag: delFlag ?? this.delFlag,
      batteryLevel: batteryLevel ?? this.batteryLevel,
      systemVolume: systemVolume ?? this.systemVolume,
      deviceMode: deviceMode ?? this.deviceMode,
      tip: tip ?? this.tip,
      remark: remark ?? this.remark,
      deviceReportReqDTO: deviceReportReqDTO ?? this.deviceReportReqDTO,
    );
  }
}

/// 设备报告模型 - 简化版本
class DeviceReportModel {
  final int? version;
  final int? userId;
  final String? uuid;
  final ApplicationModel? application;
  final OtaModel? ota;
  final BoardModel? board;
  final int? flashSize;
  final int? minimumFreeHeapSize;
  final String? macAddress;
  final String? chipModelName;
  final ChipInfoModel? chipInfo;
  final List<PartitionModel>? partitionTable;

  DeviceReportModel({
    this.version,
    this.userId,
    this.uuid,
    this.application,
    this.ota,
    this.board,
    this.flashSize,
    this.minimumFreeHeapSize,
    this.macAddress,
    this.chipModelName,
    this.chipInfo,
    this.partitionTable,
  });

  factory DeviceReportModel.fromJson(Map<String, dynamic> json) {
    return DeviceReportModel(
      version: json['version'],
      userId: json['userId'],
      uuid: json['uuid'],
      application: json['application'] != null
          ? ApplicationModel.fromJson(json['application'])
          : null,
      ota: json['ota'] != null ? OtaModel.fromJson(json['ota']) : null,
      board: json['board'] != null ? BoardModel.fromJson(json['board']) : null,
      flashSize: json['flash_size'],
      minimumFreeHeapSize: json['minimum_free_heap_size'],
      macAddress: json['mac_address'],
      chipModelName: json['chip_model_name'],
      chipInfo: json['chip_info'] != null
          ? ChipInfoModel.fromJson(json['chip_info'])
          : null,
      partitionTable: json['partition_table'] != null
          ? (json['partition_table'] as List)
              .map((item) => PartitionModel.fromJson(item))
              .toList()
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'version': version,
      'userId': userId,
      'uuid': uuid,
      'application': application?.toJson(),
      'ota': ota?.toJson(),
      'board': board?.toJson(),
      'flash_size': flashSize,
      'minimum_free_heap_size': minimumFreeHeapSize,
      'mac_address': macAddress,
      'chip_model_name': chipModelName,
      'chip_info': chipInfo?.toJson(),
      'partition_table': partitionTable?.map((item) => item.toJson()).toList(),
    };
  }
}

/// 应用信息模型
class ApplicationModel {
  final String? name;
  final String? version;
  final String? compileTime;
  final String? idfVersion;
  final String? elfSha256;

  ApplicationModel({
    this.name,
    this.version,
    this.compileTime,
    this.idfVersion,
    this.elfSha256,
  });

  factory ApplicationModel.fromJson(Map<String, dynamic> json) {
    return ApplicationModel(
      name: json['name'],
      version: json['version'],
      compileTime: json['compile_time'],
      idfVersion: json['idf_version'],
      elfSha256: json['elf_sha256'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'version': version,
      'compile_time': compileTime,
      'idf_version': idfVersion,
      'elf_sha256': elfSha256,
    };
  }
}

/// OTA信息模型
class OtaModel {
  final String? label;

  OtaModel({this.label});

  factory OtaModel.fromJson(Map<String, dynamic> json) {
    return OtaModel(label: json['label']);
  }

  Map<String, dynamic> toJson() {
    return {'label': label};
  }
}

/// 板子信息模型
class BoardModel {
  final String? type;
  final String? ssid;
  final int? rssi;
  final int? channel;
  final String? ip;
  final String? mac;

  BoardModel({
    this.type,
    this.ssid,
    this.rssi,
    this.channel,
    this.ip,
    this.mac,
  });

  factory BoardModel.fromJson(Map<String, dynamic> json) {
    return BoardModel(
      type: json['type'],
      ssid: json['ssid'],
      rssi: json['rssi'],
      channel: json['channel'],
      ip: json['ip'],
      mac: json['mac'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'ssid': ssid,
      'rssi': rssi,
      'channel': channel,
      'ip': ip,
      'mac': mac,
    };
  }
}

/// 芯片信息模型
class ChipInfoModel {
  final int? model;
  final int? cores;
  final int? revision;
  final int? features;

  ChipInfoModel({
    this.model,
    this.cores,
    this.revision,
    this.features,
  });

  factory ChipInfoModel.fromJson(Map<String, dynamic> json) {
    return ChipInfoModel(
      model: json['model'],
      cores: json['cores'],
      revision: json['revision'],
      features: json['features'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'model': model,
      'cores': cores,
      'revision': revision,
      'features': features,
    };
  }
}

/// 分区信息模型
class PartitionModel {
  final String? label;
  final int? type;
  final int? subtype;
  final int? address;
  final int? size;

  PartitionModel({
    this.label,
    this.type,
    this.subtype,
    this.address,
    this.size,
  });

  factory PartitionModel.fromJson(Map<String, dynamic> json) {
    return PartitionModel(
      label: json['label'],
      type: json['type'],
      subtype: json['subtype'],
      address: json['address'],
      size: json['size'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'label': label,
      'type': type,
      'subtype': subtype,
      'address': address,
      'size': size,
    };
  }
}

/// 用户模型 - 仅包含登录返回的用户基本信息
/// 不包含业务状态，业务状态由getBaseInfo接口返回的DeviceModel管理
class UserModel {
  final int? id;
  final String? email;
  final String? userName;
  final String? password;
  final String? avatar;
  final int? status;
  final int? createTime;
  final int? updateTime;
  final String? remark;
  final String? token;

  UserModel({
    this.id,
    this.email,
    this.userName,
    this.password,
    this.avatar,
    this.status,
    this.createTime,
    this.updateTime,
    this.remark,
    this.token,
  });

  UserModel.fromJson(Map<String, dynamic> json)
      : id = _parseId(json['id']),
        email = json['email'],
        userName = json['userName'],
        password = json['password'],
        avatar = json['avatar'],
        status = json['status'],
        createTime = _parseTime(json['createTime']),
        updateTime = _parseTime(json['updateTime']),
        remark = json['remark'],
        token = json['token'];

  /// 解析ID字段，支持字符串和整数类型
  static int? _parseId(dynamic value) {
    if (value == null) return null;
    if (value is int) return value;
    if (value is String) return int.tryParse(value);
    return null;
  }

  /// 解析时间字段，支持字符串和整数类型
  static int? _parseTime(dynamic value) {
    if (value == null) return null;
    if (value is int) return value;
    if (value is String) return int.tryParse(value);
    return null;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'userName': userName,
      'password': password,
      'avatar': avatar,
      'status': status,
      'createTime': createTime,
      'updateTime': updateTime,
      'remark': remark,
      'token': token,
    };
  }

  UserModel copyWith({
    int? id,
    String? email,
    String? userName,
    String? password,
    String? avatar,
    int? status,
    int? createTime,
    int? updateTime,
    String? remark,
    String? token,
  }) {
    return UserModel(
      id: id ?? this.id,
      email: email ?? this.email,
      userName: userName ?? this.userName,
      password: password ?? this.password,
      avatar: avatar ?? this.avatar,
      status: status ?? this.status,
      createTime: createTime ?? this.createTime,
      updateTime: updateTime ?? this.updateTime,
      remark: remark ?? this.remark,
      token: token ?? this.token,
    );
  }
}
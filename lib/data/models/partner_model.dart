/// 亲密陪伴者模型
class PartnerModel {
  final int? id;
  final int? userId;
  final String? email;
  final int? status; // 状态:1.邀请中,2.已创建,3.已取消
  final String? intimacyType; // 用户类型:100亲密陪伴者,200普通陪伴者,300创建者
  final int? createTime;
  final int? updateTime;

  PartnerModel({
    this.id,
    this.userId,
    this.email,
    this.status,
    this.intimacyType,
    this.createTime,
    this.updateTime,
  });

  PartnerModel.fromJson(Map<String, dynamic> json)
      : id = json['id'],
        userId = json['userId'],
        email = json['email'],
        status = json['status'],
        intimacyType = json['intimacyType'],
        createTime = json['createTime'],
        updateTime = json['updateTime'];

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'email': email,
      'status': status,
      'intimacyType': intimacyType,
      'createTime': createTime,
      'updateTime': updateTime,
    };
  }

  PartnerModel copyWith({
    int? id,
    int? userId,
    String? email,
    int? status,
    String? intimacyType,
    int? createTime,
    int? updateTime,
  }) {
    return PartnerModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      email: email ?? this.email,
      status: status ?? this.status,
      intimacyType: intimacyType ?? this.intimacyType,
      createTime: createTime ?? this.createTime,
      updateTime: updateTime ?? this.updateTime,
    );
  }

  // 状态枚举
  static const int STATUS_INVITING = 1; // 邀请中
  static const int STATUS_CREATED = 2;  // 已创建
  static const int STATUS_CANCELLED = 3; // 已取消

  // 亲密度类型
  static const String INTIMACY_TYPE_INTIMATE = '100'; // 亲密陪伴者
  static const String INTIMACY_TYPE_REGULAR = '200';  // 普通陪伴者
  static const String INTIMACY_TYPE_CREATOR = '300';  // 创建者

  // 获取状态描述
  String get statusDescription {
    switch (status) {
      case STATUS_INVITING:
        return '邀请中';
      case STATUS_CREATED:
        return '已创建';
      case STATUS_CANCELLED:
        return '已取消';
      default:
        return '未知状态';
    }
  }

  // 判断是否可以取消邀请
  bool get canCancel => status == STATUS_INVITING;

  // 判断是否已邀请
  bool get isInvited => status == STATUS_INVITING;

  // 判断是否已创建
  bool get isCreated => status == STATUS_CREATED;
} 
/// 分身基础信息模型 - 与API文档DollProfileInfo完全对应
class DollProfileInfo {
  int? id;
  String? agentId;
  String? dollName;
  String? dollNickName;
  String? dollGender;
  String? dollAge;
  String? dollBirthday;
  String? dollBirthdayTime;
  String? dollCity;
  String? dollZodiacSign;
  String? dollProfession;
  String? dollCharacterDescription;
  String? dollLanguageCharacteristic;
  String? createBy;
  DateTime? createTime;
  String? updateBy;
  DateTime? updateTime;
  String? email;
  int? userId;
  String? profileBrief;
  int? delFlag;
  String? exampleConversation1;
  String? exampleConversation2;
  String? exampleConversation3;
  String? exampleConversation4;
  String? exampleConversation5;

  DollProfileInfo({
    this.id,
    this.agentId,
    this.dollName,
    this.dollNickName,
    this.dollGender,
    this.dollAge,
    this.dollBirthday,
    this.dollBirthdayTime,
    this.dollCity,
    this.dollZodiacSign,
    this.dollProfession,
    this.dollCharacterDescription,
    this.dollLanguageCharacteristic,
    this.createBy,
    this.createTime,
    this.updateBy,
    this.updateTime,
    this.email,
    this.userId,
    this.profileBrief,
    this.delFlag,
    this.exampleConversation1,
    this.exampleConversation2,
    this.exampleConversation3,
    this.exampleConversation4,
    this.exampleConversation5,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'agentId': agentId,
      'dollName': dollName,
      'dollNickName': dollNickName,
      'dollGender': dollGender,
      'dollAge': dollAge,
      'dollBirthday': dollBirthday,
      'dollBirthdayTime': dollBirthdayTime,
      'dollCity': dollCity,
      'dollZodiacSign': dollZodiacSign,
      'dollProfession': dollProfession,
      'dollCharacterDescription': dollCharacterDescription,
      'dollLanguageCharacteristic': dollLanguageCharacteristic,
      'createBy': createBy,
      'createTime': createTime?.toIso8601String(),
      'updateBy': updateBy,
      'updateTime': updateTime?.toIso8601String(),
      'email': email,
      'userId': userId,
      'profileBrief': profileBrief,
      'delFlag': delFlag,
      'example_conversation_1': exampleConversation1,
      'example_conversation_2': exampleConversation2,
      'example_conversation_3': exampleConversation3,
      'example_conversation_4': exampleConversation4,
      'example_conversation_5': exampleConversation5,
    };
  }

  factory DollProfileInfo.fromJson(Map<String, dynamic> json) {
    return DollProfileInfo(
      id: json['id'],
      agentId: json['agentId'],
      dollName: json['dollName'],
      dollNickName: json['dollNickName'],
      dollGender: json['dollGender'],
      dollAge: json['dollAge'],
      dollBirthday: json['dollBirthday'],
      dollBirthdayTime: json['dollBirthdayTime'],
      dollCity: json['dollCity'],
      dollZodiacSign: json['dollZodiacSign'],
      dollProfession: json['dollProfession'],
      dollCharacterDescription: json['dollCharacterDescription'],
      dollLanguageCharacteristic: json['dollLanguageCharacteristic'],
      createBy: json['createBy'],
      createTime: json['createTime'] != null
          ? DateTime.parse(json['createTime'])
          : null,
      updateBy: json['updateBy'],
      updateTime: json['updateTime'] != null
          ? DateTime.parse(json['updateTime'])
          : null,
      email: json['email'],
      userId: json['userId'],
      profileBrief: json['profileBrief'],
      delFlag: json['delFlag'],
      exampleConversation1: json['example_conversation_1'],
      exampleConversation2: json['example_conversation_2'],
      exampleConversation3: json['example_conversation_3'],
      exampleConversation4: json['example_conversation_4'],
      exampleConversation5: json['example_conversation_5'],
    );
  }

  DollProfileInfo copyWith({
    int? id,
    String? agentId,
    String? dollName,
    String? dollNickName,
    String? dollGender,
    String? dollAge,
    String? dollBirthday,
    String? dollBirthdayTime,
    String? dollCity,
    String? dollZodiacSign,
    String? dollProfession,
    String? dollCharacterDescription,
    String? dollLanguageCharacteristic,
    String? createBy,
    DateTime? createTime,
    String? updateBy,
    DateTime? updateTime,
    String? email,
    int? userId,
    String? profileBrief,
    int? delFlag,
    String? exampleConversation1,
    String? exampleConversation2,
    String? exampleConversation3,
    String? exampleConversation4,
    String? exampleConversation5,
  }) {
    return DollProfileInfo(
      id: id ?? this.id,
      agentId: agentId ?? this.agentId,
      dollName: dollName ?? this.dollName,
      dollNickName: dollNickName ?? this.dollNickName,
      dollGender: dollGender ?? this.dollGender,
      dollAge: dollAge ?? this.dollAge,
      dollBirthday: dollBirthday ?? this.dollBirthday,
      dollBirthdayTime: dollBirthdayTime ?? this.dollBirthdayTime,
      dollCity: dollCity ?? this.dollCity,
      dollZodiacSign: dollZodiacSign ?? this.dollZodiacSign,
      dollProfession: dollProfession ?? this.dollProfession,
      dollCharacterDescription: dollCharacterDescription ?? this.dollCharacterDescription,
      dollLanguageCharacteristic: dollLanguageCharacteristic ?? this.dollLanguageCharacteristic,
      createBy: createBy ?? this.createBy,
      createTime: createTime ?? this.createTime,
      updateBy: updateBy ?? this.updateBy,
      updateTime: updateTime ?? this.updateTime,
      email: email ?? this.email,
      userId: userId ?? this.userId,
      profileBrief: profileBrief ?? this.profileBrief,
      delFlag: delFlag ?? this.delFlag,
      exampleConversation1: exampleConversation1 ?? this.exampleConversation1,
      exampleConversation2: exampleConversation2 ?? this.exampleConversation2,
      exampleConversation3: exampleConversation3 ?? this.exampleConversation3,
      exampleConversation4: exampleConversation4 ?? this.exampleConversation4,
      exampleConversation5: exampleConversation5 ?? this.exampleConversation5,
    );
  }
}

/// 音色信息模型 - 与API文档VoiceDTO对应
class VoiceDTO {
  String? voiceId;
  String? model;
  String? remark;
  int? type;
  String? url;

  VoiceDTO({this.voiceId, this.model, this.remark, this.type, this.url});

  VoiceDTO.fromJson(Map<String, dynamic> json) {
    voiceId = json['voice_id'];
    model = json['model'];
    remark = json['remark'];
    type = json['type'];
    url = json['url']; // 兼容两种字段名
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['voice_id'] = voiceId;
    data['model'] = model;
    data['remark'] = remark;
    data['type'] = type;
    data['url'] = url;
    return data;
  }
}

/// 邀请请求模型 - 与API文档InvitationReq对应
class InvitationReq {
  String? email;

  InvitationReq({this.email});

  Map<String, dynamic> toJson() {
    return {
      'email': email,
    };
  }

  factory InvitationReq.fromJson(Map<String, dynamic> json) {
    return InvitationReq(
      email: json['email'],
    );
  }
}

/// TTS音色克隆信息模型 - 对应getVoiceCloneFile接口返回的TtsVoice
class TtsVoiceClone {
  int? id;
  String? languages;
  String? name;
  String? remark;
  String? referenceAudio;
  String? referenceText;
  int? sort;
  String? ttsModelId;
  String? ttsVoice;
  String? voiceDemo;
  String? voiceType;
  String? voiceFileId;
  int? type; // 用于区分音色类型：1选择音色,2复刻音色,3上传音色

  TtsVoiceClone({
    this.id,
    this.languages,
    this.name,
    this.remark,
    this.referenceAudio,
    this.referenceText,
    this.sort,
    this.ttsModelId,
    this.ttsVoice,
    this.voiceDemo,
    this.voiceType,
    this.voiceFileId,
    this.type,
  });

  TtsVoiceClone.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    languages = json['languages'];
    name = json['name'];
    remark = json['remark'];
    referenceAudio = json['referenceAudio'];
    referenceText = json['referenceText'];
    sort = json['sort'];
    ttsModelId = json['ttsModelId'];
    ttsVoice = json['ttsVoice'];
    voiceDemo = json['voiceDemo'];
    voiceType = json['voiceType'];
    voiceFileId = json['voiceFileId'];
    type = json['type'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['languages'] = languages;
    data['name'] = name;
    data['remark'] = remark;
    data['referenceAudio'] = referenceAudio;
    data['referenceText'] = referenceText;
    data['sort'] = sort;
    data['ttsModelId'] = ttsModelId;
    data['ttsVoice'] = ttsVoice;
    data['voiceDemo'] = voiceDemo;
    data['voiceType'] = voiceType;
    data['voiceFileId'] = voiceFileId;
    data['type'] = type;
    return data;
  }
}

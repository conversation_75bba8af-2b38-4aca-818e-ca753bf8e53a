/// 设备状态信息模型
class DeviceStatus {
  final bool isConnected;
  final String? connectedSsid;
  final String? ipAddress;
  final String? macAddress;

  DeviceStatus({
    required this.isConnected,
    this.connectedSsid,
    this.ipAddress,
    this.macAddress,
  });

  factory DeviceStatus.fromJson(Map<String, dynamic> json) {
    return DeviceStatus(
      isConnected: json['sta_conn_state'] == 1,
      connectedSsid: json['sta_ssid'],
      ipAddress: json['sta_ip'],
      macAddress: json['sta_mac'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'sta_conn_state': isConnected ? 1 : 0,
      'sta_ssid': connectedSsid,
      'sta_ip': ipAddress,
      'sta_mac': macAddress,
    };
  }

  @override
  String toString() {
    return 'DeviceStatus{isConnected: $isConnected, connectedSsid: $connectedSsid, ipAddress: $ipAddress, macAddress: $macAddress}';
  }
}

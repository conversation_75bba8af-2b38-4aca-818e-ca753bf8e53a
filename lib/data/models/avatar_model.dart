

class AvatarModel {
  String? id;
  String? userId;
  String? agentId;
  String? dollName;
  String? dollNickName;
  String? dollGender;
  String? dollAge;
  String? dollProfession;
  String? dollCity;
  String? dollBirthday;
  String? dollBirthdayTime;
  String? dollCharacterDescription;
  String? dollLanguageCharacteristic;
  String? bloodType;
  String? mbti;
  String? voiceId;
  String? voiceType;
  String? avatarUrl;
  String? zodiacSign;
  List<String>? abilities; // 分身能力列表
  String? personalityTrait; // 性格特点
  Map<String, String>? dialogueExamples; // 对话示例

  AvatarModel({
    this.id,
    this.userId,
    this.agentId,
    this.dollName,
    this.dollNickName,
    this.dollGender,
    this.dollAge,
    this.dollProfession,
    this.dollCity,
    this.dollBirthday,
    this.dollBirthdayTime,
    this.dollCharacterDescription,
    this.dollLanguageCharacteristic,
    this.bloodType,
    this.mbti,
    this.voiceId,
    this.voiceType,
    this.avatarUrl,
    this.zodiacSign,
    this.abilities,
    this.personalityTrait,
    this.dialogueExamples,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'agentId': agentId,
      'dollName': dollName,
      'dollNickName': dollNickName,
      'dollGender': dollGender,
      'dollAge': dollAge,
      'dollProfession': dollProfession,
      'dollCity': dollCity,
      'dollBirthday': dollBirthday,
      'dollBirthdayTime': dollBirthdayTime,
      'dollCharacterDescription': dollCharacterDescription,
      'dollLanguageCharacteristic': dollLanguageCharacteristic,
      'bloodType': bloodType,
      'mbti': mbti,
      'voiceId': voiceId,
      'voiceType': voiceType,
      'avatarUrl': avatarUrl,
      'zodiacSign': zodiacSign,
      'abilities': abilities,
      'personalityTrait': personalityTrait,
      'dialogueExamples': dialogueExamples,
    };
  }

  factory AvatarModel.fromJson(Map<String, dynamic> json) {
    return AvatarModel(
      id: json['id'],
      userId: json['userId'],
      agentId: json['agentId'],
      dollName: json['dollName'],
      dollNickName: json['dollNickName'],
      dollGender: json['dollGender'],
      dollAge: json['dollAge'],
      dollProfession: json['dollProfession'],
      dollCity: json['dollCity'],
      dollBirthday: json['dollBirthday'],
      dollBirthdayTime: json['dollBirthdayTime'],
      dollCharacterDescription: json['dollCharacterDescription'],
      dollLanguageCharacteristic: json['dollLanguageCharacteristic'],
      bloodType: json['bloodType'],
      mbti: json['mbti'],
      voiceId: json['voiceId'],
      voiceType: json['voiceType'],
      avatarUrl: json['avatarUrl'],
      zodiacSign: json['zodiacSign'],
      abilities: json['abilities'] != null
          ? List<String>.from(json['abilities'])
          : null,
      personalityTrait: json['personalityTrait'],
      dialogueExamples: json['dialogueExamples'] != null
          ? Map<String, String>.from(json['dialogueExamples'])
          : null,
    );
  }
}

class VoiceModel {
  String? id;
  String? name;
  String? gender;
  String? description;
  String? previewUrl;
  String? isDefault;
  String? ttsVoice;
  String? voiceType;
  int? sort;

  VoiceModel({
    this.id,
    this.name,
    this.gender,
    this.description,
    this.previewUrl,
    this.isDefault,
    this.ttsVoice,
    this.voiceType,
    this.sort,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'gender': gender,
      'description': description,
      'previewUrl': previewUrl,
      'isDefault': isDefault,
      'ttsVoice': ttsVoice,
      'voiceType': voiceType,
      'sort': sort,
    };
  }

  factory VoiceModel.fromJson(Map<String, dynamic> json) {
    return VoiceModel(
      id: json['id'],
      name: json['name'],
      gender: json['gender'],
      description: json['description'],
      previewUrl: json['previewUrl'],
      isDefault: json['isDefault'],
      ttsVoice: json['ttsVoice'],
      voiceType: json['voiceType'],
      sort: json['sort'],
    );
  }
}

// 邀请请求模型
class InvitationModel {
  String? email;

  InvitationModel({this.email});

  factory InvitationModel.fromJson(Map<String, dynamic> json) {
    return InvitationModel(
      email: json['email'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'email': email,
    };
  }
}

/// 分身使用者基础信息DTO类
class DollUserProfileInfoDTO {
  String? id;
  String? agentId;
  String? dollId;
  String? intimateRelation;
  int? intimacyType;
  String? relationStatus;
  String? interactiveFeatures;
  String? userName;
  String? userNickName;
  String? userGender;
  int? userAge;
  DateTime? userBirthday;
  String? userCity;
  String? userZodiacSign;
  String? userProfession;
  String? userCharacterFeature;
  int? userId;
  String? chatTopics;

  DollUserProfileInfoDTO({
    this.id,
    this.agentId,
    this.dollId,
    this.intimateRelation,
    this.intimacyType,
    this.relationStatus,
    this.interactiveFeatures,
    this.userName,
    this.userNickName,
    this.userGender,
    this.userAge,
    this.userBirthday,
    this.userCity,
    this.userZodiacSign,
    this.userProfession,
    this.userCharacterFeature,
    this.userId,
    this.chatTopics,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'agentId': agentId,
      'dollId': dollId,
      'intimateRelation': intimateRelation,
      'intimacyType': intimacyType,
      'relationStatus': relationStatus,
      'interactiveFeatures': interactiveFeatures,
      'userName': userName,
      'userNickName': userNickName,
      'userGender': userGender,
      'userAge': userAge,
      'userBirthday': userBirthday?.millisecondsSinceEpoch,
      'userCity': userCity,
      'userZodiacSign': userZodiacSign,
      'userProfession': userProfession,
      'userCharacterFeature': userCharacterFeature,
      'userId': userId,
      'chatTopics': chatTopics,
    };
  }

  factory DollUserProfileInfoDTO.fromJson(Map<String, dynamic> json) {
    return DollUserProfileInfoDTO(
      id: json['id'],
      agentId: json['agentId'],
      dollId: json['dollId'],
      intimateRelation: json['intimateRelation'],
      intimacyType: json['intimacyType'],
      relationStatus: json['relationStatus'],
      interactiveFeatures: json['interactiveFeatures'],
      userName: json['userName'],
      userNickName: json['userNickName'],
      userGender: json['userGender'],
      userAge: json['userAge'],
      userBirthday: json['userBirthday'] != null
          ? DateTime.parse(json['userBirthday'])
          : null,
      userCity: json['userCity'],
      userZodiacSign: json['userZodiacSign'],
      userProfession: json['userProfession'],
      userCharacterFeature: json['userCharacterFeature'],
      userId: json['userId'],
      chatTopics: json['chatTopics'],
    );
  }
}




/// 分身使用者信息响应类
class DollUserProfileInfo {
  int? id; // 根据OpenAPI文档，id应该是integer (int64)
  String? agentId;
  String? dollId;
  String? intimateRelation;
  int? intimacyType;
  int? isDefault;
  String? relationStatus;
  String? interactiveFeatures;
  String? userName;
  String? userNickName;
  String? userGender;
  int? userAge; // 根据OpenAPI文档，userAge应该是integer (int32)
  int? userBirthday; // 根据OpenAPI文档，userBirthday应该是integer (int64)
  int? userBirthdayTime; // 根据OpenAPI文档，userBirthdayTime应该是integer (int64)
  String? userCity;
  String? userZodiacSign;
  String? userProfession;
  String? userCharacterFeature;
  String? chatTopics;
  String? createBy;
  String? createTime; // 根据OpenAPI文档，createTime应该是string (date-time)
  String? updateBy;
  String? updateTime; // 根据OpenAPI文档，updateTime应该是string (date-time)
  int? delFlag;
  String? memory; // 添加缺失的memory字段

  DollUserProfileInfo({
    this.id,
    this.agentId,
    this.dollId,
    this.intimateRelation,
    this.intimacyType,
    this.isDefault,
    this.relationStatus,
    this.interactiveFeatures,
    this.userName,
    this.userNickName,
    this.userGender,
    this.userAge,
    this.userBirthday,
    this.userBirthdayTime,
    this.userCity,
    this.userZodiacSign,
    this.userProfession,
    this.userCharacterFeature,
    this.chatTopics,
    this.createBy,
    this.createTime,
    this.updateBy,
    this.updateTime,
    this.delFlag,
    this.memory,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'agentId': agentId,
      'dollId': dollId,
      'intimateRelation': intimateRelation,
      'intimacyType': intimacyType,
      'isDefault': isDefault,
      'relationStatus': relationStatus,
      'interactiveFeatures': interactiveFeatures,
      'userName': userName,
      'userNickName': userNickName,
      'userGender': userGender,
      'userAge': userAge,
      'userBirthday': userBirthday,
      'userBirthdayTime': userBirthdayTime,
      'userCity': userCity,
      'userZodiacSign': userZodiacSign,
      'userProfession': userProfession,
      'userCharacterFeature': userCharacterFeature,
      'chatTopics': chatTopics,
      'createBy': createBy,
      'createTime': createTime,
      'updateBy': updateBy,
      'updateTime': updateTime,
      'delFlag': delFlag,
      'memory': memory,
    };
  }

  factory DollUserProfileInfo.fromJson(Map<String, dynamic> json) {
    return DollUserProfileInfo(
      id: json['id'] as int?,
      agentId: json['agentId'] as String?,
      dollId: json['dollId'] as String?,
      intimateRelation: json['intimateRelation'] as String?,
      intimacyType: json['intimacyType'] as int?,
      isDefault: json['isDefault'] as int?,
      relationStatus: json['relationStatus'] as String?,
      interactiveFeatures: json['interactiveFeatures'] as String?,
      userName: json['userName'] as String?,
      userNickName: json['userNickName'] as String?,
      userGender: json['userGender'] as String?,
      userAge: json['userAge'] as int?,
      userBirthday: json['userBirthday'] as int?,
      userBirthdayTime: json['userBirthdayTime'] as int?,
      userCity: json['userCity'] as String?,
      userZodiacSign: json['userZodiacSign'] as String?,
      userProfession: json['userProfession'] as String?,
      userCharacterFeature: json['userCharacterFeature'] as String?,
      chatTopics: json['chatTopics'] as String?,
      createBy: json['createBy'] as String?,
      createTime: json['createTime'] as String?,
      updateBy: json['updateBy'] as String?,
      updateTime: json['updateTime'] as String?,
      delFlag: json['delFlag'] as int?,
      memory: json['memory'] as String?,
    );
  }
}



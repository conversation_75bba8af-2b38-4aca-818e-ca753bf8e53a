class BaseResponse<T> {
  int? code;
  String? msg;
  T? data;

  BaseResponse({this.code, this.msg, this.data});

  BaseResponse.fromJson(Map<String, dynamic> json, T Function(dynamic) fromJsonT) {
    code = json['code'];
    msg = json['msg'];
    data = json['data'] != null ? fromJsonT(json['data']) : null;
  }

  Map<String, dynamic> toJson(Map<String, dynamic> Function(T) toJsonT) {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['msg'] = msg;
    if (this.data != null) {
      data['data'] = toJsonT(this.data as T);
    }
    return data;
  }

  bool isSuccess() {
    return code == 0 || code == 200;
  }
} 
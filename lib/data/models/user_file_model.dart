/// 文件上传状态
enum FileUploadStatus {
  pending,   // 待上传
  uploading, // 上传中
  uploaded,  // 已上传
  failed,    // 上传失败
}

/// 用户文件模型 - 统一文件数据结构
class UserFile {
  final int? id;
  final String? fileUrl;
  final int? userId;
  final int? createTime;
  final int? updateTime;
  final FileUploadStatus status; // 上传状态
  final int? fileSize; // 文件大小（字节）
  final String? localFilePath; // 本地文件路径（上传时使用）

  UserFile({
    this.id,
    this.fileUrl,
    this.userId,
    this.createTime,
    this.updateTime,
    this.status = FileUploadStatus.uploaded,
    this.fileSize,
    this.localFilePath,
  });

  factory UserFile.fromJson(Map<String, dynamic> json) {
    return UserFile(
      id: json['id'] as int?,
      fileUrl: json['fileUrl'] as String?,
      userId: json['userId'] as int?,
      createTime: json['createTime'] as int?,
      updateTime: json['updateTime'] as int?,
      status: FileUploadStatus.uploaded, // API返回的都是已上传状态
    );
  }

  /// 从本地文件创建 - 用于上传流程
  factory UserFile.fromLocalFile(String filePath, int fileSize) {
    return UserFile(
      localFilePath: filePath,
      fileSize: fileSize,
      status: FileUploadStatus.pending,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'fileUrl': fileUrl,
      'userId': userId,
      'createTime': createTime,
      'updateTime': updateTime,
      'fileSize': fileSize,
    };
  }

  /// 获取文件名
  String get fileName {
    // 优先使用本地文件路径
    if (localFilePath != null && localFilePath!.isNotEmpty) {
      return localFilePath!.split('/').last;
    }
    // 其次从URL中提取
    if (fileUrl != null && fileUrl!.isNotEmpty) {
      final uri = Uri.tryParse(fileUrl!);
      if (uri != null) {
        final segments = uri.pathSegments;
        if (segments.isNotEmpty) return segments.last;
      }
    }
    return '未知文件';
  }

  /// 获取创建时间的DateTime对象
  DateTime? get createDateTime {
    if (createTime == null) return null;
    return DateTime.fromMillisecondsSinceEpoch(createTime! * 1000);
  }

  /// 获取更新时间的DateTime对象
  DateTime? get updateDateTime {
    if (updateTime == null) return null;
    return DateTime.fromMillisecondsSinceEpoch(updateTime! * 1000);
  }

  /// 获取格式化的文件大小
  String get fileSizeText {
    if (fileSize == null) return '';
    final size = fileSize!;
    if (size < 1024) return '${size}B';
    if (size < 1024 * 1024) return '${(size / 1024).toStringAsFixed(1)}KB';
    return '${(size / (1024 * 1024)).toStringAsFixed(1)}MB';
  }

  /// 复制并更新状态
  UserFile copyWith({
    int? id,
    String? fileUrl,
    int? userId,
    int? createTime,
    int? updateTime,
    FileUploadStatus? status,
    int? fileSize,
    String? localFilePath,
  }) {
    return UserFile(
      id: id ?? this.id,
      fileUrl: fileUrl ?? this.fileUrl,
      userId: userId ?? this.userId,
      createTime: createTime ?? this.createTime,
      updateTime: updateTime ?? this.updateTime,
      status: status ?? this.status,
      fileSize: fileSize ?? this.fileSize,
      localFilePath: localFilePath ?? this.localFilePath,
    );
  }
}

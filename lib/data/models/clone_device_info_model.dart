/// 设备分身列表模型 - 对应 /app/getCloneList 接口返回的 ResultListDollProfileInfo 数据

/// 分身设备信息 - 对应 DollProfileInfo 数据结构
class CloneDeviceInfo {
  final int? id;
  final String? agentId;
  final String? dollName;
  final String? dollNickName;
  final String? dollGender;
  final String? dollAge;
  final String? dollBirthday;
  final String? dollBirthdayTime;
  final String? dollCity;
  final String? dollZodiacSign;
  final String? dollProfession;
  final String? dollCharacterDescription;
  final String? dollLanguageCharacteristic;
  final String? createBy;
  final String? createTime;
  final String? updateBy;
  final String? updateTime;
  final String? email;
  final int? userId;
  final String? userName;
  final String? profileBrief;
  final int? delFlag;
  final String? superPower;
  final String? chatTopics;
  final String? exampleConversation1;
  final String? exampleConversation2;
  final String? exampleConversation3;
  final String? exampleConversation4;
  final String? exampleConversation5;

  CloneDeviceInfo({
    this.id,
    this.agentId,
    this.dollName,
    this.dollNickName,
    this.dollGender,
    this.dollAge,
    this.dollBirthday,
    this.dollBirthdayTime,
    this.dollCity,
    this.dollZodiacSign,
    this.dollProfession,
    this.dollCharacterDescription,
    this.dollLanguageCharacteristic,
    this.createBy,
    this.createTime,
    this.updateBy,
    this.updateTime,
    this.email,
    this.userId,
    this.userName,
    this.profileBrief,
    this.delFlag,
    this.superPower,
    this.chatTopics,
    this.exampleConversation1,
    this.exampleConversation2,
    this.exampleConversation3,
    this.exampleConversation4,
    this.exampleConversation5,
  });

  factory CloneDeviceInfo.fromJson(Map<String, dynamic> json) {
    return CloneDeviceInfo(
      id: json['id'] as int?,
      agentId: json['agentId'] as String?,
      dollName: json['dollName'] as String?,
      dollNickName: json['dollNickName'] as String?,
      dollGender: json['dollGender'] as String?,
      dollAge: json['dollAge'] as String?,
      dollBirthday: json['dollBirthday'] as String?,
      dollBirthdayTime: json['dollBirthdayTime'] as String?,
      dollCity: json['dollCity'] as String?,
      dollZodiacSign: json['dollZodiacSign'] as String?,
      dollProfession: json['dollProfession'] as String?,
      dollCharacterDescription: json['dollCharacterDescription'] as String?,
      dollLanguageCharacteristic: json['dollLanguageCharacteristic'] as String?,
      createBy: json['createBy'] as String?,
      createTime: json['createTime'] as String?,
      updateBy: json['updateBy'] as String?,
      updateTime: json['updateTime'] as String?,
      email: json['email'] as String?,
      userId: json['userId'] as int?,
      userName: json['userName'] as String?,
      profileBrief: json['profileBrief'] as String?,
      delFlag: json['delFlag'] as int?,
      superPower: json['superPower'] as String?,
      chatTopics: json['chatTopics'] as String?,
      exampleConversation1: json['example_conversation_1'] as String?,
      exampleConversation2: json['example_conversation_2'] as String?,
      exampleConversation3: json['example_conversation_3'] as String?,
      exampleConversation4: json['example_conversation_4'] as String?,
      exampleConversation5: json['example_conversation_5'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'agentId': agentId,
      'dollName': dollName,
      'dollNickName': dollNickName,
      'dollGender': dollGender,
      'dollAge': dollAge,
      'dollBirthday': dollBirthday,
      'dollBirthdayTime': dollBirthdayTime,
      'dollCity': dollCity,
      'dollZodiacSign': dollZodiacSign,
      'dollProfession': dollProfession,
      'dollCharacterDescription': dollCharacterDescription,
      'dollLanguageCharacteristic': dollLanguageCharacteristic,
      'createBy': createBy,
      'createTime': createTime,
      'updateBy': updateBy,
      'updateTime': updateTime,
      'email': email,
      'userId': userId,
      'userName': userName,
      'profileBrief': profileBrief,
      'delFlag': delFlag,
      'superPower': superPower,
      'chatTopics': chatTopics,
      'example_conversation_1': exampleConversation1,
      'example_conversation_2': exampleConversation2,
      'example_conversation_3': exampleConversation3,
      'example_conversation_4': exampleConversation4,
      'example_conversation_5': exampleConversation5,
    };
  }

  /// 获取显示名称（优先显示昵称，其次姓名）
  String get displayName {
    if (dollNickName?.isNotEmpty == true) {
      return dollNickName!;
    }
    if (dollName?.isNotEmpty == true) {
      return dollName!;
    }
    return '未命名分身';
  }

  /// 获取性别显示文本
  String get genderDisplay {
    return dollGender ?? '未知';
  }

  /// 获取年龄显示文本
  String get ageDisplay {
    if (dollAge?.isNotEmpty == true) {
      return '${dollAge}岁';
    }
    return '年龄未知';
  }

  /// 获取职业显示文本
  String get professionDisplay {
    return dollProfession ?? '职业未知';
  }

  /// 获取城市显示文本
  String get cityDisplay {
    return dollCity ?? '城市未知';
  }

  /// 获取星座显示文本
  String get zodiacDisplay {
    return dollZodiacSign ?? '星座未知';
  }

  /// 是否已删除
  bool get isDeleted {
    return delFlag == 1;
  }

  /// 是否为有效分身
  bool get isValid {
    return !isDeleted && agentId?.isNotEmpty == true;
  }

  /// 获取格式化的创建时间
  String get formattedCreateTime {
    if (createTime?.isNotEmpty == true) {
      try {
        final dateTime = DateTime.parse(createTime!);
        return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')}';
      } catch (e) {
        return createTime!;
      }
    }
    return '创建时间未知';
  }
}
import '../../core/config/app_constants.dart';

/// BluFi设备信息模型
class BluFiDevice {
  final String name;
  final String address;
  final int rssi;
  final bool isConnectable;

  BluFiDevice({
    required this.name,
    required this.address,
    required this.rssi,
    this.isConnectable = true,
  });

  /// 信号强度等级 (1-4)
  int get signalLevel {
    if (rssi >= -50) return 4;
    if (rssi >= -60) return 3;
    if (rssi >= -70) return 2;
    return 1;
  }

  /// 是否为totwoo设备
  bool get isTotwooDevice {
    return name.toUpperCase().startsWith(AppConstants.device_name_prefix);
  }

  @override
  String toString() {
    return 'BluFiDevice(name: $name, address: $address, rssi: $rssi)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is BluFiDevice && other.address == address;
  }

  @override
  int get hashCode => address.hashCode;

  /// 从JSON创建
  factory BluFiDevice.fromJson(Map<String, dynamic> json) {
    return BluFiDevice(
      name: json['name'] ?? '',
      address: json['address'] ?? '',
      rssi: json['rssi'] ?? -100,
      isConnectable: json['isConnectable'] ?? true,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'address': address,
      'rssi': rssi,
      'isConnectable': isConnectable,
    };
  }
}

class MemeryModel {

  MemeryModel({
      this.id, 
      this.agentId, 
      this.dollId, 
      this.intimateRelation, 
      this.intimacyType, 
      this.isDefault, 
      this.relationStatus, 
      this.interactiveFeatures, 
      this.userName, 
      this.userNickName, 
      this.userGender, 
      this.userAge, 
      this.userBirthday, 
      this.userBirthdayTime, 
      this.userCity, 
      this.userZodiacSign, 
      this.userProfession, 
      this.userCharacterFeature, 
      this.chatTopics, 
      this.createBy, 
      this.createTime, 
      this.updateBy, 
      this.updateTime, 
      this.delFlag,});

  MemeryModel.fromJson(dynamic json) {
    id = json['id'];
    agentId = json['agentId'];
    dollId = json['dollId'];
    intimateRelation = json['intimateRelation'];
    intimacyType = json['intimacyType'];
    isDefault = json['isDefault'];
    relationStatus = json['relationStatus'];
    interactiveFeatures = json['interactiveFeatures'];
    userName = json['userName'];
    userNickName = json['userNickName'];
    userGender = json['userGender'];
    userAge = json['userAge'];
    userBirthday = json['userBirthday'];
    userBirthdayTime = json['userBirthdayTime'];
    userCity = json['userCity'];
    userZodiacSign = json['userZodiacSign'];
    userProfession = json['userProfession'];
    userCharacterFeature = json['userCharacterFeature'];
    chatTopics = json['chatTopics'];
    createBy = json['createBy'];
    createTime = json['createTime'];
    updateBy = json['updateBy'];
    updateTime = json['updateTime'];
    delFlag = json['delFlag'];
  }
  String? id;
  String? agentId;
  String? dollId;
  String? intimateRelation;
  num? intimacyType;
  num? isDefault;
  String? relationStatus;
  String? interactiveFeatures;
  String? userName;
  String? userNickName;
  String? userGender;
  String? userAge;
  String? userBirthday;
  String? userBirthdayTime;
  String? userCity;
  String? userZodiacSign;
  String? userProfession;
  String? userCharacterFeature;
  String? chatTopics;
  String? createBy;
  String? createTime;
  String? updateBy;
  String? updateTime;
  num? delFlag;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = id;
    map['agentId'] = agentId;
    map['dollId'] = dollId;
    map['intimateRelation'] = intimateRelation;
    map['intimacyType'] = intimacyType;
    map['isDefault'] = isDefault;
    map['relationStatus'] = relationStatus;
    map['interactiveFeatures'] = interactiveFeatures;
    map['userName'] = userName;
    map['userNickName'] = userNickName;
    map['userGender'] = userGender;
    map['userAge'] = userAge;
    map['userBirthday'] = userBirthday;
    map['userBirthdayTime'] = userBirthdayTime;
    map['userCity'] = userCity;
    map['userZodiacSign'] = userZodiacSign;
    map['userProfession'] = userProfession;
    map['userCharacterFeature'] = userCharacterFeature;
    map['chatTopics'] = chatTopics;
    map['createBy'] = createBy;
    map['createTime'] = createTime;
    map['updateBy'] = updateBy;
    map['updateTime'] = updateTime;
    map['delFlag'] = delFlag;
    return map;
  }

}
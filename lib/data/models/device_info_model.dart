class DeviceInfoModel {
  DeviceInfoModel({
      this.deviceId, 
      this.deviceName, 
      this.userId, 
      this.state, 
      this.deviceType, 
      this.lastLogin, 
      this.wifiName, 
      this.ip, 
      this.chipModelName, 
      this.type, 
      this.version, 
      this.psn, 
      this.dollName, 
      this.network, 
      this.battery, 
      this.volume, 
      this.deviceMode, 
      this.nightLight,});

  DeviceInfoModel.fromJson(dynamic json) {
    deviceId = json['deviceId'];
    deviceName = json['deviceName'];
    userId = json['userId'];
    state = json['state'];
    deviceType = json['deviceType'];
    lastLogin = json['lastLogin'];
    wifiName = json['wifiName'];
    ip = json['ip'];
    chipModelName = json['chipModelName'];
    type = json['type'];
    version = json['version'];
    psn = json['psn'];
    dollName = json['dollName'];
    network = json['network'];
    battery = json['battery'];
    volume = json['volume'];
    deviceMode = json['deviceMode'];
    nightLight = json['nightLight'];
  }
  String? deviceId;
  String? deviceName;
  num? userId;
  String? state;
  num? deviceType;
  String? lastLogin;
  String? wifiName;
  String? ip;
  String? chipModelName;
  String? type;
  String? version;
  String? psn;
  String? dollName;
  num? network;
  num? battery;
  num? volume;
  num? deviceMode;
  num? nightLight;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['deviceId'] = deviceId;
    map['deviceName'] = deviceName;
    map['userId'] = userId;
    map['state'] = state;
    map['deviceType'] = deviceType;
    map['lastLogin'] = lastLogin;
    map['wifiName'] = wifiName;
    map['ip'] = ip;
    map['chipModelName'] = chipModelName;
    map['type'] = type;
    map['version'] = version;
    map['psn'] = psn;
    map['dollName'] = dollName;
    map['network'] = network;
    map['battery'] = battery;
    map['volume'] = volume;
    map['deviceMode'] = deviceMode;
    map['nightLight'] = nightLight;
    return map;
  }

}
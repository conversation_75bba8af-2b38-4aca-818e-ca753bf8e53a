
class CurrentInfoModel {
  final int? userId;        // 用户ID
  final String? deviceId;   // 设备ID，如果为空则需要配网
  final int? agentId;       // 分身ID，如果为0或空则需要创建分身
  final List<int>? fileIds; // 聊天文件ID列表，如果为空则需要上传聊天文件
  final String? cloneVoiceId; // 音色ID，如果为空则需要设置音色
  final List<int>? dollUserId; // 分身用户ID列表
  final List<int>? voicePrintIds; // 声纹ID列表
  final String? dollId;     // 分身dollId

  CurrentInfoModel({
    this.userId,
    this.deviceId,
    this.agentId,
    this.fileIds,
    this.cloneVoiceId,
    this.dollUserId,
    this.voicePrintIds,
    this.dollId,
  });

  /// 从JSON创建CurrentInfoModel
  factory CurrentInfoModel.fromJson(Map<String, dynamic> json) {
    return CurrentInfoModel(
      userId: json['userId'] as int?,
      deviceId: json['deviceId'] as String?,
      agentId: json['agentId'] as int?,
      fileIds: (json['fileIds'] as List<dynamic>?)?.cast<int>(),
      cloneVoiceId: json['cloneVoiceId'] as String?,
      dollUserId: (json['dollUserId'] as List<dynamic>?)?.cast<int>(),
      voicePrintIds: (json['voicePrintIds'] as List<dynamic>?)?.cast<int>(),
      dollId: json['dollId'] as String?,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'deviceId': deviceId,
      'agentId': agentId,
      'fileIds': fileIds,
      'cloneVoiceId': cloneVoiceId,
      'dollUserId': dollUserId,
      'voicePrintIds': voicePrintIds,
      'dollId': dollId,
    };
  }

  /// 业务状态判断方法

  /// 是否需要配网设备（deviceId为空）
  bool get needsDeviceConfiguration {
    return deviceId == null || deviceId!.isEmpty;
  }

  /// 是否需要创建分身（agentId为0或空）
  bool get needsAvatarCreation {
    return agentId == null || agentId == 0;
  }

  /// 是否需要上传聊天文件（fileIds为空）
  bool get needsChatFileUpload {
    return fileIds == null || fileIds!.isEmpty;
  }

  /// 是否需要设置音色（cloneVoiceId为空）
  bool get needsVoiceConfiguration {
    return cloneVoiceId == null || cloneVoiceId!.isEmpty;
  }

  /// 是否需要设置亲密陪伴者（dollUserId为空）
  bool get needsIntimateCompanionSetup {
    return dollUserId == null || dollUserId!.isEmpty;
  }

  /// 是否需要录制声纹（voicePrintIds为空）
  bool get needsVoicePrintRecording {
    return voicePrintIds == null || voicePrintIds!.isEmpty;
  }

  /// 是否可以进入首页（所有步骤都完成）
  bool get canEnterHome {
    return !needsDeviceConfiguration &&
           !needsAvatarCreation &&
           !needsChatFileUpload &&
           !needsVoiceConfiguration &&
           !needsIntimateCompanionSetup &&
           !needsVoicePrintRecording;
  }

  /// 获取当前状态描述（用于调试和日志）
  String get statusDescription {
    if (needsDeviceConfiguration) return '需要配网设备';
    if (needsAvatarCreation) return '需要创建分身';
    if (needsChatFileUpload) return '需要上传聊天文件';
    if (needsVoiceConfiguration) return '需要设置音色';
    if (needsIntimateCompanionSetup) return '需要设置亲密陪伴者';
    if (needsVoicePrintRecording) return '需要录制声纹';
    return '已完成所有步骤，可进入首页';
  }

  /// 创建副本
  CurrentInfoModel copyWith({
    int? userId,
    String? deviceId,
    int? agentId,
    List<int>? fileIds,
    String? cloneVoiceId,
    List<int>? dollUserId,
    List<int>? voicePrintIds,
  }) {
    return CurrentInfoModel(
      userId: userId ?? this.userId,
      deviceId: deviceId ?? this.deviceId,
      agentId: agentId ?? this.agentId,
      fileIds: fileIds ?? this.fileIds,
      cloneVoiceId: cloneVoiceId ?? this.cloneVoiceId,
      dollUserId: dollUserId ?? this.dollUserId,
      voicePrintIds: voicePrintIds ?? this.voicePrintIds,
    );
  }

  @override
  String toString() {
    return 'CurrentInfoModel(userId: $userId, deviceId: $deviceId, agentId: $agentId, fileIds: $fileIds, cloneVoiceId: $cloneVoiceId, dollUserId: $dollUserId, voicePrintIds: $voicePrintIds)';
  }
}

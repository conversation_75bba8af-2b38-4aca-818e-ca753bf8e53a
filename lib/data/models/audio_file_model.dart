import 'dart:io';

/// 音频文件数据模型 - 统一管理本地和远程音频文件
/// 用于简化音色选择中的音频文件管理逻辑
class AudioFileModel {
  /// 文件显示名称
  final String displayName;

  /// 播放路径（本地文件路径或远程URL）
  final String playPath;

  /// 文件类型：local-本地文件, remote-远程文件
  final AudioFileType type;

  /// 本地文件对象（仅当type为local时有值）
  final File? localFile;

  /// 远程文件URL（仅当type为remote时有值）
  final String? remoteUrl;

  /// 是否已上传/可用
  final bool isAvailable;

  const AudioFileModel({
    required this.displayName,
    required this.playPath,
    required this.type,
    this.localFile,
    this.remoteUrl,
    this.isAvailable = true,
  });

  /// 创建本地音频文件模型
  factory AudioFileModel.local({
    required File file,
    String? customName,
  }) {
    final fileName = customName ?? file.path.split('/').last;
    return AudioFileModel(
      displayName: fileName,
      playPath: file.path,
      type: AudioFileType.local,
      localFile: file,
      isAvailable: true,
    );
  }

  /// 创建远程音频文件模型
  factory AudioFileModel.remote({
    required String url,
    String? customName,
  }) {
    final fileName = customName ?? _extractFileNameFromUrl(url);
    return AudioFileModel(
      displayName: fileName,
      playPath: url,
      type: AudioFileType.remote,
      remoteUrl: url,
      isAvailable: true,
    );
  }

  /// 创建空的音频文件模型
  factory AudioFileModel.empty() {
    return const AudioFileModel(
      displayName: '',
      playPath: '',
      type: AudioFileType.local,
      isAvailable: false,
    );
  }

  /// 是否为空
  bool get isEmpty => !isAvailable || playPath.isEmpty;

  /// 是否为本地文件
  bool get isLocal => type == AudioFileType.local;

  /// 是否为远程文件
  bool get isRemote => type == AudioFileType.remote;

  /// 获取用于API上传的文件对象
  File? get uploadFile => localFile;

  /// 从URL提取文件名
  static String _extractFileNameFromUrl(String url) {
    try {
      final uri = Uri.parse(url);
      final fileName = uri.pathSegments.last;
      // 处理类似 "014050bd1a8d48419ea6190b378021c3_2.mp3" 的文件名
      if (fileName.contains('_')) {
        final parts = fileName.split('_');
        if (parts.length > 1) {
          return parts.last; // 返回 "2.mp3"
        }
      }
      return fileName;
    } catch (e) {
      return '音频文件';
    }
  }

  /// 复制并更新属性
  AudioFileModel copyWith({
    String? displayName,
    String? playPath,
    AudioFileType? type,
    File? localFile,
    String? remoteUrl,
    bool? isAvailable,
  }) {
    return AudioFileModel(
      displayName: displayName ?? this.displayName,
      playPath: playPath ?? this.playPath,
      type: type ?? this.type,
      localFile: localFile ?? this.localFile,
      remoteUrl: remoteUrl ?? this.remoteUrl,
      isAvailable: isAvailable ?? this.isAvailable,
    );
  }

  @override
  String toString() {
    return 'AudioFileModel(displayName: $displayName, type: $type, isAvailable: $isAvailable)';
  }
}

/// 音频文件类型枚举
enum AudioFileType {
  /// 本地文件
  local,

  /// 远程文件
  remote,
}

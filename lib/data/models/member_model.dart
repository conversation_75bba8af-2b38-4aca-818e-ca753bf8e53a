class MemberModel {
  MemberModel({
      this.id, 
      this.agentId, 
      this.dollId,
      this.avatar,
      this.email,
      this.intimateRelation, 
      this.intimacyType, 
      this.isDefault, 
      this.relationStatus, 
      this.interactiveFeatures, 
      this.userName, 
      this.userNickName, 
      this.userGender, 
      this.userAge, 
      this.userBirthday, 
      this.userBirthdayTime, 
      this.userCity, 
      this.userZodiacSign, 
      this.userProfession, 
      this.userCharacterFeature, 
      this.chatTopics, 
      this.createBy, 
      this.createTime, 
      this.updateBy, 
      this.updateTime, 
      this.delFlag,
      this.memory,});

  MemberModel.fromJson(Map<String, dynamic> json) {
    id = json['id'] as int?;
    agentId = json['agentId'] as String?;
    dollId = json['dollId'] as String?;
    avatar = json['avatar'] as String? ?? '';
    email = json['email'] as String? ?? '';
    intimateRelation = json['intimateRelation'] as String?;
    intimacyType = json['intimacyType'] as int?;
    isDefault = json['isDefault'] as int?;
    relationStatus = json['relationStatus'] as String?;
    interactiveFeatures = json['interactiveFeatures'] as String?;
    userName = json['userName'] as String?;
    userNickName = json['userNickName'] as String?;
    userGender = json['userGender'] as String?;
    userAge = json['userAge'] as int?;
    userBirthday = json['userBirthday'] as int?;
    userBirthdayTime = json['userBirthdayTime'] as int?;
    userCity = json['userCity'] as String?;
    userZodiacSign = json['userZodiacSign'] as String?;
    userProfession = json['userProfession'] as String?;
    userCharacterFeature = json['userCharacterFeature'] as String?;
    chatTopics = json['chatTopics'] as String?;
    createBy = json['createBy'] as String?;
    createTime = json['createTime'] as String?;
    updateBy = json['updateBy'] as String?;
    updateTime = json['updateTime'] as String?;
    delFlag = json['delFlag'] as int?;
    memory = json['memory'] as String?;
  }
  int? id;
  String? agentId;
  String? dollId;
  String? avatar;
  String? email;
  String? intimateRelation;
  int? intimacyType;
  int? isDefault;
  String? relationStatus;
  String? interactiveFeatures;
  String? userName;
  String? userNickName;
  String? userGender;
  int? userAge;
  int? userBirthday;
  int? userBirthdayTime;
  String? userCity;
  String? userZodiacSign;
  String? userProfession;
  String? userCharacterFeature;
  String? chatTopics;
  String? createBy;
  String? createTime;
  String? updateBy;
  String? updateTime;
  int? delFlag;
  String? memory;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['id'] = id;
    map['agentId'] = agentId;
    map['dollId'] = dollId;
    map['avatar'] = avatar;
    map['email'] = email;
    map['intimateRelation'] = intimateRelation;
    map['intimacyType'] = intimacyType;
    map['isDefault'] = isDefault;
    map['relationStatus'] = relationStatus;
    map['interactiveFeatures'] = interactiveFeatures;
    map['userName'] = userName;
    map['userNickName'] = userNickName;
    map['userGender'] = userGender;
    map['userAge'] = userAge;
    map['userBirthday'] = userBirthday;
    map['userBirthdayTime'] = userBirthdayTime;
    map['userCity'] = userCity;
    map['userZodiacSign'] = userZodiacSign;
    map['userProfession'] = userProfession;
    map['userCharacterFeature'] = userCharacterFeature;
    map['chatTopics'] = chatTopics;
    map['createBy'] = createBy;
    map['createTime'] = createTime;
    map['updateBy'] = updateBy;
    map['updateTime'] = updateTime;
    map['delFlag'] = delFlag;
    map['memory'] = memory;
    return map;
  }

}
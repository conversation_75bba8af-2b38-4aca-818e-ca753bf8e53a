import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:record/record.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:path_provider/path_provider.dart';

enum CloneRecordingStatus {
  idle, // 空闲状态
  recording, // 录音中
  cancelling, // 取消录音
  verifying, // 校验中
  completed, // 完成录制
}

class VoiceCloneRecorder extends StatefulWidget {
  final Function(File audioFile) onRecordComplete;
  final Function(CloneRecordingStatus status) onStatusChanged;
  final Color primaryColor;
  final Color cancelColor;
  final String buttonText;
  final String recordingText;
  final String cancelText;
  final String verifyingText;
  final int minDuration; // 最小录音时长(秒)
  final int maxDuration; // 最大录音时长(秒)

  const VoiceCloneRecorder({
    Key? key,
    required this.onRecordComplete,
    required this.onStatusChanged,
    this.primaryColor = const Color(0xFF3F51B5),
    this.cancelColor = Colors.red,
    this.buttonText = '按住朗读',
    this.recordingText = '松手发送，上移取消',
    this.cancelText = '取消录音',
    this.verifyingText = '音频校验中...',
    this.minDuration = 3,
    this.maxDuration = 60,
  }) : super(key: key);

  @override
  State<VoiceCloneRecorder> createState() => _VoiceCloneRecorderState();
}

class _VoiceCloneRecorderState extends State<VoiceCloneRecorder>
    with SingleTickerProviderStateMixin {
  final AudioRecorder _recorder = AudioRecorder();

  CloneRecordingStatus _status = CloneRecordingStatus.idle;
  double _startPositionY = 0;
  double _currentPositionY = 0;
  String? _currentRecordPath;

  // 音量大小控制
  int _volumeLevel = 0;
  final int _maxVolumeLevel = 7;

  // 录制时长(秒)
  int _recordDuration = 0;
  Timer? _recordTimer;
  Timer? _amplitudeTimer;

  // 动画控制器
  late AnimationController _animationController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _verifyingAnimation;

  @override
  void initState() {
    super.initState();
    _initAnimations();
  }

  @override
  void dispose() {
    _stopRecording();
    _stopTimers();
    _animationController.dispose();
    _recorder.dispose();
    super.dispose();
  }

  // 初始化动画
  void _initAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    // 脉动动画 (录音按钮大小变化)
    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.1).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );

    // 验证中旋转动画
    _verifyingAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.linear,
      ),
    );

    _animationController.repeat(reverse: true);
  }

  // 开始录音
  Future<void> _startRecording() async {
    // 检查并请求麦克风权限
    var status = await Permission.microphone.request();
    if (status != PermissionStatus.granted) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('需要麦克风权限才能录音')),
        );
      }
      return;
    }

    setState(() {
      _status = CloneRecordingStatus.recording;
      _recordDuration = 0;
      _volumeLevel = 0;
    });

    widget.onStatusChanged(_status);

    // 准备录音文件路径
    final dir = await getTemporaryDirectory();
    _currentRecordPath =
        '${dir.path}/voice_clone_${DateTime.now().millisecondsSinceEpoch}.m4a';

    // 开始录音
    await _recorder.start(
      RecordConfig(
        encoder: AudioEncoder.aacLc,
        bitRate: 128000,
        sampleRate: 44100,
      ),
      path: _currentRecordPath!,
    );

    // 开始计时
    _startTimers();
  }

  // 停止录音
  Future<void> _stopRecording({bool cancel = false}) async {
    _stopTimers();

    // 如果取消录音
    if (cancel || _status == CloneRecordingStatus.cancelling) {
      setState(() {
        _status = CloneRecordingStatus.idle;
      });

      if (await _recorder.isRecording()) {
        await _recorder.stop();
      }

      widget.onStatusChanged(_status);
      return;
    }

    // 如果录音时间太短
    if (_recordDuration < widget.minDuration) {
      setState(() {
        _status = CloneRecordingStatus.idle;
      });

      if (await _recorder.isRecording()) {
        await _recorder.stop();
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('请至少录制 ${widget.minDuration} 秒')),
        );
      }
      widget.onStatusChanged(_status);
      return;
    }

    // 停止录音
    String? path;
    if (await _recorder.isRecording()) {
      path = await _recorder.stop();
    }

    if (path != null) {
      _currentRecordPath = path;
      _startVerifying();
    } else {
      setState(() {
        _status = CloneRecordingStatus.idle;
      });
      widget.onStatusChanged(_status);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('录音失败，请重试')),
        );
      }
    }
  }

  // 开始录音校验
  void _startVerifying() {
    setState(() {
      _status = CloneRecordingStatus.verifying;
    });
    widget.onStatusChanged(_status);

    // 模拟校验过程，实际项目中可能需要进行网络请求或者本地音频质量检测
    Future.delayed(const Duration(seconds: 2), () {
      if (_currentRecordPath != null) {
        setState(() {
          _status = CloneRecordingStatus.completed;
        });
        widget.onStatusChanged(_status);

        // 调用回调函数，传递录音文件
        widget.onRecordComplete(File(_currentRecordPath!));

        // 重置状态
        Future.delayed(const Duration(milliseconds: 500), () {
          setState(() {
            _status = CloneRecordingStatus.idle;
          });
          widget.onStatusChanged(_status);
        });
      } else {
        setState(() {
          _status = CloneRecordingStatus.idle;
        });
        widget.onStatusChanged(_status);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('录音失败，请重试')),
          );
        }
      }
    });
  }

  // 开始计时器
  void _startTimers() {
    _stopTimers();

    // 录音时长计时器
    _recordTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _recordDuration++;
      });

      // 如果超过最大录音时长，自动停止
      if (_recordDuration >= widget.maxDuration) {
        _stopRecording();
      }
    });

    // 音量监测计时器
    _amplitudeTimer =
        Timer.periodic(const Duration(milliseconds: 200), (timer) async {
      final amplitude = await _recorder.getAmplitude();
      final volume = amplitude.current / 100; // 归一化到0-1范围

      // 将音量值（0-1）映射到音量级别（0-7）
      int level = (volume * _maxVolumeLevel).round();
      if (level > _maxVolumeLevel) level = _maxVolumeLevel;

      if (mounted) {
        setState(() {
          _volumeLevel = level;
        });
      }
    });
  }

  // 停止计时器
  void _stopTimers() {
    _recordTimer?.cancel();
    _recordTimer = null;
    _amplitudeTimer?.cancel();
    _amplitudeTimer = null;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // 录音状态UI
        _buildStatusView(),

        const SizedBox(height: 16),

        // 录音按钮
        GestureDetector(
          onLongPressStart: _status == CloneRecordingStatus.idle
              ? (details) {
                  _startPositionY = details.globalPosition.dy;
                  _currentPositionY = details.globalPosition.dy;
                  _startRecording();
                }
              : null,
          onLongPressMoveUpdate: _status == CloneRecordingStatus.recording
              ? (details) {
                  _currentPositionY = details.globalPosition.dy;

                  // 如果上滑超过50像素，则进入取消模式
                  if (_startPositionY - _currentPositionY > 50) {
                    if (_status != CloneRecordingStatus.cancelling) {
                      setState(() {
                        _status = CloneRecordingStatus.cancelling;
                      });
                      widget.onStatusChanged(_status);
                    }
                  } else if (_status == CloneRecordingStatus.cancelling) {
                    setState(() {
                      _status = CloneRecordingStatus.recording;
                    });
                    widget.onStatusChanged(_status);
                  }
                }
              : null,
          onLongPressEnd: (_status == CloneRecordingStatus.recording ||
                  _status == CloneRecordingStatus.cancelling)
              ? (details) {
                  _stopRecording(
                      cancel: _status == CloneRecordingStatus.cancelling);
                }
              : null,
          onLongPressCancel: (_status == CloneRecordingStatus.recording ||
                  _status == CloneRecordingStatus.cancelling)
              ? () {
                  _stopRecording(cancel: true);
                }
              : null,
          child: AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              // 不同状态下按钮样式不同
              return Transform.scale(
                scale: _status == CloneRecordingStatus.recording &&
                        _status != CloneRecordingStatus.cancelling
                    ? _pulseAnimation.value
                    : 1.0,
                child: Container(
                  // 使用固定尺寸并添加约束确保尺寸有效
                  width: 120,
                  height: 120,
                  constraints: const BoxConstraints(
                    minWidth: 80,
                    minHeight: 80,
                    maxWidth: 120,
                    maxHeight: 120,
                  ),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: _getButtonColor(),
                    boxShadow: [
                      BoxShadow(
                        color: _getButtonColor().withOpacity(0.3),
                        blurRadius: 10,
                        spreadRadius: 2,
                      ),
                    ],
                  ),
                  child: _buildButtonContent(),
                ),
              );
            },
          ),
        ),

        const SizedBox(height: 8),

        // 提示文字
        Text(
          _getStatusText(),
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: _status == CloneRecordingStatus.cancelling
                ? widget.cancelColor
                : Colors.grey[700],
          ),
        ),
      ],
    );
  }

  // 根据不同状态构建按钮内容
  Widget _buildButtonContent() {
    switch (_status) {
      case CloneRecordingStatus.idle:
        return Icon(
          Icons.mic,
          color: Colors.white,
          size: 40,
        );

      case CloneRecordingStatus.recording:
      case CloneRecordingStatus.cancelling:
        return Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              _status == CloneRecordingStatus.cancelling
                  ? Icons.close
                  : Icons.mic,
              color: Colors.white,
              size: 40,
            ),
            const SizedBox(height: 4),
            Text(
              '$_recordDuration"',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
          ],
        );

      case CloneRecordingStatus.verifying:
        return AnimatedBuilder(
          animation: _verifyingAnimation,
          builder: (context, child) {
            return Transform.rotate(
              angle: _verifyingAnimation.value * 2 * 3.14159,
              child: const Icon(
                Icons.refresh,
                color: Colors.white,
                size: 40,
              ),
            );
          },
        );

      case CloneRecordingStatus.completed:
        return const Icon(
          Icons.check,
          color: Colors.white,
          size: 40,
        );
    }
  }

  // 构建状态视图
  Widget _buildStatusView() {
    // 只有在录音时才显示波形图
    if (_status == CloneRecordingStatus.recording) {
      return _buildVoiceWaveform();
    }

    return const SizedBox(height: 40);
  }

  // 构建声音波形图
  Widget _buildVoiceWaveform() {
    return SizedBox(
      height: 40,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: List.generate(9, (index) {
          // 计算波形柱高度，中间最高，两边降低
          double maxHeight = 30.0;
          double heightFactor;

          // 按照音量级别和位置计算高度
          if (index == 4) {
            // 中间柱
            heightFactor = 0.3 + (_volumeLevel / _maxVolumeLevel) * 0.7;
          } else if (index == 3 || index == 5) {
            // 中间旁边柱
            heightFactor = 0.25 + (_volumeLevel / _maxVolumeLevel) * 0.65;
          } else if (index == 2 || index == 6) {
            heightFactor = 0.2 + (_volumeLevel / _maxVolumeLevel) * 0.6;
          } else if (index == 1 || index == 7) {
            heightFactor = 0.15 + (_volumeLevel / _maxVolumeLevel) * 0.55;
          } else {
            // 两边柱
            heightFactor = 0.1 + (_volumeLevel / _maxVolumeLevel) * 0.5;
          }

          return Container(
            width: 4,
            height: maxHeight * heightFactor,
            margin: const EdgeInsets.symmetric(horizontal: 3),
            decoration: BoxDecoration(
              color: widget.primaryColor,
              borderRadius: BorderRadius.circular(2),
            ),
          );
        }),
      ),
    );
  }

  // 获取按钮颜色
  Color _getButtonColor() {
    switch (_status) {
      case CloneRecordingStatus.idle:
        return widget.primaryColor;
      case CloneRecordingStatus.recording:
        return widget.primaryColor;
      case CloneRecordingStatus.cancelling:
        return widget.cancelColor;
      case CloneRecordingStatus.verifying:
        return Colors.orange;
      case CloneRecordingStatus.completed:
        return Colors.green;
    }
  }

  // 获取状态文本
  String _getStatusText() {
    switch (_status) {
      case CloneRecordingStatus.idle:
        return widget.buttonText;
      case CloneRecordingStatus.recording:
        return widget.recordingText;
      case CloneRecordingStatus.cancelling:
        return widget.cancelText;
      case CloneRecordingStatus.verifying:
        return widget.verifyingText;
      case CloneRecordingStatus.completed:
        return '录制完成';
    }
  }
}

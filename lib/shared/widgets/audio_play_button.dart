import 'package:flutter/material.dart';
import 'dart:async';
import 'package:just_audio/just_audio.dart';

class AudioPlayButton extends StatefulWidget {
  final String audioUrl;
  final String? title;
  final String? description;
  final Color activeColor;
  final Function(bool isPlaying)? onPlayStateChanged;

  const AudioPlayButton({
    Key? key,
    required this.audioUrl,
    this.title,
    this.description,
    this.activeColor = const Color(0xFF3F51B5),
    this.onPlayStateChanged,
  }) : super(key: key);

  @override
  State<AudioPlayButton> createState() => _AudioPlayButtonState();
}

class _AudioPlayButtonState extends State<AudioPlayButton>
    with SingleTickerProviderStateMixin {
  final AudioPlayer _audioPlayer = AudioPlayer();
  bool _isPlaying = false;
  double _progress = 0.0;
  String _position = '0:00';
  String _duration = '0:00';
  Timer? _progressTimer;

  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 400),
    );

    _animation =
        Tween<double>(begin: 0.0, end: 1.0).animate(_animationController);

    _audioPlayer.playerStateStream.listen((state) {
      if (mounted) {
        setState(() {
          _isPlaying = state.playing;
          if (state.processingState == ProcessingState.completed) {
            _isPlaying = false;
            _progress = 0.0;
            _position = '0:00';
            _stopProgressTimer();
          }

          // 通知父组件播放状态变化
          widget.onPlayStateChanged?.call(_isPlaying);
        });
      }
    });

    // 获取音频时长
    _audioPlayer.setUrl(widget.audioUrl).then((_) {
      if (mounted) {
        _audioPlayer.durationStream.listen((duration) {
          if (mounted && duration != null) {
            setState(() {
              _duration = _formatDuration(duration);
            });
          }
        });
      }
    });
  }

  @override
  void dispose() {
    _stopProgressTimer();
    _audioPlayer.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _startProgressTimer() {
    _progressTimer?.cancel();
    _progressTimer =
        Timer.periodic(const Duration(milliseconds: 50), (timer) async {
      if (mounted && _isPlaying) {
        final position = await _audioPlayer.position;
        final duration = await _audioPlayer.duration ?? Duration.zero;

        if (mounted) {
          setState(() {
            _position = _formatDuration(position);
            _progress = duration.inMilliseconds > 0
                ? position.inMilliseconds / duration.inMilliseconds
                : 0.0;
          });
        }
      }
    });
  }

  void _stopProgressTimer() {
    _progressTimer?.cancel();
    _progressTimer = null;
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '$minutes:${seconds.toString().padLeft(2, '0')}';
  }

  Future<void> _togglePlay() async {
    try {
      if (_isPlaying) {
        await _audioPlayer.pause();
        _stopProgressTimer();
        _animationController.reverse();
      } else {
        await _audioPlayer.setUrl(widget.audioUrl);
        await _audioPlayer.play();
        _startProgressTimer();
        _animationController.forward();
      }
    } catch (e) {
      print('Error playing audio: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 12),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(30),
        border: Border.all(
          color: _isPlaying ? widget.activeColor : Colors.grey[300]!,
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 播放按钮
          GestureDetector(
            onTap: _togglePlay,
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              width: 36,
              height: 36,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: _isPlaying ? widget.activeColor : Colors.white,
                border: Border.all(
                  color: _isPlaying ? widget.activeColor : Colors.grey[300]!,
                ),
                boxShadow: _isPlaying
                    ? [
                        BoxShadow(
                          color: widget.activeColor.withOpacity(0.3),
                          blurRadius: 8,
                          spreadRadius: 1,
                        )
                      ]
                    : null,
              ),
              child: Center(
                child: Icon(
                  _isPlaying ? Icons.pause : Icons.play_arrow,
                  color: _isPlaying ? Colors.white : widget.activeColor,
                  size: 20,
                ),
              ),
            ),
          ),

          const SizedBox(width: 8),

          // 进度信息
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              if (widget.title != null)
                Text(
                  widget.title!,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: _isPlaying ? widget.activeColor : Colors.black87,
                  ),
                ),
              SizedBox(
                width: 120,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 进度条
                    ClipRRect(
                      borderRadius: BorderRadius.circular(2),
                      child: LinearProgressIndicator(
                        value: _progress,
                        backgroundColor: Colors.grey[300],
                        valueColor:
                            AlwaysStoppedAnimation<Color>(widget.activeColor),
                        minHeight: 4,
                      ),
                    ),

                    const SizedBox(height: 4),

                    // 时间显示
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          _position,
                          style: TextStyle(
                            fontSize: 10,
                            color: Colors.grey[600],
                          ),
                        ),
                        Text(
                          _duration,
                          style: TextStyle(
                            fontSize: 10,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'dart:async';

class VoiceRecordButton extends StatefulWidget {
  final Function(String? audioFilePath) onRecordComplete;
  final Function() onRecordStart;
  final Function() onRecordCancel;
  final String holdText;
  final String cancelText;
  final Color primaryColor;
  final Color cancelColor;

  const VoiceRecordButton({
    Key? key,
    required this.onRecordComplete,
    required this.onRecordStart,
    required this.onRecordCancel,
    this.holdText = '按住说话',
    this.cancelText = '上滑取消发送',
    this.primaryColor = const Color(0xFF3F51B5),
    this.cancelColor = Colors.red,
  }) : super(key: key);

  @override
  State<VoiceRecordButton> createState() => _VoiceRecordButtonState();
}

class _VoiceRecordButtonState extends State<VoiceRecordButton>
    with SingleTickerProviderStateMixin {
  bool _isRecording = false;
  bool _isCancelling = false;
  double _cancelY = 0;
  final double _cancelThreshold = -50.0;

  // 录音动画控制器
  late AnimationController _animationController;
  late Animation<double> _animation;

  Timer? _recordTimer;
  int _recordDuration = 0;
  List<int> _amplitudeList = [];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    )..repeat(reverse: true);

    _animation =
        Tween<double>(begin: 1.0, end: 1.2).animate(_animationController);
  }

  @override
  void dispose() {
    _animationController.dispose();
    _recordTimer?.cancel();
    super.dispose();
  }

  void _startRecording() {
    setState(() {
      _isRecording = true;
      _isCancelling = false;
      _recordDuration = 0;
      _amplitudeList = [];
    });

    // 开始录音的实际逻辑
    widget.onRecordStart();

    // 开始计时
    _recordTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _recordDuration++;

        // 模拟音量振幅
        _amplitudeList.add(50 + (DateTime.now().millisecondsSinceEpoch % 50));
        if (_amplitudeList.length > 20) {
          _amplitudeList.removeAt(0);
        }
      });
    });
  }

  void _stopRecording() {
    if (!_isRecording) return;

    _animationController.stop();
    _recordTimer?.cancel();

    if (_isCancelling) {
      widget.onRecordCancel();
    } else {
      // 获取录音文件路径并传递给回调
      // 这里用null代替，实际使用时需要传递真实文件路径
      widget.onRecordComplete('audio_file_path');
    }

    setState(() {
      _isRecording = false;
      _isCancelling = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (_isRecording)
          Container(
            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
            decoration: BoxDecoration(
              color: Colors.black54,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              children: [
                Text(
                  _isCancelling ? widget.cancelText : '录音中 ${_recordDuration}秒',
                  style: TextStyle(
                    color: _isCancelling ? widget.cancelColor : Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                _buildVoiceWave(),
              ],
            ),
          ),
        const SizedBox(height: 20),
        GestureDetector(
          onLongPressStart: (details) {
            _startRecording();
          },
          onLongPressMoveUpdate: (details) {
            final dy = details.localPosition.dy;
            // 如果向上移动超过阈值，则标记为取消
            if (dy < _cancelThreshold && !_isCancelling) {
              setState(() {
                _isCancelling = true;
              });
            } else if (dy >= _cancelThreshold && _isCancelling) {
              setState(() {
                _isCancelling = false;
              });
            }

            setState(() {
              _cancelY = dy;
            });
          },
          onLongPressEnd: (details) {
            _stopRecording();
          },
          onLongPressCancel: () {
            _stopRecording();
          },
          child: AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              return Transform.scale(
                scale: _isRecording && !_isCancelling ? _animation.value : 1.0,
                child: Container(
                  width: 64,
                  height: 64,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: _isRecording
                        ? (_isCancelling
                            ? widget.cancelColor
                            : widget.primaryColor)
                        : Colors.white,
                    border: Border.all(
                      color: _isRecording
                          ? (_isCancelling
                              ? widget.cancelColor
                              : widget.primaryColor)
                          : widget.primaryColor,
                      width: 2,
                    ),
                    boxShadow: _isRecording
                        ? [
                            BoxShadow(
                              color: (_isCancelling
                                      ? widget.cancelColor
                                      : widget.primaryColor)
                                  .withOpacity(0.3),
                              blurRadius: 10,
                              spreadRadius: 3,
                            )
                          ]
                        : null,
                  ),
                  child: Icon(
                    _isRecording
                        ? (_isCancelling ? Icons.close : Icons.mic)
                        : Icons.mic,
                    color: _isRecording ? Colors.white : widget.primaryColor,
                    size: 32,
                  ),
                ),
              );
            },
          ),
        ),
        const SizedBox(height: 8),
        Text(
          _isRecording ? (_isCancelling ? '松开手指取消' : '松开发送') : widget.holdText,
          style: TextStyle(
            fontSize: 14,
            color: _isRecording
                ? (_isCancelling ? widget.cancelColor : Colors.grey[700])
                : Colors.grey[700],
          ),
        ),
      ],
    );
  }

  // 构建声波可视化
  Widget _buildVoiceWave() {
    return Container(
      height: 40,
      width: 160,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: List.generate(10, (index) {
          // 如果没有足够的振幅数据，使用随机值
          double amplitude = _amplitudeList.isEmpty
              ? 5 + (index % 3) * 5.0
              : (5 +
                  _amplitudeList[_amplitudeList.length -
                          1 -
                          (index % _amplitudeList.length)] /
                      10);

          return AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            width: 4,
            height: _isCancelling ? 5 : amplitude,
            decoration: BoxDecoration(
              color: _isCancelling ? widget.cancelColor : Colors.white,
              borderRadius: BorderRadius.circular(2),
            ),
          );
        }),
      ),
    );
  }
}

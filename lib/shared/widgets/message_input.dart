import 'package:flutter/material.dart';
import 'voice_record_button.dart';

class MessageInputBar extends StatefulWidget {
  final Function(String text) onSendText;
  final Function(String audioPath) onSendVoice;
  final String hintText;
  final Color primaryColor;

  const MessageInputBar({
    Key? key,
    required this.onSendText,
    required this.onSendVoice,
    this.hintText = '输入消息...',
    this.primaryColor = const Color(0xFF3F51B5),
  }) : super(key: key);

  @override
  State<MessageInputBar> createState() => _MessageInputBarState();
}

class _MessageInputBarState extends State<MessageInputBar> {
  final TextEditingController _textController = TextEditingController();
  bool _isVoiceMode = false;
  bool _isRecording = false;

  @override
  void dispose() {
    _textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, -3),
          ),
        ],
      ),
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (_isVoiceMode && !_isRecording)
            _buildVoiceButton()
          else if (_isRecording)
            _buildRecordingView()
          else
            _buildTextInput(),
        ],
      ),
    );
  }

  Widget _buildTextInput() {
    return Row(
      children: [
        // 语音/键盘切换按钮
        IconButton(
          icon: Icon(
            _isVoiceMode ? Icons.keyboard : Icons.mic,
            color: Colors.grey[700],
          ),
          onPressed: () {
            setState(() {
              _isVoiceMode = !_isVoiceMode;
            });
          },
        ),

        // 文本输入框
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(20),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
            child: TextField(
              controller: _textController,
              decoration: InputDecoration(
                hintText: widget.hintText,
                hintStyle: TextStyle(color: Colors.grey[500]),
                border: InputBorder.none,
              ),
              maxLines: 5,
              minLines: 1,
            ),
          ),
        ),

        // 发送按钮
        IconButton(
          icon: Icon(
            Icons.send,
            color: _textController.text.isEmpty
                ? Colors.grey[400]
                : widget.primaryColor,
          ),
          onPressed: () {
            if (_textController.text.isNotEmpty) {
              widget.onSendText(_textController.text);
              _textController.clear();
            }
          },
        ),
      ],
    );
  }

  Widget _buildVoiceButton() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: VoiceRecordButton(
        onRecordStart: () {
          setState(() {
            _isRecording = true;
          });
          // 这里实际项目中应该调用录音服务开始录音
          // 例如使用 flutter_sound 等库
        },
        onRecordComplete: (audioPath) {
          setState(() {
            _isRecording = false;
          });
          if (audioPath != null) {
            widget.onSendVoice(audioPath);
          }
        },
        onRecordCancel: () {
          setState(() {
            _isRecording = false;
          });
          // 取消录音逻辑
        },
        primaryColor: widget.primaryColor,
      ),
    );
  }

  Widget _buildRecordingView() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: Text(
        '正在录音...',
        style: TextStyle(
          color: Colors.grey[600],
          fontSize: 16,
        ),
      ),
    );
  }
}

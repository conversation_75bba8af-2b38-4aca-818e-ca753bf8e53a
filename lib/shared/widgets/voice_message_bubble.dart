import 'package:flutter/material.dart';
import 'dart:async';
import 'package:just_audio/just_audio.dart';
import '../../core/utils/logger.dart';

class VoiceMessageBubble extends StatefulWidget {
  final String audioUrl;
  final int durationInSeconds;
  final bool isMe;
  final DateTime timestamp;
  final Color myBubbleColor;
  final Color otherBubbleColor;

  const VoiceMessageBubble({
    Key? key,
    required this.audioUrl,
    required this.durationInSeconds,
    required this.isMe,
    required this.timestamp,
    this.myBubbleColor = const Color(0xFF3F51B5),
    this.otherBubbleColor = const Color(0xFFEEEEEE),
  }) : super(key: key);

  @override
  State<VoiceMessageBubble> createState() => _VoiceMessageBubbleState();
}

class _VoiceMessageBubbleState extends State<VoiceMessageBubble> {
  late final AudioPlayer _audioPlayer;
  bool _isPlaying = false;
  Duration _position = Duration.zero;
  Timer? _progressTimer;

  @override
  void initState() {
    super.initState();
    _audioPlayer = AudioPlayer();
    _initAudioPlayer();
  }

  void _initAudioPlayer() {
    _audioPlayer.playerStateStream.listen((state) {
      if (mounted) {
        setState(() {
          _isPlaying = state.playing;
          if (state.processingState == ProcessingState.completed) {
            _position = Duration.zero;
            _isPlaying = false;
            _stopProgressTimer();
          }
        });
      }
    });
  }

  void _startProgressTimer() {
    _progressTimer?.cancel();
    _progressTimer =
        Timer.periodic(const Duration(milliseconds: 100), (timer) async {
      if (mounted && _isPlaying) {
        final position = await _audioPlayer.position;
        if (mounted) {
          setState(() {
            _position = position;
          });
        }
      }
    });
  }

  void _stopProgressTimer() {
    _progressTimer?.cancel();
    _progressTimer = null;
  }

  Future<void> _togglePlay() async {
    try {
      if (_isPlaying) {
        await _audioPlayer.pause();
        _stopProgressTimer();
      } else {
        if (_position == Duration.zero) {
          await _audioPlayer.setUrl(widget.audioUrl);
        }
        await _audioPlayer.play();
        _startProgressTimer();
      }
    } catch (e) {
      AppLogger.error('播放语音消息失败', e);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('播放失败: ${e.toString()}')),
        );
      }
    }
  }

  @override
  void dispose() {
    _stopProgressTimer();
    _audioPlayer.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final bubbleColor =
        widget.isMe ? widget.myBubbleColor : widget.otherBubbleColor;
    final textColor = widget.isMe ? Colors.white : Colors.black87;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 16),
      child: Row(
        mainAxisAlignment:
            widget.isMe ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          if (!widget.isMe) _buildAvatar(),
          Flexible(
            child: Container(
              constraints: BoxConstraints(
                maxWidth: MediaQuery.of(context).size.width * 0.7,
              ),
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: bubbleColor,
                borderRadius: BorderRadius.circular(16),
              ),
              child: IntrinsicWidth(
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // 播放按钮
                    IconButton(
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                      icon: Icon(
                        _isPlaying
                            ? Icons.pause_circle_filled
                            : Icons.play_circle_filled,
                        color: textColor,
                        size: 32,
                      ),
                      onPressed: _togglePlay,
                    ),
                    const SizedBox(width: 8),

                    // 音频波形图/进度条
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildWaveform(textColor),
                          const SizedBox(height: 4),
                          Text(
                            '${widget.durationInSeconds}″',
                            style: TextStyle(
                              color: textColor.withOpacity(0.7),
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          if (widget.isMe) _buildAvatar(),
        ],
      ),
    );
  }

  Widget _buildAvatar() {
    return Padding(
      padding: const EdgeInsets.only(left: 8, right: 8, bottom: 4),
      child: CircleAvatar(
        radius: 16,
        backgroundColor: widget.isMe ? widget.myBubbleColor : Colors.grey[300],
        child: Icon(
          Icons.person,
          size: 18,
          color: widget.isMe ? Colors.white : Colors.grey[700],
        ),
      ),
    );
  }

  Widget _buildWaveform(Color color) {
    // 计算播放进度百分比
    final totalDuration = Duration(seconds: widget.durationInSeconds);
    final progressPercent = totalDuration.inMilliseconds > 0
        ? _position.inMilliseconds / totalDuration.inMilliseconds
        : 0.0;

    return SizedBox(
      height: 24,
      child: Row(
        children: List.generate(15, (index) {
          // 随机高度，模拟波形图
          final barHeight = 5 + (index % 3) * 4 + (index % 5) * 2.0;

          // 已播放的部分使用深色，未播放使用浅色
          final isActive = index / 15 <= progressPercent;
          final barColor = isActive ? color : color.withOpacity(0.4);

          return Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 1),
              child: Container(
                height: barHeight,
                decoration: BoxDecoration(
                  color: barColor,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
            ),
          );
        }),
      ),
    );
  }
}

import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:record/record.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:audioplayers/audioplayers.dart';

typedef VoiceRecordCompleteCallback = void Function(
    String? voicePath, double duration);
typedef RecordStatusCallback = void Function(RecordingStatus status);
typedef RecordingTimeCallback = void Function(int seconds);

enum RecordingStatus {
  idle, // 空闲状态
  recording, // 录音中
  cancelling, // 取消录音
  willCancel, // 将要取消（上滑状态）
}

class SimpleVoiceRecorder extends StatefulWidget {
  final VoiceRecordCompleteCallback onRecordComplete;
  final RecordStatusCallback? onRecordStatusChanged;
  final RecordingTimeCallback? onRecordingTimeChanged;
  final Color primaryColor;
  final Color cancelColor;
  final String holdText;
  final String releaseText;
  final String cancelText;

  const SimpleVoiceRecorder({
    Key? key,
    required this.onRecordComplete,
    this.onRecordStatusChanged,
    this.onRecordingTimeChanged,
    this.primaryColor = const Color(0xFF3F51B5),
    this.cancelColor = Colors.red,
    this.holdText = '按住说话',
    this.releaseText = '松开发送',
    this.cancelText = '松开手指取消发送',
  }) : super(key: key);

  @override
  State<SimpleVoiceRecorder> createState() => _SimpleVoiceRecorderState();
}

class _SimpleVoiceRecorderState extends State<SimpleVoiceRecorder> {
  final AudioRecorder _recorder = AudioRecorder();
  final AudioPlayer _audioPlayer = AudioPlayer();

  RecordingStatus _recordStatus = RecordingStatus.idle;
  double _startPositionY = 0;
  double _currentPositionY = 0;

  // 录音相关
  String? _recordPath;
  int _recordDuration = 0;
  Timer? _recordTimer;
  Timer? _amplitudeTimer;
  int _volumeLevel = 0;
  final int _maxVolumeLevel = 7;

  @override
  void dispose() {
    _stopRecording();
    _stopTimers();
    _recorder.dispose();
    _audioPlayer.dispose();
    super.dispose();
  }

  // 开始录音
  Future<void> _startRecording() async {
    // 检查并请求麦克风权限
    var status = await Permission.microphone.request();
    if (status != PermissionStatus.granted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('需要麦克风权限才能录音')),
      );
      return;
    }

    // 更新状态为录音中
    setState(() {
      _recordStatus = RecordingStatus.recording;
      _recordDuration = 0;
      _volumeLevel = 0;
    });

    // 通知状态变化
    widget.onRecordStatusChanged?.call(_recordStatus);

    // 准备录音文件路径
    final dir = await getTemporaryDirectory();
    _recordPath =
        '${dir.path}/audio_${DateTime.now().millisecondsSinceEpoch}.m4a';

    // 开始录音
    await _recorder.start(
      RecordConfig(
        encoder: AudioEncoder.aacLc,
        bitRate: 128000,
        sampleRate: 44100,
      ),
      path: _recordPath!,
    );

    // 开始计时器
    _startTimers();
  }

  // 停止录音
  Future<void> _stopRecording({bool cancel = false}) async {
    _stopTimers();

    // 如果取消录音或者未开始录音
    if (cancel ||
        _recordStatus == RecordingStatus.cancelling ||
        _recordStatus == RecordingStatus.willCancel ||
        _recordPath == null) {
      if (await _recorder.isRecording()) {
        await _recorder.stop();
      }

      setState(() {
        _recordStatus = RecordingStatus.idle;
      });

      widget.onRecordStatusChanged?.call(_recordStatus);
      return;
    }

    // 如果录音时间太短
    if (_recordDuration < 1) {
      if (await _recorder.isRecording()) {
        await _recorder.stop();
      }

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('说话时间太短')),
      );

      setState(() {
        _recordStatus = RecordingStatus.idle;
      });

      widget.onRecordStatusChanged?.call(_recordStatus);
      return;
    }

    // 停止录音
    if (await _recorder.isRecording()) {
      final path = await _recorder.stop();

      setState(() {
        _recordStatus = RecordingStatus.idle;
      });

      // 回调录音文件路径和时长
      if (path != null) {
        widget.onRecordComplete(path, _recordDuration.toDouble());
      }
      widget.onRecordStatusChanged?.call(_recordStatus);
    }
  }

  // 开始计时器
  void _startTimers() {
    _stopTimers();

    // 录音时长计时器
    _recordTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _recordDuration++;
      });

      // 通知录音时长变化
      widget.onRecordingTimeChanged?.call(_recordDuration);
    });

    // 音量监测计时器
    _amplitudeTimer =
        Timer.periodic(const Duration(milliseconds: 200), (timer) async {
      final amplitude = await _recorder.getAmplitude();
      final volume = amplitude.current / 100; // 归一化到0-1范围

      // 将音量值（0-1）映射到音量级别（0-7）
      int level = (volume * _maxVolumeLevel).round();
      if (level > _maxVolumeLevel) level = _maxVolumeLevel;

      setState(() {
        _volumeLevel = level;
      });
    });
  }

  // 停止计时器
  void _stopTimers() {
    _recordTimer?.cancel();
    _recordTimer = null;
    _amplitudeTimer?.cancel();
    _amplitudeTimer = null;
  }

  // 检测垂直拖动
  void _checkVerticalDrag(double deltaY) {
    // 上滑超过阈值，进入取消状态
    final threshold = 30.0;

    if (deltaY < -threshold) {
      if (_recordStatus != RecordingStatus.willCancel) {
        setState(() {
          _recordStatus = RecordingStatus.willCancel;
        });
        widget.onRecordStatusChanged?.call(_recordStatus);
      }
    } else {
      if (_recordStatus == RecordingStatus.willCancel) {
        setState(() {
          _recordStatus = RecordingStatus.recording;
        });
        widget.onRecordStatusChanged?.call(_recordStatus);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        GestureDetector(
          onPanStart: (details) {
            if (_recordStatus == RecordingStatus.idle) {
              _startPositionY = details.globalPosition.dy;
              _currentPositionY = _startPositionY;
              _startRecording();
            }
          },
          onPanUpdate: (details) {
            if (_recordStatus == RecordingStatus.recording ||
                _recordStatus == RecordingStatus.willCancel) {
              _currentPositionY = details.globalPosition.dy;
              final deltaY = _currentPositionY - _startPositionY;
              _checkVerticalDrag(deltaY);
            }
          },
          onPanEnd: (details) {
            bool shouldCancel = _recordStatus == RecordingStatus.willCancel;
            _stopRecording(cancel: shouldCancel);
          },
          onPanCancel: () {
            _stopRecording(cancel: true);
          },
          child: Container(
            width: double.infinity,
            alignment: Alignment.center,
            padding: const EdgeInsets.symmetric(vertical: 10),
            child: Text(
              _getButtonText(),
              style: TextStyle(
                color: _getTextColor(),
                fontSize: 16,
              ),
            ),
          ),
        ),
      ],
    );
  }

  // 获取按钮文本
  String _getButtonText() {
    switch (_recordStatus) {
      case RecordingStatus.idle:
        return widget.holdText;
      case RecordingStatus.recording:
        return widget.releaseText;
      case RecordingStatus.cancelling:
      case RecordingStatus.willCancel:
        return widget.cancelText;
      default:
        return widget.holdText;
    }
  }

  // 获取文本颜色
  Color _getTextColor() {
    switch (_recordStatus) {
      case RecordingStatus.idle:
        return Colors.grey[700]!;
      case RecordingStatus.recording:
        return widget.primaryColor;
      case RecordingStatus.cancelling:
      case RecordingStatus.willCancel:
        return widget.cancelColor;
      default:
        return Colors.grey[700]!;
    }
  }
}

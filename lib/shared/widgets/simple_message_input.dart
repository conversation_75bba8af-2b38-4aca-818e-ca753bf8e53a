import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'simple_voice_recorder.dart';

class SimpleMessageInput extends StatefulWidget {
  final Function(String text) onSendText;
  final Function(String audioPath, double duration) onSendVoice;
  final Function(bool isRecording, bool showCancel)? onRecordingStateChanged;
  final Function(int seconds)? onRecordingTimeChanged;
  final String? hintText;
  final Color? primaryColor;

  const SimpleMessageInput({
    Key? key,
    required this.onSendText,
    required this.onSendVoice,
    this.onRecordingStateChanged,
    this.onRecordingTimeChanged,
    this.hintText,
    this.primaryColor,
  }) : super(key: key);

  @override
  State<SimpleMessageInput> createState() => _SimpleMessageInputState();
}

class _SimpleMessageInputState extends State<SimpleMessageInput> {
  final TextEditingController _textController = TextEditingController();
  bool _isVoiceMode = false; // 是否是语音模式
  RecordingStatus _recordStatus = RecordingStatus.idle;
  int _recordingDuration = 0;
  bool _isRecording = false;
  bool _showCancelRecording = false;
  bool _hasText = false; // 追踪文本状态

  @override
  void dispose() {
    _textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final primaryColor = widget.primaryColor ?? Theme.of(context).primaryColor;
    final hintText = widget.hintText ?? 'type_message'.tr;

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 3,
            offset: const Offset(0, -1),
          ),
        ],
      ),
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                // 语音/键盘切换按钮
                GestureDetector(
                  onTap: () {
                    setState(() {
                      _isVoiceMode = !_isVoiceMode;
                    });
                  },
                  child: Container(
                    width: 36.w,
                    height: 36.w,
                    margin: EdgeInsets.only(right: 8.w, bottom: 5.h),
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      _isVoiceMode ? Icons.keyboard : Icons.mic,
                      color: Colors.grey[700],
                      size: 24.sp,
                    ),
                  ),
                ),

                // 中间区域：文本框或语音按钮
                Expanded(
                  child: _isVoiceMode
                      ? _buildVoiceButton(primaryColor)
                      : _buildTextInput(hintText),
                ),

                // 发送按钮 (始终显示在文本模式下)
                if (!_isVoiceMode)
                  GestureDetector(
                    onTap: () {
                      if (_textController.text.isNotEmpty) {
                        widget.onSendText(_textController.text);
                        _textController.clear();
                        setState(() {
                          _hasText = false;
                        });
                      }
                    },
                    child: Container(
                      width: 60.w,
                      height: 36.h,
                      margin: EdgeInsets.only(left: 8.w),
                      decoration: BoxDecoration(
                        color: primaryColor,
                        borderRadius: BorderRadius.circular(4.r),
                      ),
                      alignment: Alignment.center,
                      child: Text(
                        'send'.tr,
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),

                // 表情按钮 (仅在文本模式且没有文本时显示)
                if (!_isVoiceMode && _textController.text.isEmpty && false)
                  GestureDetector(
                    onTap: () {
                      // TODO: 显示表情选择器
                    },
                    child: Container(
                      width: 36.w,
                      height: 36.w,
                      margin: EdgeInsets.only(left: 8.w, bottom: 5.h),
                      decoration: const BoxDecoration(
                        color: Colors.white,
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.emoji_emotions_outlined,
                        color: Colors.grey[700],
                        size: 24.sp,
                      ),
                    ),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // 文本输入框
  Widget _buildTextInput(String hintText) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(4.r),
      ),
      child: TextField(
        controller: _textController,
        decoration: InputDecoration(
          hintText: hintText,
          hintStyle: TextStyle(color: Colors.grey[500]),
          border: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
        ),
        maxLines: 5,
        minLines: 1,
        keyboardType: TextInputType.multiline,
        textInputAction: TextInputAction.newline,
        enableSuggestions: true,
        autocorrect: true,
        // 移除固定的文本方向设置，允许系统自动处理
        textDirection: TextDirection.ltr,
        style: TextStyle(
          fontSize: 16.sp,
          color: Colors.black87,
        ),
        onChanged: (value) {
          // 强制刷新UI，显示或隐藏发送按钮
          setState(() {});
        },
      ),
    );
  }

  // 语音按钮
  Widget _buildVoiceButton(Color primaryColor) {
    return Container(
      height: 36.h,
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(4.r),
      ),
      child: SimpleVoiceRecorder(
        onRecordComplete: (path, duration) {
          if (path != null && path.isNotEmpty) {
            widget.onSendVoice(path, duration);
          }

          // 录音结束
          _isRecording = false;
          _showCancelRecording = false;
          _notifyRecordingStateChanged();
        },
        onRecordStatusChanged: (status) {
          setState(() {
            _recordStatus = status;

            // 更新录音状态
            _isRecording = status == RecordingStatus.recording;
            _showCancelRecording = status == RecordingStatus.willCancel;

            // 通知状态变化
            _notifyRecordingStateChanged();
          });
        },
        onRecordingTimeChanged: (timeInSeconds) {
          _recordingDuration = timeInSeconds;

          // 通知录音时间变化
          if (widget.onRecordingTimeChanged != null) {
            widget.onRecordingTimeChanged!(_recordingDuration);
          }
        },
        primaryColor: primaryColor,
        holdText: 'press_to_talk'.tr,
        releaseText: 'release_to_send'.tr,
        cancelText: 'slide_to_cancel'.tr,
      ),
    );
  }

  // 通知录音状态变化
  void _notifyRecordingStateChanged() {
    if (widget.onRecordingStateChanged != null) {
      widget.onRecordingStateChanged!(_isRecording, _showCancelRecording);
    }
  }
}

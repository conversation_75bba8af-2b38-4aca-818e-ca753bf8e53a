import 'package:get/get.dart';

import '../../../data/repositories/auth_repository.dart';
import '../controllers/auth_controller.dart';

/// 认证绑定
class AuthBinding implements Bindings {
  @override
  void dependencies() {
    // 注册AuthRepository
    Get.lazyPut<AuthRepository>(
      () => AuthRepository(),
      fenix: true,
    );

    // 注册AuthController
    Get.lazyPut<AuthController>(() => AuthController(), fenix: true);
  }
}

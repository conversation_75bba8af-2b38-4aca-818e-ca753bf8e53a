import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/utils/validators.dart';
import '../../../routes/app_routes.dart';
import '../controllers/auth_controller.dart';

class LoginView extends GetView<AuthController> {
  const LoginView({super.key});

  @override
  Widget build(BuildContext context) {
    final formKey = GlobalKey<FormState>();

    void handleLogin() {
      if (formKey.currentState!.validate()) {
        controller.login(
          controller.emailController.text,
          controller.passwordController.text,
        );
      }
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('登录'),
      ),
      body: GestureDetector(
        // 点击空白处收起键盘
        // onTap: () => FocusScope.of(context).unfocus(),
        child: SafeArea(
          child: Form(
            key: formKey,
            child: CustomScrollView(
              physics: const ClampingScrollPhysics(),
              slivers: [
                SliverFillRemaining(
                  hasScrollBody: false,
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        const Spacer(flex: 1),
                        const Icon(
                          Icons.watch,
                          size: 80,
                          color: Colors.blue,
                        ),
                        const SizedBox(height: 20),
                        const Text(
                          '智能穿戴',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 40),
                        TextFormField(
                          controller: controller.emailController,
                          decoration: const InputDecoration(
                            labelText: '邮箱',
                            prefixIcon: Icon(Icons.person),
                          ),
                          validator: Validators.validateUsername,
                          textInputAction: TextInputAction.next,
                        ),
                        const SizedBox(height: 16),
                        TextFormField(
                          controller: controller.passwordController,
                          decoration: const InputDecoration(
                            labelText: '密码',
                            prefixIcon: Icon(Icons.lock),
                          ),
                          obscureText: true,
                          validator: Validators.validatePassword,
                          textInputAction: TextInputAction.done,
                          onFieldSubmitted: (_) {
                            // 当用户按下键盘上的完成按钮时，触发登录
                            handleLogin();
                          },
                        ),
                        const SizedBox(height: 16),

                        // 记住我选项
                        Align(
                          alignment: Alignment.centerLeft,
                          child: Obx(() => Row(
                                children: [
                                  Checkbox(
                                    value: controller.rememberMe.value,
                                    onChanged: (value) => controller
                                        .setRememberMe(value ?? false),
                                  ),
                                  const Text('记住我'),
                                ],
                              )),
                        ),

                        const SizedBox(height: 24),
                        ElevatedButton(
                              onPressed: handleLogin,
                              child: const Text('登录'),
                            ),
                        const SizedBox(height: 16),
                        TextButton(
                          onPressed: () => Get.toNamed(Routes.REGISTER),
                          child: const Text('没有账号？点击注册'),
                        ),
                        const Spacer(flex: 1),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

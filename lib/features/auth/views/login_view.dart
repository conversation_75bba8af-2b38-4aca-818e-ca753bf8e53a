import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../../../core/config/app_theme.dart';
import '../../../core/utils/validators.dart';
import '../../../shared/widgets/common_widgets.dart';
import '../../../shared/widgets/custom_button.dart';
import '../controllers/auth_controller.dart';

class LoginView extends GetView<AuthController> {
  const LoginView({super.key});

  @override
  Widget build(BuildContext context) {
    final formKey = GlobalKey<FormState>();

    void handleLogin() {
      if (formKey.currentState!.validate()) {
        controller.login(
          controller.emailController.text,
          controller.passwordController.text,
        );
      }
    }

    return Scaffold(
      // 沉浸式状态栏
      extendBodyBehindAppBar: true,
      backgroundColor: AppTheme.loginBackgroundColor,
      body: AnnotatedRegion<SystemUiOverlayStyle>(
        value: const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.dark,
          systemNavigationBarColor: Colors.white,
          systemNavigationBarIconBrightness: Brightness.dark,
        ),
        child: SafeArea(
          child: Form(
            key: formKey,
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: 10.h),
                  // 页面头部 - 返回按钮和帮助按钮
                  const PageHeader(),
                  SizedBox(height: 4.h),

                  // 品牌图标
                  Container(
                    width: 70.r,
                    height: 70.r,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(16.r),
                      gradient: const LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          AppTheme.brandIconStartColor,
                          AppTheme.brandIconEndColor,
                        ],
                      ),
                    ),
                    child: Icon(
                      Icons.favorite,
                      color: const Color(0xFFFFD700),
                      size: 32.r,
                    ),
                  ),
                  SizedBox(height: 24.h),
                  // 标题
                  Text(
                    'Create your account',
                    textAlign: TextAlign.center,
                    style: AppTheme.loginTitleStyle,
                  ),
                  // 副标题
                  Text(
                    'Access all that Coinbase has to offer with asingle account',
                    textAlign: TextAlign.start,
                    style: AppTheme.loginSubtitleStyle,
                  ),

                  SizedBox(height: 40.h),


                  const SizedBox(height: 40),
                  TextFormField(
                    controller: controller.emailController,
                    decoration: const InputDecoration(
                      labelText: '邮箱',
                      prefixIcon: Icon(Icons.person),
                    ),
                    validator: Validators.validateUsername,
                    textInputAction: TextInputAction.next,
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: controller.passwordController,
                    decoration: const InputDecoration(
                      labelText: '密码',
                      prefixIcon: Icon(Icons.lock),
                    ),
                    obscureText: true,
                    validator: Validators.validatePassword,
                    textInputAction: TextInputAction.done,
                    onFieldSubmitted: (_) {
                      // 当用户按下键盘上的完成按钮时，触发登录
                      handleLogin();
                    },
                  ),

                  // 忘记密码链接 - 精确尺寸和位置
                  Align(
                    alignment: Alignment.centerRight,
                    child: SizedBox(
                      child: TextButton(
                        onPressed: () {
                          // TODO: 跳转到忘记密码页面
                        },
                        style: TextButton.styleFrom(
                          padding: EdgeInsets.zero,
                          minimumSize: Size.zero,
                          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        ),
                        child: Text(
                          'Forgot Password?',
                          style: AppTheme.forgotPasswordStyle,
                        ),
                      ),
                    ),
                  ),

                  SizedBox(height: 24.h),

                  // 登录按钮 - 使用标准化组件
                  Center(
                    child: PurpleLoginButton(
                      text: 'Log In',
                      onPressed: handleLogin,
                      isLoading: false,
                    ),
                  ),
                  SizedBox(height: 40.h),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

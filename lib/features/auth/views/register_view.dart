import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/utils/validators.dart';
import '../../../routes/app_routes.dart';
import '../controllers/auth_controller.dart';

class RegisterView extends GetView<AuthController> {
  const RegisterView({super.key});

  @override
  Widget build(BuildContext context) {
    final formKey = GlobalKey<FormState>();

    void handleRegister() {
      if (formKey.currentState!.validate()) {
        controller.register(
          username: controller.usernameController.text,
          email: controller.emailController.text,
          password: controller.passwordController.text,
        );
      }
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('注册'),
      ),
      body: GestureDetector(
        // 点击空白处收起键盘
        // onTap: () => FocusScope.of(context).unfocus(),
        child: Safe<PERSON><PERSON>(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Form(
              key: form<PERSON><PERSON>,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  const SizedBox(height: 20),
                  const Icon(
                    Icons.watch,
                    size: 80,
                    color: Colors.blue,
                  ),
                  const SizedBox(height: 20),
                  const Text(
                    '创建账号',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 30),
                  TextFormField(
                    controller: controller.usernameController,
                    decoration: const InputDecoration(
                      labelText: '用户名',
                      prefixIcon: Icon(Icons.person),
                    ),
                    validator: Validators.validateUsername,
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: controller.emailController,
                    decoration: const InputDecoration(
                      labelText: '电子邮箱',
                      prefixIcon: Icon(Icons.email),
                    ),
                    keyboardType: TextInputType.emailAddress,
                    validator: Validators.validateEmail,
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: controller.passwordController,
                    decoration: const InputDecoration(
                      labelText: '密码',
                      prefixIcon: Icon(Icons.lock),
                    ),
                    obscureText: true,
                    validator: Validators.validatePassword,
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: controller.confirmPasswordController,
                    decoration: const InputDecoration(
                      labelText: '确认密码',
                      prefixIcon: Icon(Icons.lock_outline),
                    ),
                    obscureText: true,
                    validator: (value) => Validators.validateConfirmPassword(
                      value,
                      controller.passwordController.text,
                    ),
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                        onPressed: handleRegister,
                        child: const Text('注册'),
                      ),
                  const SizedBox(height: 16),
                  TextButton(
                    onPressed: () => Get.offNamed(Routes.LOGIN),
                    child: const Text('已有账号？点击登录'),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

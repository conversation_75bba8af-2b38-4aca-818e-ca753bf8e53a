import 'package:get/get.dart';
import '../../../core/utils/logger.dart';
import '../../../core/services/app_state_service.dart';
import '../../../data/models/user_model.dart';
import '../../../data/models/member_model.dart';
import '../../../data/repositories/user_repository.dart';

class MemberManagementController extends GetxController {
  final UserRepository _userRepository;
  final AppStateService _appStateService;

  MemberManagementController({
    required UserRepository userRepository,
    required AppStateService appStateService,
  }) : _userRepository = userRepository,
       _appStateService = appStateService;

  // 加载状态
  final isLoading = false.obs;

  // 创建者
  final creator = Rx<MemberModel?>(null);

  // 亲密陪伴者列表
  final intimateMembers = <MemberModel>[].obs;

  // 普通陪伴者列表
  final regularMembers = <MemberModel>[].obs;

  // 当前用户类型
  final userType = ''.obs; // creator, intimate, regular

  /// 获取当前agentId
  String? get currentAgentId => _appStateService.currentAgentId.value.toString();

  /// 检查agentId是否可用
  bool _checkAgentId() {
    final agentId = currentAgentId;
    if (agentId == null || agentId.isEmpty) {
      AppLogger.warning('agentId为空，无法执行操作');
      Get.snackbar('操作失败', '请先创建分身');
      return false;
    }
    return true;
  }

  @override
  void onInit() {
    super.onInit();
    loadMembers();
  }

  // 加载成员信息
  Future<void> loadMembers() async {
    try {
      isLoading(true);
      
      // 检查agentId是否可用
      if (!_checkAgentId()) return;
      
      final String agentId = currentAgentId!;

      AppLogger.info('开始加载成员信息，agentId: $agentId');
      
      // 获取创建者信息
      final creatorResult = await _userRepository.getCreator(agentId);
      if (creatorResult != null) {
        creator.value = creatorResult;
        AppLogger.info('创建者信息加载成功');
      } else {
        creator.value = null;
        AppLogger.info('暂无创建者信息');
      }

      // 获取亲密陪伴者列表
      final intimateResult = await _userRepository.getIntimateMembers(agentId);
      if (intimateResult != null) {
        intimateMembers.assignAll(intimateResult);
        AppLogger.info('亲密陪伴者列表加载成功，数量：${intimateResult.length}');
      } else {
        intimateMembers.clear();
        AppLogger.info('亲密陪伴者列表为空');
      }

      // 获取普通陪伴者列表
      final regularResponse = await _userRepository.getRegularMembers(agentId);
      if (regularResponse != null) {
        regularMembers.assignAll(regularResponse);
        AppLogger.info('普通陪伴者列表加载成功，数量：${regularResponse.length}');
      } else {
        regularMembers.clear();
        AppLogger.info('普通陪伴者列表为空');
      }

      // 获取当前用户类型
      final typeResponse = await _userRepository.getCurrentUserType();
      if (typeResponse != null) {
        userType.value = typeResponse;
      }
    } catch (e) {
      AppLogger.error('加载成员信息失败', e);
      Get.snackbar('加载失败', '加载成员信息失败，请重试');
    } finally {
      isLoading(false);
    }
  }

  // 添加亲密陪伴者
  Future<bool> addIntimateMember(String invitationCode) async {
    try {
      isLoading(true);

      // 检查是否已达到上限
      if (intimateMembers.length >= 2) {
        Get.snackbar('添加失败', '亲密陪伴者数量已达上限（2人）');
        return false;
      }

      // 添加亲密陪伴者
      final response = await _userRepository.addIntimateMember(invitationCode);
      final bool success = response == true;

      if (success) {
        // 重新加载成员列表
        await loadMembers();
        Get.snackbar('添加成功', '亲密陪伴者添加成功');
      }

      return success;
    } catch (e) {
      AppLogger.error('添加亲密陪伴者失败', e);
      Get.snackbar('添加失败', '添加亲密陪伴者失败，请重试');
      return false;
    } finally {
      isLoading(false);
    }
  }

  // 添加普通陪伴者
  Future<bool> addRegularMember(String name) async {
    try {
      isLoading(true);

      // 检查是否已达到上限
      if (regularMembers.length >= 5) {
        Get.snackbar('添加失败', '普通陪伴者数量已达上限（5人）');
        return false;
      }

      // 添加普通陪伴者
      final response = await _userRepository.addRegularMember(name);
      final bool success = response == true;

      if (success) {
        // 重新加载成员列表
        await loadMembers();
        Get.snackbar('添加成功', '普通陪伴者添加成功');
      }

      return success;
    } catch (e) {
      AppLogger.error('添加普通陪伴者失败', e);
      Get.snackbar('添加失败', '添加普通陪伴者失败，请重试');
      return false;
    } finally {
      isLoading(false);
    }
  }

  // 移除亲密陪伴者
  Future<bool> removeIntimateMember(String memberId) async {
    try {
      isLoading(true);

      // 移除亲密陪伴者
      final response = await _userRepository.removeIntimateMember(memberId);
   
      final bool success =  response == true;

      if (success) {
        // 重新加载成员列表
        await loadMembers();
        Get.snackbar('移除成功', '亲密陪伴者已移除');
      }

      return success;
    } catch (e) {
      AppLogger.error('移除亲密陪伴者失败', e);
      Get.snackbar('移除失败', '移除亲密陪伴者失败，请重试');
      return false;
    } finally {
      isLoading(false);
    }
  }

  // 移除普通陪伴者
  Future<bool> removeRegularMember(String memberId) async {
    try {
      isLoading(true);

      // 移除普通陪伴者
      final response = await _userRepository.removeRegularMember(memberId);
      final bool success = response == true;

      if (success) {
        // 重新加载成员列表
        await loadMembers();
        Get.snackbar('移除成功', '普通陪伴者已移除');
      }

      return success;
    } catch (e) {
      AppLogger.error('移除普通陪伴者失败', e);
      Get.snackbar('移除失败', '移除普通陪伴者失败，请重试');
      return false;
    } finally {
      isLoading(false);
    }
  }

  // 获取邀请码
  Future<String> getInvitationCode() async {
    try {
      isLoading(true);

      // 获取邀请码
      final response = await _userRepository.getInvitationCode();

        return response!;
      return '';
    } catch (e) {
      AppLogger.error('获取邀请码失败', e);
      Get.snackbar('获取失败', '获取邀请码失败，请重试');
      return '';
    } finally {
      isLoading(false);
    }
  }

  // 验证邀请码
  Future<bool> verifyInvitationCode(String code) async {
    try {
      isLoading(true);

      // 验证邀请码
      final response = await _userRepository.verifyInvitationCode(code);
      return  response == true;
    } catch (e) {
      AppLogger.error('验证邀请码失败', e);
      return false;
    } finally {
      isLoading(false);
    }
  }
}

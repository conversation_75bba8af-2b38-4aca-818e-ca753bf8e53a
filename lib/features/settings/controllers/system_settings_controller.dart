import 'package:get/get.dart';
import '../../../core/services/storage_service.dart';
import '../../../data/repositories/device_repository.dart';
import '../../../core/utils/logger.dart';

class SystemSettingsController extends GetxController {
  final DeviceRepository _deviceRepository;
  final StorageService _storageService;

  SystemSettingsController({
    required DeviceRepository deviceRepository,
  })  : _deviceRepository = deviceRepository,
        _storageService = Get.find<StorageService>();

  // 夜灯模式
  final isNightLightEnabled = false.obs;

  // 免打扰模式
  final isDoNotDisturbEnabled = false.obs;

  // 肚子灯光亮度
  final brightnessLevel = 60.0.obs;

  // 系统音量
  final volumeLevel = 40.0.obs;

  @override
  void onInit() {
    super.onInit();
    _loadSettings();
  }

  // 加载设置
  Future<void> _loadSettings() async {
    try {
      // 从本地存储加载设置
      isNightLightEnabled.value =
          await _storageService.getBool('night_light_mode') ?? false;
      isDoNotDisturbEnabled.value =
          await _storageService.getBool('do_not_disturb_mode') ?? false;
      brightnessLevel.value =
          await _storageService.getDouble('brightness_level') ?? 60.0;
      volumeLevel.value =
          await _storageService.getDouble('volume_level') ?? 40.0;

      // 同步设备设置
      _syncSettingsWithDevice();
    } catch (e) {
      AppLogger.error('加载设置失败', e);
    }
  }

  // 切换夜灯模式
  void toggleNightLight(bool value) async {
    isNightLightEnabled.value = value;

    try {
      // 保存到本地
      await _storageService.setBool('night_light_mode', value);

      // 同步到设备
      await _deviceRepository.setNightLightMode(value);

      // 如果开启夜灯模式，自动调整亮度
      if (value) {
        // 设置暖光模式
        await _deviceRepository.setLightMode('warm');

        // 降低屏幕亮度
        await _deviceRepository.setScreenBrightness(20);
      } else {
        // 恢复正常模式
        await _deviceRepository.setLightMode('normal');

        // 恢复屏幕亮度
        await _deviceRepository
            .setScreenBrightness(brightnessLevel.value.toInt());
      }

      // 发送系统消息
      _sendSystemMessage(
        value ? '夜灯模式已开启' : '夜灯模式已关闭',
        '系统设置',
      );
    } catch (e) {
      AppLogger.error('设置夜灯模式失败', e);
      Get.snackbar('设置失败', '夜灯模式设置失败，请重试');
    }
  }

  // 切换免打扰模式
  void toggleDoNotDisturb(bool value) async {
    isDoNotDisturbEnabled.value = value;

    try {
      // 保存到本地
      await _storageService.setBool('do_not_disturb_mode', value);

      // 同步到设备
      await _deviceRepository.setDoNotDisturbMode(value);

      // 发送系统消息
      _sendSystemMessage(
        value ? '免打扰模式已开启' : '免打扰模式已关闭',
        '系统设置',
      );
    } catch (e) {
      AppLogger.error('设置免打扰模式失败', e);
      Get.snackbar('设置失败', '免打扰模式设置失败，请重试');
    }
  }

  // 设置肚子灯光亮度
  void setBrightnessLevel(double value) async {
    brightnessLevel.value = value;

    try {
      // 保存到本地
      await _storageService.setDouble('brightness_level', value);

      // 同步到设备
      await _deviceRepository.setLightBrightness(value.toInt());
    } catch (e) {
      AppLogger.error('设置亮度失败', e);
      Get.snackbar('设置失败', '亮度设置失败，请重试');
    }
  }

  // 设置系统音量
  void setVolumeLevel(double value) async {
    volumeLevel.value = value;

    try {
      // 保存到本地
      await _storageService.setDouble('volume_level', value);

      // 同步到设备
      await _deviceRepository.setVolume(value.toInt());
    } catch (e) {
      AppLogger.error('设置音量失败', e);
      Get.snackbar('设置失败', '音量设置失败，请重试');
    }
  }

  // 同步设置到设备
  Future<void> _syncSettingsWithDevice() async {
    try {
      await _deviceRepository.setNightLightMode(isNightLightEnabled.value);
      await _deviceRepository.setDoNotDisturbMode(isDoNotDisturbEnabled.value);
      await _deviceRepository.setLightBrightness(brightnessLevel.value.toInt());
      await _deviceRepository.setVolume(volumeLevel.value.toInt());

      if (isNightLightEnabled.value) {
        await _deviceRepository.setLightMode('warm');
        await _deviceRepository.setScreenBrightness(20);
      }
    } catch (e) {
      AppLogger.error('同步设置到设备失败', e);
    }
  }

  // 发送系统消息
  void _sendSystemMessage(String message, String category) async {
    try {
      // 获取当前设备ID（假设有一个默认设备ID）
      final String deviceId = "default_device_id"; // 替换为实际获取逻辑

      await _deviceRepository.sendSystemMessage(
        deviceId: deviceId,
        message: message,
      );
    } catch (e) {
      AppLogger.error('发送系统消息失败', e);
    }
  }
}

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:smartai/routes/app_routes.dart';
import '../controllers/member_management_controller.dart';
import '../../../core/utils/route_helper.dart';

class MemberManagementView extends GetView<MemberManagementController> {
  const MemberManagementView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
        title: const Text('绑定成员管理'),
        centerTitle: true,
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return const Center(child: CircularProgressIndicator());
        }
        return SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 创建者
              _buildSectionTitle('创建者'),
              _buildCreatorCard(context),
              _buildSectionDescription(
                  '创建者具有添加/删除亲密陪伴者和其他陪伴者、修改人偶初始化数据、添加记忆权限。'),

              // 亲密陪伴者
              _buildSectionTitle('亲密陪伴者'),
              Obx(() => Column(
                    children: [
                      ...controller.intimateMembers
                          .map((member) => _buildMemberCard(context, member,
                              isIntimate: true))
                          .toList(),
                      if (controller.intimateMembers.length < 2)
                        _buildAddMemberButton(context, '添加亲密陪伴者',
                            () => RouterHelper.toAvatarInvite()),
                    ],
                  )),
              _buildSectionDescription(
                  '亲密陪伴者为核心陪伴者，将深度记忆其互动过程；具有添加其他陪伴者权限；\n最多可设置两位亲密陪伴者。'),

              // 普通陪伴者
              _buildSectionTitle('其他陪伴者'),
              Obx(() => Column(
                    children: [
                      ...controller.regularMembers
                          .map((member) => _buildMemberCard(context, member,
                              isIntimate: false))
                          .toList(),
                      if (controller.regularMembers.length < 5)
                        _buildAddMemberButton(context, '添加其他陪伴者',
                            () => RouterHelper.toAvatarInvite()),
                    ],
                  )),
              _buildSectionDescription(
                  '其他陪伴者为次要陪伴者，采用短期记忆形式，无需注册登录APP，\n由创建者或亲密陪伴者辅助添加；\n最多可设置五位其他陪伴者。'),
            ],
          ),
        );
      }),
    );
  }

  // 构建区块标题
  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: EdgeInsets.only(left: 16.r, top: 24.r, bottom: 8.r),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 18.sp,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  // 构建区块描述
  Widget _buildSectionDescription(String description) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.r, vertical: 8.r),
      child: Text(
        description,
        style: TextStyle(
          fontSize: 12.sp,
          color: Colors.grey[600],
        ),
      ),
    );
  }

  // 构建创建者卡片
  Widget _buildCreatorCard(BuildContext context) {
    return Obx(() {
      final creator = controller.creator.value;
      if (creator == null) {
        return const SizedBox.shrink();
      }

      return Container(
        margin: EdgeInsets.symmetric(horizontal: 16.r, vertical: 8.r),
        padding: EdgeInsets.all(16.r),
        decoration: BoxDecoration(
          color: Colors.grey[100],
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Row(
          children: [
            // 头像
            ClipRRect(
              borderRadius: BorderRadius.circular(8.r),
              child: Image.network(
                creator.avatar ?? 'https://image.totwoo.com/default_other.png',
                width: 60.r,
                height: 60.r,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => Container(
                  width: 60.r,
                  height: 60.r,
                  color: Colors.grey[300],
                  child:
                      Icon(Icons.person, size: 30.r, color: Colors.grey[600]),
                ),
              ),
            ),
            SizedBox(width: 16.w),

            // 用户信息
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        creator.userName ?? '未设置名称',
                        style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(width: 8.w),
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 8.r,
                          vertical: 2.r,
                        ),
                        decoration: BoxDecoration(
                          color:
                              Theme.of(context).primaryColor.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(4.r),
                        ),
                        child: Text(
                          '创建者',
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: Theme.of(context).primaryColor,
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 4.h),
                  Text(
                    creator.email ?? '<EMAIL>',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    });
  }

  // 构建成员卡片
  Widget _buildMemberCard(BuildContext context, dynamic member,
      {required bool isIntimate}) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.r, vertical: 8.r),
      padding: EdgeInsets.all(16.r),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Row(
        children: [
          // 头像
          ClipRRect(
            borderRadius: BorderRadius.circular(8.r),
            child: Image.network(
              member.avatar ?? '',
              width: 60.r,
              height: 60.r,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) => Container(
                width: 60.r,
                height: 60.r,
                color: Colors.grey[300],
                child: Icon(Icons.person, size: 30.r, color: Colors.grey[600]),
              ),
            ),
          ),
          SizedBox(width: 16.w),

          // 用户信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      member.userName ?? '未设置名称',
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(width: 8.w),
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: 8.r,
                        vertical: 2.r,
                      ),
                      decoration: BoxDecoration(
                        color: isIntimate
                            ? Colors.blue.withOpacity(0.1)
                            : Colors.green.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(4.r),
                      ),
                      child: Text(
                        isIntimate ? '亲密陪伴者' : '其他陪伴者',
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: isIntimate ? Colors.blue : Colors.green,
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 4.h),
                Text(
                  member.email ?? '',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),

          // 编辑按钮
          IconButton(
            icon: const Icon(Icons.more_vert),
            onPressed: () => _showMemberOptions(context, member, isIntimate),
          ),
        ],
      ),
    );
  }

  // 构建添加成员按钮
  Widget _buildAddMemberButton(
      BuildContext context, String label, VoidCallback onTap) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.r, vertical: 8.r),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8.r),
        child: Container(
          padding: EdgeInsets.all(16.r),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey[300]!),
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Row(
            children: [
              CircleAvatar(
                radius: 30.r,
                backgroundColor: Colors.grey[200],
                child: Icon(Icons.add, size: 30.r, color: Colors.grey[600]),
              ),
              SizedBox(width: 16.w),
              Text(
                label,
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w500,
                  color: Theme.of(context).primaryColor,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 显示成员操作选项
  void _showMemberOptions(
      BuildContext context, dynamic member, bool isIntimate) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading: const Icon(Icons.edit),
            title: const Text('编辑信息'),
            onTap: () {
              Get.back();
              Get.toNamed(
                '/settings/members/edit',
                arguments: {
                  'member': member,
                  'isIntimate': isIntimate,
                },
              );
            },
          ),
          ListTile(
            leading: const Icon(Icons.delete, color: Colors.red),
            title: const Text('删除成员', style: TextStyle(color: Colors.red)),
            onTap: () {
              Get.back();
              _showDeleteConfirmation(context, member, isIntimate);
            },
          ),
          ListTile(
            leading: const Icon(Icons.close),
            title: const Text('取消'),
            onTap: () => Get.back(),
          ),
        ],
      ),
    );
  }

  // 显示删除确认对话框
  void _showDeleteConfirmation(
      BuildContext context, dynamic member, bool isIntimate) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('删除${isIntimate ? '亲密陪伴者' : '其他陪伴者'}'),
        content: Text('确定要删除 ${member.name} 吗？此操作不可恢复。'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              if (isIntimate) {
                controller.removeIntimateMember(member.id);
              } else {
                controller.removeRegularMember(member.id);
              }
            },
            child: const Text('确定删除', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }
}

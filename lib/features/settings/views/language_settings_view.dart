import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../core/controllers/language_controller.dart';

class LanguageSettingsView extends StatelessWidget {
  const LanguageSettingsView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final languageController = Get.put(LanguageController());

    return Scaffold(
      appBar: AppBar(
        title: const Text('语言设置'),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () => Get.back(),
        ),
      ),
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.r, vertical: 16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '选择您偏好的语言',
              style: TextStyle(
                fontSize: 16.sp,
                color: Colors.grey[600],
              ),
            ),
            SizedBox(height: 16.h),

            // 语言选项列表
            Expanded(
              child: GetBuilder<LanguageController>(
                builder: (controller) {
                  return ListView(
                    children:
                        controller.supportedLanguages.keys.map((language) {
                      final isSelected =
                          language == controller.currentLanguageName;

                      return Card(
                        margin: EdgeInsets.symmetric(vertical: 8.r),
                        child: ListTile(
                          title: Text(
                            language,
                            style: TextStyle(
                              fontSize: 16.sp,
                              fontWeight: isSelected
                                  ? FontWeight.bold
                                  : FontWeight.normal,
                            ),
                          ),
                          trailing: isSelected
                              ? Icon(
                                  Icons.check_circle,
                                  color: Theme.of(context).primaryColor,
                                )
                              : null,
                          onTap: () => controller.changeLanguage(language),
                        ),
                      );
                    }).toList(),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}

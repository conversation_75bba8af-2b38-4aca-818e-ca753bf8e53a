import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../controllers/system_settings_controller.dart';
import '../../../features/auth/controllers/auth_controller.dart';

class SystemSettingsView extends GetView<SystemSettingsController> {
  const SystemSettingsView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 获取AuthController
    final AuthController authController = Get.find<AuthController>();

    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
        title: const Text('系统设置'),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 夜灯模式
            _buildSettingSection(
              context,
              child: _buildNightLightMode(context),
            ),

            // 免打扰模式
            _buildSettingSection(
              context,
              child: _buildDoNotDisturbMode(context),
            ),

            // 系统设置标题
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.r, vertical: 8.r),
              child: Text(
                '系统设置',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[800],
                ),
              ),
            ),

            // 肚子灯光亮度
            _buildSettingSection(
              context,
              child: _buildBrightnessSlider(context),
            ),

            // 系统音量
            _buildSettingSection(
              context,
              child: _buildVolumeSlider(context),
            ),

            // 账号设置标题
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.r, vertical: 8.r),
              child: Text(
                '账号设置',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[800],
                ),
              ),
            ),

            // 退出登录按钮
            _buildSettingSection(
              context,
              child: _buildLogoutButton(context, authController),
            ),
          ],
        ),
      ),
    );
  }

  // 设置区域
  Widget _buildSettingSection(BuildContext context, {required Widget child}) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 8.r),
      padding: EdgeInsets.symmetric(vertical: 16.r),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: child,
    );
  }

  // 夜灯模式
  Widget _buildNightLightMode(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.r),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '夜灯模式',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Obx(() => Switch(
                    value: controller.isNightLightEnabled.value,
                    onChanged: controller.toggleNightLight,
                    activeColor: Theme.of(context).primaryColor,
                  )),
            ],
          ),
        ),

        // 夜灯模式图标和说明
        Obx(() {
          if (controller.isNightLightEnabled.value) {
            return Column(
              children: [
                SizedBox(height: 16.h),
                Center(
                  child: Icon(
                    Icons.lightbulb_outline,
                    size: 80.r,
                    color: Colors.grey[600],
                  ),
                ),
                SizedBox(height: 16.h),
                Center(
                  child: Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 16.r,
                      vertical: 8.r,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(20.r),
                    ),
                    child: Text(
                      '暖光灯',
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: Colors.grey[700],
                      ),
                    ),
                  ),
                ),
                SizedBox(height: 16.h),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16.r),
                  child: Text(
                    '开启夜灯模式，将自动降低眼睛亮度，可调节灯光模式和亮度大小',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: Colors.grey[600],
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            );
          }
          return const SizedBox.shrink();
        }),
      ],
    );
  }

  // 免打扰模式
  Widget _buildDoNotDisturbMode(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.r),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '免打扰模式',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Obx(() => Switch(
                    value: controller.isDoNotDisturbEnabled.value,
                    onChanged: controller.toggleDoNotDisturb,
                    activeColor: Theme.of(context).primaryColor,
                  )),
            ],
          ),
        ),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.r),
          child: Text(
            '开启免打扰模式后，人偶将暂停主动发声，提醒类消息仅APP显示通知',
            style: TextStyle(
              fontSize: 12.sp,
              color: Colors.grey[600],
            ),
          ),
        ),
      ],
    );
  }

  // 肚子灯光亮度
  Widget _buildBrightnessSlider(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.r),
          child: Row(
            children: [
              Text(
                '肚子灯光亮度',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: 8.h),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.r),
          child: Row(
            children: [
              Icon(Icons.brightness_low, color: Colors.grey[600]),
              Expanded(
                child: Obx(() => Slider(
                      value: controller.brightnessLevel.value,
                      onChanged: controller.setBrightnessLevel,
                      activeColor: Theme.of(context).primaryColor,
                      min: 0,
                      max: 100,
                    )),
              ),
              Container(
                width: 48.w,
                alignment: Alignment.center,
                child: Obx(() => Text(
                      '${controller.brightnessLevel.value.toInt()}%',
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: Colors.grey[800],
                      ),
                    )),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // 系统音量
  Widget _buildVolumeSlider(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.r),
          child: Row(
            children: [
              Text(
                '系统音量',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: 8.h),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.r),
          child: Row(
            children: [
              Icon(Icons.volume_down, color: Colors.grey[600]),
              Expanded(
                child: Obx(() => Slider(
                      value: controller.volumeLevel.value,
                      onChanged: controller.setVolumeLevel,
                      activeColor: Theme.of(context).primaryColor,
                      min: 0,
                      max: 100,
                    )),
              ),
              Container(
                width: 48.w,
                alignment: Alignment.center,
                child: Obx(() => Text(
                      '${controller.volumeLevel.value.toInt()}%',
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: Colors.grey[800],
                      ),
                    )),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // 退出登录按钮
  Widget _buildLogoutButton(
      BuildContext context, AuthController authController) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 16.r),
      child: ElevatedButton(
        onPressed: () => _showLogoutConfirmDialog(context, authController),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.redAccent,
          foregroundColor: Colors.white,
          padding: EdgeInsets.symmetric(vertical: 12.r),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8.r),
          ),
        ),
        child: Text(
          '退出登录',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  // 退出登录确认对话框
  void _showLogoutConfirmDialog(
      BuildContext context, AuthController authController) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('确认退出'),
          content: const Text('您确定要退出登录吗？'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                authController.logout();
              },
              child: Text(
                '确认',
                style: TextStyle(color: Colors.red[700]),
              ),
            ),
          ],
        );
      },
    );
  }
}

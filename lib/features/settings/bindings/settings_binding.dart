import 'package:get/get.dart';
import '../../../data/repositories/device_repository.dart';
import '../controllers/system_settings_controller.dart';

/// 系统设置绑定
class SettingsBinding implements Bindings {
  @override
  void dependencies() {
    // 注册DeviceRepository
    Get.lazyPut<DeviceRepository>(
      () => DeviceRepository(),
      fenix: true,
    );

    // 注册SystemSettingsController
    Get.lazyPut<SystemSettingsController>(
      () => SystemSettingsController(
        deviceRepository: Get.find<DeviceRepository>(),
      ),
      fenix: true,
    );
  }
}

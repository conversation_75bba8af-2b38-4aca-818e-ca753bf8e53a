import 'package:get/get.dart';
import '../../../core/services/app_state_service.dart';
import '../../../data/repositories/user_repository.dart';
import '../controllers/member_management_controller.dart';

/// 成员管理绑定
class MemberManagementBinding implements Bindings {
  @override
  void dependencies() {
    // 注册UserRepository
    Get.lazyPut<UserRepository>(
      () => UserRepository(),
      fenix: true,
    );

    // 注册MemberManagementController
    Get.lazyPut<MemberManagementController>(
      () => MemberManagementController(
        userRepository: Get.find<UserRepository>(),
        appStateService: Get.find<AppStateService>(),
      ),
      fenix: true,
    );
  }
}

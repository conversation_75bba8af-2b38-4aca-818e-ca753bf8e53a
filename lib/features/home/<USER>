import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'home_controller.dart';
import 'tabs/home_page_view.dart';
import 'tabs/msg_view.dart';
import 'tabs/profile_view.dart';

class HomeScreen extends GetView<HomeController> {
  const HomeScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Obx(() => _getPageByIndex(controller.currentIndex.value)),
      bottomNavigationBar: Obx(() => _buildBottomNavigationBar()),
    );
  }

  // 根据索引获取对应页面
  Widget _getPageByIndex(int index) {
    switch (index) {
      case 0:
        return const HomePageView();
      case 1:
        return const MsgView();
      case 2:
        return const ProfileView();
      default:
        return const HomePageView();
    }
  }

  // 构建底部导航栏
  Widget _buildBottomNavigationBar() {
    return BottomNavigationBar(
      currentIndex: controller.currentIndex.value,
      onTap: controller.changeTab,
      type: BottomNavigationBarType.fixed,
      selectedItemColor: Colors.blue,
      unselectedItemColor: Colors.grey,
      items: const [
        BottomNavigationBarItem(
          icon: Icon(Icons.home),
          label: '首页',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.message),
          label: '消息',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.person),
          label: '我的',
        ),
      ],
    );
  }
}

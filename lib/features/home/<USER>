import 'package:get/get.dart';
import '../../data/repositories/device_repository.dart';
import 'home_controller.dart';

/// 主页绑定
class HomeBinding implements Bindings {
  @override
  void dependencies() {
    // 注册DeviceRepository
    Get.lazyPut<DeviceRepository>(
      () => DeviceRepository(),
      fenix: true,
    );

    // 注册HomeController
    Get.lazyPut<HomeController>(() => HomeController(), fenix: true);
  }
}

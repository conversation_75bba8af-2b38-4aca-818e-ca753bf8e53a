import 'package:get/get.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import '../../core/utils/logger.dart';
import '../../data/models/home_model.dart';
import '../../routes/app_routes.dart';

/// 首页控制器 - 重构版本
/// 专注于首页展示和基本交互功能
/// 状态管理交给AppStateService
class HomeController extends GetxController {


  // 当前选中的底部导航栏索引
  final RxInt currentIndex = 0.obs;

  // 加载状态
  final RxBool isLoading = false.obs;

  // 首页状态数据 - 从socket接收
  final Rx<HomeModel> homeData = HomeModel(
    isWifiConnected: "false", // 默认未连接
    batteryLevel: null,
    systemVolume: null,
  ).obs;



  /// 更新首页数据（供socket调用）
  void updateHomeData(Map<String, dynamic> data) {
    homeData.value = HomeModel.fromJson(data);
    AppLogger.info('首页数据已更新: WiFi连接状态=${homeData.value.isWifiConnected}');
  }

  /// 获取WiFi连接状态
  bool get isWifiConnected {
    return homeData.value.isWifiConnected == "true";
  }



  /// 切换底部导航栏
  void changeTab(int index) {
    if (currentIndex.value == index) return;
    currentIndex.value = index;
  }

  /// 显示Toast消息
  void showToast(String message) {
    EasyLoading.showToast(message);
  }





  /// 处理分身交互
  void handleAvatarInteraction() {
    showToast('触摸分身');
    _sendInteractionCommand('touch');
  }

  /// 切换静音模式
  void toggleMute() {
    showToast('切换静音模式');
    _sendInteractionCommand('mute');
  }

  /// 切换夜间模式
  void toggleNightMode() {
    showToast('切换夜间模式');
    _sendInteractionCommand('night_mode');
  }

  /// 发送交互命令到设备
  void _sendInteractionCommand(String command) {
    try {
      // 这里应该通过蓝牙服务发送命令到设备
      // _bluetoothService.sendCommand(command);
      AppLogger.info('发送交互命令: $command');
    } catch (e) {
      AppLogger.error('发送交互命令失败', e);
      showToast('交互失败');
    }
  }

  /// 跳转到设备管理页面
  void goToDeviceManagement() {
    Get.toNamed(Routes.DEVICE_MANAGEMENT);
  }


  /// 跳转到消息页面
  void goToMessages() {
    changeTab(1); // 切换到消息tab
  }

  /// 跳转到个人中心
  void goToProfile() {
    changeTab(2); // 切换到个人中心tab
  }


  /// 获取当前页面标题
  String get currentPageTitle {
    switch (currentIndex.value) {
      case 0:
        return '首页';
      case 1:
        return '消息';
      case 2:
        return '我的';
      default:
        return '首页';
    }
  }


}

import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:record/record.dart';
import '../../../core/utils/logger.dart';
import '../../../core/services/app_state_service.dart';
import '../../../data/repositories/chat_repository.dart';
import '../../../core/network/socket/web_socket_instance.dart';
import '../../../core/network/socket/socket_resp.dart';

/// 消息类型枚举
enum MessageType { text, voice }

/// 消息发送者枚举
enum MessageSender { self, other }

/// 聊天消息模型
class ChatMessage {
  final String id;
  final String content;
  final MessageType type;
  final MessageSender sender;
  final DateTime timestamp;
  final int? voiceDuration;
  final bool? isPlaying;
  final String? nickname; // 发送者昵称

  ChatMessage({
    required this.id,
    required this.content,
    required this.type,
    required this.sender,
    required this.timestamp,
    this.voiceDuration,
    this.isPlaying = false,
    this.nickname,
  });

  bool get isMine => sender == MessageSender.self;

  /// 获取类型字符串，用于UI兼容性
  String get typeString {
    switch (type) {
      case MessageType.text:
        return 'text';
      case MessageType.voice:
        return 'voice';
    }
  }

  /// 获取显示的昵称，如果没有则显示"未知"
  String get displayNickname => nickname ?? '未知';

  ChatMessage copyWith({
    String? id,
    String? content,
    MessageType? type,
    MessageSender? sender,
    DateTime? timestamp,
    int? voiceDuration,
    bool? isPlaying,
    String? nickname,
  }) {
    return ChatMessage(
      id: id ?? this.id,
      content: content ?? this.content,
      type: type ?? this.type,
      sender: sender ?? this.sender,
      timestamp: timestamp ?? this.timestamp,
      voiceDuration: voiceDuration ?? this.voiceDuration,
      isPlaying: isPlaying ?? this.isPlaying,
      nickname: nickname ?? this.nickname,
    );
  }
}

/// 消息控制器 - 重构版本
/// 专注于聊天功能，使用真实的API调用
class MessageController extends GetxController {
  final AppStateService _appStateService = Get.find<AppStateService>();
  final ChatRepository _chatRepository = Get.find<ChatRepository>();
  final WebSocketInstance _webSocketInstance = WebSocketInstance.instance;
  
  /// 获取当前用户昵称
  String get _currentUserNickname {
    final user = _appStateService.user.value;
    return user?.userName ?? '我';
  }
  
  /// 获取AI助手昵称 (目前使用通用名称，后续可以从avatar信息获取)
  String get _botNickname {
    // TODO: 从当前avatar信息获取实际昵称
    return 'AI助手';
  }

  // 消息列表
  final RxList<ChatMessage> messages = <ChatMessage>[].obs;

  // 加载状态
  final RxBool isLoading = false.obs;

  // 录音相关状态
  final RxBool isRecording = false.obs;
  final RxInt recordDuration = 0.obs;
  final RxBool showCancelRecording = false.obs;

  // 当前正在播放的语音消息ID
  final RxString currentPlayingMessageId = ''.obs;

  // 输入框控制器
  final textController = TextEditingController();

  // 录音和播放器
  final _audioRecorder = AudioRecorder();
  final _audioPlayer = AudioPlayer();

  // 录音计时器
  Timer? _recordTimer;

  @override
  void onInit() {
    super.onInit();
    loadChatHistory();
    
    // 监听音频播放完成事件
    _audioPlayer.onPlayerComplete.listen((event) {
      stopPlayingVoiceMessage();
    });
    
    // 设置WebSocket消息监听
    _setupWebSocketListener();
  }

  @override
  void onClose() {
    textController.dispose();
    _stopRecordTimer();
    _audioRecorder.dispose();
    _audioPlayer.dispose();
    super.onClose();
  }


  /// 加载聊天历史 - 使用真实API
  Future<void> loadChatHistory() async {
    final agentId = "";
    if (agentId == null) {
      AppLogger.warning('没有找到agentId，无法加载聊天历史');
      return;
    }

    try {
      isLoading.value = true;
      AppLogger.info('开始加载聊天历史，agentId: $agentId');

      final response = await _chatRepository.getChatHistory(
        agentId: agentId,
        page: 1,
        limit: 50,
      );

      if (response != null && response != null) {
        final data = response!;
        final pageData = data['data'] as Map<String, dynamic>?;

        if (pageData != null && pageData['list'] != null) {
          final List<dynamic> chatSessions = pageData['list'] as List<dynamic>;
          final List<ChatMessage> historyMessages = [];

          for (final session in chatSessions) {
            final sessionId = session['sessionId'] as String?;
            final chatCount = session['chatCount'] as int? ?? 0;
            final createdAt = session['createdAt'] as String?;

            if (sessionId != null && chatCount > 0) {
              historyMessages.add(ChatMessage(
                id: sessionId,
                content: '历史对话 ($chatCount 条消息)',
                type: MessageType.text,
                sender: MessageSender.other,
                timestamp: DateTime.tryParse(createdAt ?? '') ?? DateTime.now(),
                nickname: _botNickname,
              ));
            }
          }

          messages.assignAll(historyMessages);
          AppLogger.info('成功加载 ${historyMessages.length} 条历史消息');
        }
      } else {
        AppLogger.warning('加载聊天历史失败: ${"操作失败"}');
      }
    } catch (e) {
      AppLogger.error('加载历史消息失败', e);
    } finally {
      isLoading.value = false;
    }
  }

  /// 发送文本消息
  void sendTextMessage(String text) {
    if (text.trim().isEmpty) return;

    try {
      final message = ChatMessage(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        content: text.trim(),
        type: MessageType.text,
        sender: MessageSender.self,
        timestamp: DateTime.now(),
        nickname: _currentUserNickname,
      );

      messages.add(message);
      textController.clear();

      // 通过WebSocket发送消息
      _sendMessageViaWebSocket(text.trim(), 'text');

      AppLogger.info('文本消息已发送: ${text.trim()}');
    } catch (e) {
      AppLogger.error('发送文本消息失败', e);
    }
  }

  /// 发送语音消息
  void sendVoiceMessage(String audioPath) {
    try {
      final message = ChatMessage(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        content: audioPath,
        type: MessageType.voice,
        sender: MessageSender.self,
        timestamp: DateTime.now(),
        voiceDuration: recordDuration.value > 0 ? recordDuration.value : 1,
        nickname: _currentUserNickname,
      );

      messages.add(message);

      // 通过WebSocket发送语音消息
      _sendMessageViaWebSocket(audioPath, 'voice', recordDuration.value);

      AppLogger.info('语音消息已发送，时长: ${recordDuration.value}秒');
    } catch (e) {
      AppLogger.error('发送语音消息失败', e);
    }
  }

  /// 播放语音消息
  Future<void> playVoiceMessage(ChatMessage message) async {
    try {
      if (currentPlayingMessageId.value == message.id) {
        // 如果正在播放同一条消息，则停止播放
        await stopPlayingVoiceMessage();
        return;
      }

      // 停止当前播放的消息
      await stopPlayingVoiceMessage();

      // 开始播放新消息
      currentPlayingMessageId.value = message.id;
      
      // 更新消息播放状态
      final index = messages.indexWhere((m) => m.id == message.id);
      if (index != -1) {
        messages[index] = message.copyWith(isPlaying: true);
      }

      // 播放音频文件
      await _audioPlayer.play(DeviceFileSource(message.content));
      
      AppLogger.info('开始播放语音消息: ${message.id}');
    } catch (e) {
      AppLogger.error('播放语音消息失败', e);
      await stopPlayingVoiceMessage();
    }
  }

  /// 停止播放语音消息
  Future<void> stopPlayingVoiceMessage() async {
    try {
      await _audioPlayer.stop();
      
      // 更新播放状态
      if (currentPlayingMessageId.value.isNotEmpty) {
        final index = messages.indexWhere((m) => m.id == currentPlayingMessageId.value);
        if (index != -1) {
          messages[index] = messages[index].copyWith(isPlaying: false);
        }
        currentPlayingMessageId.value = '';
      }
    } catch (e) {
      AppLogger.error('停止播放语音消息失败', e);
    }
  }

  /// 开始录音计时
  void _startRecordTimer() {
    recordDuration.value = 0;
    _recordTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      recordDuration.value++;
    });
  }

  /// 停止录音计时
  void _stopRecordTimer() {
    _recordTimer?.cancel();
    _recordTimer = null;
    recordDuration.value = 0;
  }

  /// 设置WebSocket消息监听器
  void _setupWebSocketListener() {
    _webSocketInstance.setMessageListener((socketResp) {
      _handleWebSocketMessage(socketResp);
    });
  }

  /// 处理WebSocket接收到的消息
  void _handleWebSocketMessage(SocketResp socketResp) {
    try {
      if (socketResp.body != null && socketResp.body is Map) {
        final body = socketResp.body as Map<String, dynamic>;
        final messageType = body['type'] as String?;
        
        if (messageType == 'chat_reply') {
          // 处理聊天回复消息
          final content = body['content'] as String?;
          final replyType = body['messageType'] as String? ?? 'text';
          final duration = body['duration'] as int?;
          
          if (content != null && content.isNotEmpty) {
            final replyMessage = ChatMessage(
              id: DateTime.now().millisecondsSinceEpoch.toString(),
              content: content,
              type: replyType == 'voice' ? MessageType.voice : MessageType.text,
              sender: MessageSender.other,
              timestamp: DateTime.now(),
              nickname: _botNickname,
              voiceDuration: duration,
            );
            
            messages.add(replyMessage);
            AppLogger.info('收到WebSocket回复消息: $content');
          }
        }
      }
    } catch (e) {
      AppLogger.error('处理WebSocket消息失败', e);
    }
  }

  /// 通过WebSocket发送消息
  void _sendMessageViaWebSocket(String content, String type, [int? duration]) {
    try {
      final agentId = _appStateService.currentAgentId.value;
      final userId = _appStateService.currentUserId;
      
      final messageData = {
        'type': 'chat_message',
        'agentId': agentId.toString(),
        'userId': userId.toString(),
        'content': content,
        'messageType': type,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        if (duration != null) 'duration': duration,
      };

      final socketMessage = {
        'code': 200,
        'message': 'send_message',
        'body': messageData,
      };

      _webSocketInstance.sendMessage(jsonEncode(socketMessage));
      AppLogger.info('消息已通过WebSocket发送: $type - $content');
    } catch (e) {
      AppLogger.error('WebSocket发送消息失败', e);
    }
  }

  /// 模拟收到回复（临时方法，实际应该通过WebSocket或API轮询）
  void _simulateReply() {
    Future.delayed(const Duration(seconds: 2), () {
      final replies = [
        '我收到了你的消息',
        '好的，我明白了',
        '让我想想...',
        '这个问题很有趣',
        '我会帮助你的',
      ];
      
      final reply = replies[DateTime.now().millisecond % replies.length];
      
      final replyMessage = ChatMessage(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        content: reply,
        type: MessageType.text,
        sender: MessageSender.other,
        timestamp: DateTime.now(),
        nickname: _botNickname,
      );
      
      messages.add(replyMessage);
    });
  }

  /// 清空聊天记录
  void clearMessages() {
    messages.clear();
    AppLogger.info('聊天记录已清空');
  }

  /// 刷新聊天历史
  Future<void> refreshChatHistory() async {
    messages.clear();
    await loadChatHistory();
  }
}

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:smartai/features/home/<USER>';
import '../../../core/controllers/theme_controller.dart';
import '../../../routes/app_routes.dart';
import '../../auth/controllers/auth_controller.dart';

class ProfileView extends GetView<HomeController> {
  const ProfileView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 初始化主题控制器
    final themeController = Get.put(ThemeController());

    return Scaffold(
      appBar: AppBar(
        title: const Text('我的'),
        centerTitle: true,
        actions: [
          // 深色模式切换按钮
          Obx(() => IconButton(
                icon: Icon(
                  themeController.isDarkMode
                      ? Icons.light_mode
                      : Icons.dark_mode,
                ),
                onPressed: () => themeController.toggleThemeMode(),
              )),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () => Get.toNamed(Routes.SETTINGS),
          ),
        ],
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return const Center(child: CircularProgressIndicator());
        }
        return _buildProfileContent(context);
      }),
    );
  }

  // 构建个人页面内容
  Widget _buildProfileContent(BuildContext context) {
    return SingleChildScrollView(
      padding: EdgeInsets.symmetric(vertical: 16.r),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // 头像和用户名部分
          _buildUserHeader(context),
          SizedBox(height: 24.h),

          // 快捷设置区域
          _buildQuickSettings(context),

          // 分割线
          Divider(height: 32.h, thickness: 8.h, color: Colors.grey[100]),

          // 设置选项列表
          _buildSettingsOptions(context),
        ],
      ),
    );
  }

  // 头像和用户名部分
  Widget _buildUserHeader(BuildContext context) {
    return GestureDetector(
      onTap: () => Get.toNamed(Routes.PROFILE), // 点击进入个人资料详情页
      child: Column(
        children: [
          // 头像
          Stack(
            alignment: Alignment.bottomRight,
            children: [
              CircleAvatar(
                radius: 50.r,
                backgroundColor: Colors.grey[200],
                child: ClipOval(
                  child: Image.asset(
                    'assets/images/default_avatar.png',
                    width: 100.r,
                    height: 100.r,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) => Icon(
                      Icons.person,
                      size: 50.r,
                      color: Colors.grey[400],
                    ),
                  ),
                ),
              ),
              Container(
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor,
                  shape: BoxShape.circle,
                ),
                padding: EdgeInsets.all(4.r),
                child: Icon(
                  Icons.edit,
                  size: 16.r,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),

          // 用户名
          Obx(() {
            final authController = Get.find<AuthController>();
            return Text(
              authController.user.value?.userName ?? '未登录',
              style: TextStyle(
                fontSize: 20.sp,
                fontWeight: FontWeight.bold,
              ),
            );
          }),
        ],
      ),
    );
  }

  // 快捷设置区域
  Widget _buildQuickSettings(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 24.r),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildQuickSettingItem(
            context,
            icon: Icons.settings,
            label: '系统设置',
            onTap: () => Get.toNamed(Routes.SYSTEM_SETTINGS),
          ),
          _buildQuickSettingItem(
            context,
            icon: Icons.phone_android,
            label: '手机重要设置',
            onTap: () => Get.toNamed(Routes.PHONE_SETTINGS),
          ),
        ],
      ),
    );
  }

  // 快捷设置项
  Widget _buildQuickSettingItem(
    BuildContext context, {
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return Expanded(
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          margin: EdgeInsets.symmetric(horizontal: 8.r),
          padding: EdgeInsets.symmetric(vertical: 16.r),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey[300]!),
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, size: 24.r, color: Colors.grey[600]),
              SizedBox(width: 8.w),
              Text(
                label,
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.grey[800],
                ),
              ),
              Icon(Icons.chevron_right, size: 20.r, color: Colors.grey[400]),
            ],
          ),
        ),
      ),
    );
  }

  // 设置选项列表
  Widget _buildSettingsOptions(BuildContext context) {
    return Column(
      children: [
        _buildSettingItem(
          icon: Icons.devices,
          title: '管理我的设备',
          onTap: () => Get.toNamed(Routes.DEVICE_MANAGEMENT),
        ),
        _buildSettingItem(
          icon: Icons.people,
          title: '绑定成员管理',
          onTap: () => Get.toNamed(Routes.MEMBER_MANAGEMENT),
        ),
        _buildSettingItem(
          icon: Icons.person_add,
          title: '邀请亲密陪伴者',
          onTap: () => Get.toNamed(Routes.AVATAR_INVITE),
        ),
        _buildSettingItem(
          icon: Icons.refresh,
          title: '初始化数据设置',
          onTap: () => Get.toNamed(Routes.DATA_INIT),
        ),
        _buildSettingItem(
          icon: Icons.notifications,
          title: '消息中心',
          onTap: () {
            // 切换到消息tab（索引为1）
            controller.changeTab(1);
          },
        ),
        _buildSettingItem(
          icon: Icons.support_agent,
          title: '联系客服',
          onTap: () => Get.toNamed(Routes.SUPPORT),
        ),
        _buildSettingItem(
          icon: Icons.language,
          title: '语言设置',
          onTap: () => Get.toNamed(Routes.LANGUAGE),
        ),
        _buildAppVersion(),
        _buildSettingItem(
          icon: Icons.privacy_tip,
          title: '隐私政策',
          onTap: () => Get.toNamed(Routes.WEBVIEW,
              arguments: {'title': '隐私政策', 'url': 'https://www.baidu.com'}),
        ),
        _buildSettingItem(
          icon: Icons.description,
          title: '用户协议',
          onTap: () => Get.toNamed(Routes.WEBVIEW,
              arguments: {'title': '用户协议', 'url': 'https://www.baidu.com'}),
        ),
        _buildSettingItem(
          icon: Icons.logout,
          title: '退出登录',
          onTap: () => _showLogoutDialog(context),
        ),
      ],
    );
  }

  // 应用版本信息
  Widget _buildAppVersion() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.r, vertical: 12.r),
      child: Row(
        children: [
          SizedBox(width: 8.w),
          Text(
            'APP版本',
            style: TextStyle(
              fontSize: 16.sp,
              color: Colors.grey[800],
            ),
          ),
          const Spacer(),
          Text(
            'V4.4.3 (226d0b3.1548)',
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.grey[600],
            ),
          ),
          SizedBox(width: 8.w),
        ],
      ),
    );
  }

  // 设置项
  Widget _buildSettingItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.r, vertical: 16.r),
        child: Row(
          children: [
            Icon(icon, color: Colors.grey[600]),
            SizedBox(width: 16.w),
            Text(
              title,
              style: TextStyle(
                fontSize: 16.sp,
                color: Colors.grey[800],
              ),
            ),
            const Spacer(),
            Icon(Icons.chevron_right, size: 24.r, color: Colors.grey[400]),
          ],
        ),
      ),
    );
  }

  // 显示退出登录对话框
  void _showLogoutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('退出登录'),
        content: const Text('确定要退出登录吗？'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              Get.find<AuthController>().logout();
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }
}

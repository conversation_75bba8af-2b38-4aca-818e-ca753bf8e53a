import 'dart:math';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:smartai/features/home/<USER>/message_controller_new.dart';
import 'message_binding.dart';
import '../../../shared/widgets/simple_message_input.dart';
import '../../../shared/widgets/common_widgets.dart';

class MsgView extends StatefulWidget {
  const MsgView({Key? key}) : super(key: key);

  @override
  State<MsgView> createState() => _MsgViewState();
}

class _MsgViewState extends State<MsgView> {
  late MessageController controller;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    // 初始化控制器
    MessageBinding().dependencies();
    controller = Get.find<MessageController>();

    // 添加消息列表变化监听，自动滚动到底部
    controller.messages.listen((_) {
      _scrollToBottom();
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  // 滚动到底部
  void _scrollToBottom() {
    if (_scrollController.hasClients) {
      Future.delayed(const Duration(milliseconds: 100), () {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: Column(
        children: [
          // 消息列表
          Expanded(
            child: _buildMessageList(),
          ),

          // 录音取消提示
          _buildRecordingCancelHint(),

          // 录音状态提示
          _buildRecordingStatusHint(),

          // 输入区域
          SimpleMessageInput(
            onSendText: (text) {
              controller.sendTextMessage(text);
            },
            onSendVoice: (audioPath, duration) {
              if (audioPath.isNotEmpty) {
                controller.sendVoiceMessage(audioPath);
              }
            },
            onRecordingStateChanged: (isRecording, showCancel) {
              controller.isRecording.value = isRecording;
              controller.showCancelRecording.value = showCancel;
            },
            onRecordingTimeChanged: (time) {
              controller.recordDuration.value = time;
            },
          ),
        ],
      ),
    );
  }

  // 构建应用栏
  AppBar _buildAppBar() {
    return AppBar(
      title: Text('message'.tr),
      centerTitle: true,
      backgroundColor: Colors.white,
      foregroundColor: Colors.black,
      elevation: 0.5,
      actions: [
        IconButton(
          icon: const Icon(Icons.more_vert),
          onPressed: () => _showMoreOptions(),
        ),
      ],
    );
  }

  // 显示更多选项
  void _showMoreOptions() {
    Get.bottomSheet(
      Container(
        padding: EdgeInsets.all(16.r),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(
            top: Radius.circular(16.r),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.delete_outline),
              title: Text('clear_messages'.tr),
              onTap: () {
                Get.back();
                _showClearConfirmDialog();
              },
            ),
            ListTile(
              leading: const Icon(Icons.settings_outlined),
              title: Text('settings'.tr),
              onTap: () {
                Get.back();
                // 打开聊天设置页面
              },
            ),
          ],
        ),
      ),
    );
  }

  // 显示清空聊天记录确认对话框
  void _showClearConfirmDialog() {
    Get.dialog(
      AlertDialog(
        title: Text('confirm'.tr),
        content: Text('clear_confirm'.tr),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text('cancel'.tr),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              controller.messages.clear();
            },
            child: Text('ok'.tr),
          ),
        ],
      ),
    );
  }

  // 构建消息列表
  Widget _buildMessageList() {
    return Obx(() {
      if (controller.isLoading.value && controller.messages.isEmpty) {
        return LoadingWidget();
      }

      if (controller.messages.isEmpty) {
        return EmptyStateWidget(
          message: 'no_messages'.tr,
          icon: Icons.chat_bubble_outline,
        );
      }

      return ListView.builder(
        controller: _scrollController,
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
        itemCount: controller.messages.length,
        itemBuilder: (context, index) {
          final message = controller.messages[index];
          final showTime = _shouldShowTime(index);

          return Column(
            children: [
              if (showTime) _buildTimeWidget(message.timestamp),
              MessageItem(message: message),
            ],
          );
        },
      );
    });
  }

  // 判断是否应该显示时间
  bool _shouldShowTime(int index) {
    if (index == 0) return true;

    final currentMessage = controller.messages[index];
    final previousMessage = controller.messages[index - 1];

    // 如果两条消息相差超过5分钟，显示时间
    final timeDifference =
        currentMessage.timestamp.difference(previousMessage.timestamp);
    return timeDifference.inMinutes >= 5;
  }

  // 构建时间组件
  Widget _buildTimeWidget(DateTime timestamp) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final messageDate =
        DateTime(timestamp.year, timestamp.month, timestamp.day);

    String timeText;
    if (messageDate == today) {
      // 今天的消息，仅显示时间
      timeText =
          '${timestamp.hour.toString().padLeft(2, '0')}:${timestamp.minute.toString().padLeft(2, '0')}';
    } else if (messageDate == yesterday) {
      // 昨天的消息
      timeText =
          '${'yesterday'.tr} ${timestamp.hour.toString().padLeft(2, '0')}:${timestamp.minute.toString().padLeft(2, '0')}';
    } else {
      // 更早的消息，显示完整日期和时间
      timeText = DateFormat('yyyy-MM-dd HH:mm').format(timestamp);
    }

    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8.h),
      child: Center(
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 4.h),
          decoration: BoxDecoration(
            color: Colors.grey[200],
            borderRadius: BorderRadius.circular(12.r),
          ),
          child: Text(
            timeText,
            style: TextStyle(
              fontSize: 12.sp,
              color: Colors.grey[600],
            ),
          ),
        ),
      ),
    );
  }

  // 构建录音取消提示
  Widget _buildRecordingCancelHint() {
    return Obx(() {
      if (!controller.isRecording.value ||
          !controller.showCancelRecording.value) {
        return const SizedBox.shrink();
      }

      return Container(
        width: double.infinity,
        padding: EdgeInsets.symmetric(vertical: 16.h),
        color: Colors.red.withOpacity(0.1),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.delete_forever, color: Colors.red, size: 36.sp),
            SizedBox(height: 8.h),
            Text(
              'slide_to_cancel'.tr,
              style: TextStyle(
                color: Colors.red,
                fontSize: 14.sp,
              ),
            ),
          ],
        ),
      );
    });
  }

  // 构建录音状态提示
  Widget _buildRecordingStatusHint() {
    return Obx(() {
      if (!controller.isRecording.value ||
          controller.showCancelRecording.value) {
        return const SizedBox.shrink();
      }

      return Container(
        width: double.infinity,
        padding: EdgeInsets.symmetric(vertical: 16.h),
        color: Colors.grey.withOpacity(0.1),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                RecordingAnimationDots(),
                SizedBox(width: 8.w),
                Text(
                  '${'recording'.tr}: ${controller.recordDuration.value}s',
                  style: TextStyle(
                    color: Colors.grey[700],
                    fontSize: 14.sp,
                  ),
                ),
              ],
            ),
            SizedBox(height: 8.h),
            Text(
              'slide_to_cancel'.tr,
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 12.sp,
              ),
            ),
          ],
        ),
      );
    });
  }
}

// 录音动画点
class RecordingAnimationDots extends StatefulWidget {
  const RecordingAnimationDots({Key? key}) : super(key: key);

  @override
  State<RecordingAnimationDots> createState() => _RecordingAnimationDotsState();
}

class _RecordingAnimationDotsState extends State<RecordingAnimationDots>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    )..repeat();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: List.generate(3, (index) {
            final animation = CurvedAnimation(
              parent: _animationController,
              curve: Interval(
                index * 0.3, // 错开每个点的动画起始时间
                index * 0.3 + 0.5,
                curve: Curves.easeInOut,
              ),
            );

            return Container(
              margin: EdgeInsets.symmetric(horizontal: 2.w),
              width: 6.w,
              height: 6.h,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.red.withOpacity(0.2 + animation.value * 0.8),
              ),
            );
          }),
        );
      },
    );
  }
}

// 消息项组件
class MessageItem extends StatelessWidget {
  final dynamic message;

  const MessageItem({Key? key, required this.message}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bool isMe = message.isMine;

    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4.h),
      child: Row(
        mainAxisAlignment:
            isMe ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 头像 (仅在非我发送的消息显示)
          if (!isMe) _buildAvatar(),

          SizedBox(width: isMe ? 0 : 8.w),

          // 消息内容
          Flexible(
            child: Column(
              crossAxisAlignment: isMe ? CrossAxisAlignment.end : CrossAxisAlignment.start,
              children: [
                // 显示昵称
                if (!isMe) // 只在非自己发送的消息显示昵称
                  Padding(
                    padding: EdgeInsets.only(left: 8.w, bottom: 4.h),
                    child: Text(
                      message.displayNickname,
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: Colors.grey[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                // 消息气泡
                Container(
                  constraints: BoxConstraints(
                    maxWidth: 0.7.sw, // 限制气泡最大宽度为屏幕宽度的70%
                  ),
                  decoration: BoxDecoration(
                    color: isMe ? Colors.blue : Colors.grey[200],
                    borderRadius: BorderRadius.circular(16.r),
                  ),
                  padding: EdgeInsets.all(12.r),
                  child: _buildMessageContent(),
                ),
              ],
            ),
          ),

          SizedBox(width: isMe ? 8.w : 0),

          // 头像 (仅在我发送的消息显示)
          if (isMe) _buildAvatar(isMe: true),
        ],
      ),
    );
  }

  // 构建头像
  Widget _buildAvatar({bool isMe = false}) {
    return CircleAvatar(
      radius: 18.r,
      backgroundColor: isMe ? Colors.blue[100] : Colors.grey[300],
      child: Icon(
        isMe ? Icons.person : Icons.pets,
        size: 20.r,
        color: isMe ? Colors.blue[800] : Colors.grey[700],
      ),
    );
  }

  // 构建消息内容
  Widget _buildMessageContent() {
    if (message.typeString == 'text') {
      return Text(
        message.content,
        style: TextStyle(
          fontSize: 16.sp,
          color: message.isMine ? Colors.white : Colors.black,
        ),
      );
    } else if (message.typeString == 'voice') {
      return VoiceMessageContent(
        duration: message.voiceDuration ?? 0,
        isPlaying:  false,
        isMine: message.isMine,
        onTap: () {
          // 播放语音消息
          final controller = Get.find<MessageController>();
          controller.playVoiceMessage(message);
        },
      );
    } else {
      return Text(
        'unknown'.tr,
        style: TextStyle(
          fontSize: 16.sp,
          color: message.isMine ? Colors.white : Colors.black,
        ),
      );
    }
  }
}

// 语音消息内容组件
class VoiceMessageContent extends StatelessWidget {
  final int duration;
  final bool isPlaying;
  final bool isMine;
  final VoidCallback onTap;

  const VoiceMessageContent({
    Key? key,
    required this.duration,
    required this.isPlaying,
    required this.isMine,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 播放图标
          Icon(
            isPlaying ? Icons.pause : Icons.play_arrow,
            color: isMine ? Colors.white : Colors.blue,
            size: 24.sp,
          ),

          SizedBox(width: 8.w),

          // 波形图或时长指示
          if (isPlaying)
            SoundWaveAnimation(isMine: isMine)
          else
            Text(
              '${duration}s',
              style: TextStyle(
                fontSize: 14.sp,
                color: isMine ? Colors.white70 : Colors.grey[600],
              ),
            ),
        ],
      ),
    );
  }
}

// 声波动画组件
class SoundWaveAnimation extends StatefulWidget {
  final bool isMine;

  const SoundWaveAnimation({Key? key, required this.isMine}) : super(key: key);

  @override
  State<SoundWaveAnimation> createState() => _SoundWaveAnimationState();
}

class _SoundWaveAnimationState extends State<SoundWaveAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  final List<double> _barHeights = [];

  @override
  void initState() {
    super.initState();

    // 生成随机高度条
    for (int i = 0; i < 5; i++) {
      _barHeights.add(Random().nextDouble() * 10 + 5);
    }

    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600),
    )..repeat(reverse: true);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 40.w,
      height: 20.h,
      child: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          return Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: List.generate(_barHeights.length, (index) {
              // 使用正弦函数创建波动效果，错开每个条的相位
              final wave = sin((_controller.value * pi) + (index * 0.5));
              final height =
                  (_barHeights[index] * wave.abs() + 5).clamp(3.0, 15.0);

              return Container(
                width: 3.w,
                height: height,
                decoration: BoxDecoration(
                  color: widget.isMine ? Colors.white70 : Colors.blue[300],
                  borderRadius: BorderRadius.circular(1.r),
                ),
              );
            }),
          );
        },
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../home_controller.dart';

class HomePageView extends GetView<HomeController> {
  const HomePageView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      body: SafeArea(
        child: Obx(() => Column(
          children: [
            _buildDeviceStatusCard(),
            Expanded(child: _buildAvatarArea()),
            _buildControlButtons(),
          ],
        )),
      ),
    );
  }

  /// 构建WiFi连接状态
  Widget _buildWifiStatus() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
      child: Row(
        children: [
          Icon(
            Icons.wifi,
            size: 20,
            color: controller.isWifiConnected ? Colors.green : Colors.grey,
          ),
          const SizedBox(width: 8),
          Text(
            controller.isWifiConnected ? '已连接' : '未连接',
            style: TextStyle(
              fontSize: 14,
              color: controller.isWifiConnected ? Colors.green : Colors.grey[600],
            ),
          ),
          const Spacer(),
          Icon(
            Icons.arrow_forward_ios,
            size: 16,
            color: Colors.grey[400],
          ),
        ],
      ),
    );
  }

  /// 构建设备状态卡片
  Widget _buildDeviceStatusCard() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
      child: Column(
        children: [
          _buildWifiStatus(),
          const SizedBox(height: 12),
          _buildRatingAndStats(),
        ],
      ),
    );
  }

  /// 构建评分和统计信息
  Widget _buildRatingAndStats() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(25),
      ),
      child: Column(
        children: [
          // 星级评分
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ...List.generate(5, (index) => Icon(
                index < 3 ? Icons.star : Icons.star_border,
                color: index < 3 ? Colors.amber : Colors.grey[400],
                size: 28,
              )),
              const SizedBox(width: 8),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: Colors.grey[400],
              ),
            ],
          ),
          const SizedBox(height: 16),
          // 电池和音量信息
          Row(
            children: [
              _buildStatusItem(
                Icons.battery_std,
                '${controller.homeData.value.batteryLevel ?? 67}%',
              ),
              const SizedBox(height: 12),
              _buildStatusItem(
                Icons.volume_up,
                '${controller.homeData.value.systemVolume ?? 80}%',
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建状态项
  Widget _buildStatusItem(IconData icon, String text) {
    return Row(
      children: [
        Icon(icon, size: 20, color: Colors.black87),
        const SizedBox(width: 8),
        Text(
          text,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
      ],
    );
  }

  /// 构建分身形象区域
  Widget _buildAvatarArea() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Center(
        child: Container(
          width: 280,
          height: 350,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Stack(
            children: [
              // 分身形象
              Center(
                child: Container(
                  width: 200,
                  height: 250,
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: BorderRadius.circular(15),
                  ),
                  child: const Center(
                    child: Text(
                      '分身形象\n(待实现)',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey,
                      ),
                    ),
                  ),
                ),
              ),
              // 交互点位（圆形按钮）
              ..._buildInteractionPoints(),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建交互点位
  List<Widget> _buildInteractionPoints() {
    return [
      // 头部点位
      Positioned(
        top: 30,
        left: 140,
        child: _buildInteractionPoint(),
      ),
      // 左耳点位
      Positioned(
        top: 80,
        left: 60,
        child: _buildInteractionPoint(),
      ),
      // 右耳点位
      Positioned(
        top: 80,
        right: 60,
        child: _buildInteractionPoint(),
      ),
      // 胸部点位
      Positioned(
        bottom: 120,
        left: 140,
        child: _buildInteractionPoint(),
      ),
      // 左手点位
      Positioned(
        bottom: 60,
        left: 40,
        child: _buildInteractionPoint(),
      ),
      // 右手点位
      Positioned(
        bottom: 60,
        right: 40,
        child: _buildInteractionPoint(),
      ),
    ];
  }

  /// 构建单个交互点
  Widget _buildInteractionPoint() {
    return GestureDetector(
      onTap: () => _handleInteraction(),
      child: Container(
        width: 24,
        height: 24,
        decoration: BoxDecoration(
          color: Colors.grey[600],
          shape: BoxShape.circle,
          border: Border.all(color: Colors.white, width: 2),
        ),
      ),
    );
  }

  /// 构建控制按钮
  Widget _buildControlButtons() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildControlButton(
            Icons.notifications_off,
            '静音',
            () => _toggleMute(),
          ),
          _buildControlButton(
            Icons.nightlight_round,
            '夜间',
            () => _toggleNightMode(),
          ),
        ],
      ),
    );
  }

  /// 构建控制按钮
  Widget _buildControlButton(IconData icon, String label, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: Colors.grey[700],
              size: 24,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }



  /// 处理交互
  void _handleInteraction() {
    controller.handleAvatarInteraction();
  }

  /// 切换静音
  void _toggleMute() {
    controller.toggleMute();
  }

  /// 切换夜间模式
  void _toggleNightMode() {
    controller.toggleNightMode();
  }
}

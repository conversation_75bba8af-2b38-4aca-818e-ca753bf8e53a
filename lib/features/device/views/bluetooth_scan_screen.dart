import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:smartai/features/device/controllers/bluetooth_scan_controller.dart';

/// 简化的蓝牙扫描页面 - 清晰的状态展示
class BluetoothScanScreen extends StatelessWidget {
  const BluetoothScanScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<BluetoothScanController>();

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text('添加设备'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: () => Get.back(),
          ),
        ],
      ),
      body: SafeArea(
        child: Obx(() {
          // 扫描中状态（最高优先级）
          if (controller.isScanning.value) {
            return _buildScanningView();
          }

          // 权限被永久拒绝状态
          if (controller.showPermissionDenied.value) {
            return _buildPermissionDeniedView(controller);
          }

          // 设备列表状态
          if (controller.discoveredDevices.isEmpty) {
            return _buildEmptyView(controller);
          }

          return _buildDeviceList(controller);
        }),
      ),
    );
  }

  /// 扫描中界面
  Widget _buildScanningView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(color: Colors.blue),
          SizedBox(height: 24.h),
          Text(
            '正在扫描设备...',
            style: TextStyle(fontSize: 18.sp, fontWeight: FontWeight.w500),
          ),
          SizedBox(height: 8.h),
          Text(
            '请确保设备已开启并处于配网模式',
            style: TextStyle(fontSize: 14.sp, color: Colors.grey.shade600),
          ),
        ],
      ),
    );
  }

  /// 权限被拒绝界面
  Widget _buildPermissionDeniedView(BluetoothScanController controller) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(32.r),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.bluetooth_disabled, size: 80.r, color: Colors.grey.shade400),
            SizedBox(height: 24.h),
            Text(
              '需要蓝牙权限',
              style: TextStyle(fontSize: 20.sp, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 12.h),
            Text(
              '请在系统设置中开启蓝牙权限以扫描设备',
              style: TextStyle(fontSize: 16.sp, color: Colors.grey.shade600),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 32.h),
            SizedBox(
              width: double.infinity,
              height: 48.h,
              child: ElevatedButton(
                onPressed: controller.openSystemSettings,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8.r)),
                ),
                child: Text('打开设置', style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w500)),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 空设备列表界面
  Widget _buildEmptyView(BluetoothScanController controller) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(32.r),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.bluetooth_searching, size: 80.r, color: Colors.grey.shade400),
            SizedBox(height: 24.h),
            Text(
              '未发现设备',
              style: TextStyle(fontSize: 18.sp, fontWeight: FontWeight.w500),
            ),
            SizedBox(height: 12.h),
            Text(
              '请确保设备已开启并处于配网模式',
              style: TextStyle(fontSize: 14.sp, color: Colors.grey.shade600),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 32.h),
            SizedBox(
              width: double.infinity,
              height: 50.h,
              child: ElevatedButton.icon(
                onPressed: controller.startScan,
                icon: Icon(Icons.refresh, size: 20.r),
                label: Text('重新扫描', style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w500)),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8.r)),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 设备列表界面
  Widget _buildDeviceList(BluetoothScanController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.all(24.r),
          child: Text(
            '发现 ${controller.discoveredDevices.length} 个设备',
            style: TextStyle(fontSize: 16.sp, color: Colors.grey.shade600),
          ),
        ),
        Expanded(
          child: ListView.builder(
            padding: EdgeInsets.symmetric(horizontal: 24.r),
            itemCount: controller.discoveredDevices.length,
            itemBuilder: (context, index) {
              final device = controller.discoveredDevices[index];
              return _buildDeviceItem(device, controller);
            },
          ),
        ),
        Padding(
          padding: EdgeInsets.all(24.r),
          child: SizedBox(
            width: double.infinity,
            height: 50.h,
            child: OutlinedButton.icon(
              onPressed: controller.startScan,
              icon: Icon(Icons.refresh, size: 20.r),
              label: Text('重新扫描', style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w500)),
              style: OutlinedButton.styleFrom(
                foregroundColor: Colors.blue,
                side: BorderSide(color: Colors.blue),
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8.r)),
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 设备项
  Widget _buildDeviceItem(device, BluetoothScanController controller) {
    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      padding: EdgeInsets.all(16.r),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: Colors.grey.shade300),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            width: 48.r,
            height: 48.r,
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Icon(Icons.bluetooth, color: Colors.blue, size: 24.r),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  device.name,
                  style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w500),
                ),
                SizedBox(height: 4.h),
                Text(
                  'MAC: ${device.address}',
                  style: TextStyle(fontSize: 12.sp, color: Colors.grey.shade600),
                ),
              ],
            ),
          ),
          Obx(() => SizedBox(
            width: 80.w,
            height: 36.h,
            child: ElevatedButton(
              onPressed: controller.isConnecting.value 
                  ? null 
                  : () => controller.connectToDevice(device),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(18.r)),
                padding: EdgeInsets.zero,
              ),
              child: controller.isConnecting.value
                  ? SizedBox(
                      width: 16.r,
                      height: 16.r,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Text('添加', style: TextStyle(fontSize: 14.sp, fontWeight: FontWeight.w500)),
            ),
          )),
        ],
      ),
    );
  }
}

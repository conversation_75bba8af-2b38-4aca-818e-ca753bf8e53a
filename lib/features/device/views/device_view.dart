import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../routes/app_routes.dart';
import '../../../shared/widgets/common_widgets.dart';

class DeviceView extends StatelessWidget {
  const DeviceView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('device_management'.tr),
        centerTitle: true,
      ),
      body: SafeArea(
        child: Center(
          child: EmptyStateWidget(
            message: 'no_device_found'.tr,
            icon: Icons.devices_other,
            onRefresh: () {
              // 跳转到蓝牙扫描页面
              Get.toNamed(Routes.BLUETOOTH_SCAN);
            },
          ),
        ),
      ),
    );
  }
}

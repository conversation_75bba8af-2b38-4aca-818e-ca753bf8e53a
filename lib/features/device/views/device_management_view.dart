import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../data/models/clone_device_info_model.dart';
import '../controllers/device_management_controller.dart';
import '../../../data/models/device_model.dart';

class DeviceManagementView extends GetView<DeviceManagementController> {
  const DeviceManagementView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('我的设备'),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () => Get.back(),
        ),
      ),
      body: SafeArea(
        child: Obx(() {
          if (controller.isLoading.value) {
            return const Center(child: CircularProgressIndicator());
          }

          return Column(
            children: [
              // 设备列表
              Expanded(
                child: ListView.builder(
                  padding: EdgeInsets.symmetric(vertical: 16.r),
                  itemCount: controller.devices.length,
                  itemBuilder: (context, index) {
                    return _buildDeviceCard(context, controller.devices[index]);
                  },
                ),
              ),

              // 添加设备按钮
              Padding(
                padding: EdgeInsets.all(16.r),
                child: _buildAddDeviceButton(context),
              ),
            ],
          );
        }),
      ),
    );
  }

  // 设备卡片
  Widget _buildDeviceCard(BuildContext context, CloneDeviceInfo device) {
    return GestureDetector(
      onTap: () => controller.viewDeviceDetail(device.agentId!),
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: 16.r, vertical: 8.r),
        padding: EdgeInsets.all(16.r),
        decoration: BoxDecoration(
          color: Colors.grey[100],
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Row(
          children: [
            // 设备图标/图片
            Container(
              width: 60.r,
              height: 60.r,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Center(
                child: Icon(
                  Icons.devices,
                  size: 30.r,
                  color: Colors.grey[600],
                ),
              ),
            ),
            SizedBox(width: 16.w),

            // 设备信息
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    device.dollName??"未命名设备",
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 4.h),
                  Row(
                    children: [
                      // Icon(
                      //   device.isConnected ? Icons.link : Icons.link_off,
                      //   size: 14.r,
                      //   color: device.isConnected ? Colors.green : Colors.grey,
                      // ),
                      SizedBox(width: 4.w),
                      Text('已连接',
                        // device.isConnected ? '已连接' : '未连接',
                        style: TextStyle(
                          fontSize: 12.sp,
                          color:
                              true? Colors.green : Colors.grey,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // 详情图标
            Icon(
              Icons.chevron_right,
              size: 24.r,
              color: Colors.grey[400],
            ),
          ],
        ),
      ),
    );
  }

  // 添加设备按钮
  Widget _buildAddDeviceButton(BuildContext context) {
    return GestureDetector(
      onTap: controller.addNewDevice,
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.symmetric(vertical: 16.r),
        decoration: BoxDecoration(
          color: Colors.grey[200],
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.add,
              size: 24.r,
              color: Colors.grey[700],
            ),
            SizedBox(width: 8.w),
            Text(
              '添加设备',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w500,
                color: Colors.grey[700],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

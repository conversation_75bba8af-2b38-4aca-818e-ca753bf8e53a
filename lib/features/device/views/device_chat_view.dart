import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/device_controller.dart';
import '../../../shared/widgets/message_input.dart';
import '../../../shared/widgets/voice_message_bubble.dart';
import '../../../core/utils/logger.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class DeviceChatView extends GetView<DeviceController> {
  const DeviceChatView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 假设这些是聊天消息数据
    final messages = [
      {
        'id': '1',
        'type': 'text',
        'content': '你好！',
        'sender': 'user',
        'timestamp': DateTime.now().subtract(const Duration(minutes: 5)),
      },
      {
        'id': '2',
        'type': 'voice',
        'content':
            'https://audio-samples.github.io/samples/mp3/blizzard_biased/sample-1.mp3',
        'duration': 3,
        'sender': 'device',
        'timestamp': DateTime.now().subtract(const Duration(minutes: 4)),
      },
      {
        'id': '3',
        'type': 'text',
        'content': '今天天气怎么样？',
        'sender': 'user',
        'timestamp': DateTime.now().subtract(const Duration(minutes: 3)),
      },
      {
        'id': '4',
        'type': 'voice',
        'content':
            'https://audio-samples.github.io/samples/mp3/blizzard_biased/sample-2.mp3',
        'duration': 5,
        'sender': 'device',
        'timestamp': DateTime.now().subtract(const Duration(minutes: 2)),
      },
    ];

    return Scaffold(
      appBar: AppBar(
        title: const Text('设备聊天'),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.more_vert),
            onPressed: () {
              // 显示更多选项
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // 消息列表
          Expanded(
            child: ListView.builder(
              reverse: true,
              padding: EdgeInsets.symmetric(vertical: 16.r),
              itemCount: messages.length,
              itemBuilder: (context, index) {
                // 反转索引以显示最新的消息在底部
                final reversedIndex = messages.length - 1 - index;
                final message = messages[reversedIndex];
                final isMe = message['sender'] == 'user';

                if (message['type'] == 'text') {
                  return _buildTextMessage(
                    message['content'].toString(),
                    isMe,
                    message['timestamp'] as DateTime,
                  );
                } else if (message['type'] == 'voice') {
                  return VoiceMessageBubble(
                    audioUrl: message['content'].toString(),
                    durationInSeconds: message['duration'] as int,
                    isMe: isMe,
                    timestamp: message['timestamp'] as DateTime,
                  );
                }
                return const SizedBox.shrink();
              },
            ),
          ),

          // 输入区域
          MessageInputBar(
            onSendText: (text) {
              // 处理发送文本消息
              controller.sendTextMessage(text);
            },
            onSendVoice: (audioPath) {
              // 处理发送语音消息
              controller.sendVoiceMessage(audioPath);
            },
            hintText: '说点什么...',
            primaryColor: Theme.of(context).primaryColor,
          ),
        ],
      ),
    );
  }

  // 构建文本消息气泡
  Widget _buildTextMessage(String text, bool isMe, DateTime timestamp) {
    final bubbleColor = isMe ? Get.theme.primaryColor : const Color(0xFFEEEEEE);
    final textColor = isMe ? Colors.white : Colors.black87;

    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4.r, horizontal: 16.r),
      child: Row(
        mainAxisAlignment:
            isMe ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          if (!isMe) _buildAvatar(isMe),
          Flexible(
            child: Container(
              constraints: BoxConstraints(
                maxWidth: Get.width * 0.7,
              ),
              padding: EdgeInsets.symmetric(horizontal: 12.r, vertical: 8.r),
              decoration: BoxDecoration(
                color: bubbleColor,
                borderRadius: BorderRadius.circular(16.r),
              ),
              child: Text(
                text,
                style: TextStyle(
                  color: textColor,
                  fontSize: 16.sp,
                ),
              ),
            ),
          ),
          if (isMe) _buildAvatar(isMe),
        ],
      ),
    );
  }

  // 构建头像
  Widget _buildAvatar(bool isMe) {
    return Padding(
      padding: EdgeInsets.only(left: 8.r, right: 8.r, bottom: 4.r),
      child: CircleAvatar(
        radius: 16.r,
        backgroundColor: isMe ? Get.theme.primaryColor : Colors.grey[300],
        child: Icon(
          isMe ? Icons.person : Icons.devices,
          size: 18.r,
          color: isMe ? Colors.white : Colors.grey[700],
        ),
      ),
    );
  }
}

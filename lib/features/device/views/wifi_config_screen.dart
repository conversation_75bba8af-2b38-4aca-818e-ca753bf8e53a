import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../controllers/wifi_config_controller.dart';

/// WiFi配网页面 - 简洁清晰的配网界面
class WifiConfigScreen extends StatelessWidget {
  const WifiConfigScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(WifiConfigController());

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text('设置WiFi网络'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: () => Get.back(),
          ),
        ],
      ),
      body: SafeArea(
        child: Obx(() {
          switch (controller.currentState.value) {
            case WifiConfigState.scanning:
              return _buildScanningView();
            case WifiConfigState.ready:
              return _buildConfigView(controller);
            case WifiConfigState.configuring:
              return _buildConfiguringView(controller);
            case WifiConfigState.success:
              return _buildSuccessView();
            case WifiConfigState.failed:
              return _buildConfigView(controller);
            default:
              return _buildConfigView(controller);
          }
        }),
      ),
    );
  }

  /// WiFi扫描中界面
  Widget _buildScanningView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(
            color: Colors.blue,
            strokeWidth: 3,
          ),
          SizedBox(height: 24.h),
          Text(
            'WiFi扫描中...',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.w500,
              color: Colors.black,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            '正在搜索可用的WiFi网络',
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }

  /// WiFi配置界面
  Widget _buildConfigView(WifiConfigController controller) {
    return Padding(
      padding: EdgeInsets.all(24.r),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 提示文字
          Center(
            child: Text(
              '仅支持2.4GHz WiFi',
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.grey.shade600,
              ),
            ),
          ),

          SizedBox(height: 32.h),

          // WiFi选择区域
          Container(
            padding: EdgeInsets.all(16.r),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12.r),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: Row(
              children: [
                Icon(Icons.wifi, color: Colors.grey.shade600, size: 24.r),
                SizedBox(width: 12.w),
                Expanded(
                  child: Obx(() => Text(
                    controller.selectedSsid.value.isEmpty 
                        ? 'WiFi' 
                        : controller.selectedSsid.value,
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: controller.selectedSsid.value.isEmpty 
                          ? Colors.grey.shade500 
                          : Colors.black,
                    ),
                  )),
                ),
                TextButton(
                  onPressed: () => _showWifiListDialog(controller),
                  child: Text(
                    '选择WiFi',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: Colors.blue,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),

          SizedBox(height: 24.h),

          // 密码输入区域
          Container(
            padding: EdgeInsets.all(16.r),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12.r),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: Row(
              children: [
                Icon(Icons.lock_outline, color: Colors.grey.shade600, size: 24.r),
                SizedBox(width: 12.w),
                Expanded(
                  child: Obx(() => TextField(
                    controller: controller.passwordController,
                    obscureText: !controller.showPassword.value,
                    decoration: InputDecoration(
                      hintText: '密码',
                      border: const OutlineInputBorder(
                        borderSide: BorderSide.none, // 去掉边框线条
                      ),
                      enabledBorder: const OutlineInputBorder(
                        borderSide: BorderSide.none, // 去掉默认启用状态的边框
                      ),
                      focusedBorder: const OutlineInputBorder(
                        borderSide: BorderSide.none, // 去掉聚焦状态的边框
                      ),
                      hintStyle: TextStyle(
                        color: Colors.grey.shade500,
                        fontSize: 16.sp,
                      ),
                    ),
                    style: TextStyle(fontSize: 16.sp),
                  )),
                ),
                IconButton(
                  onPressed: controller.togglePasswordVisibility,
                  icon: Obx(() => Icon(
                    controller.showPassword.value 
                        ? Icons.visibility_off 
                        : Icons.visibility,
                    color: Colors.grey.shade600,
                    size: 20.r,
                  )),
                ),
              ],
            ),
          ),

          SizedBox(height: 16.h),

          // 记住密码选项
          Obx(() => Row(
            children: [
              Checkbox(
                value: controller.rememberPassword.value,
                onChanged: (value) => controller.rememberPassword.value = value ?? false,
                activeColor: Colors.blue,
              ),
              Text(
                '记住密码',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.grey.shade700,
                ),
              ),
            ],
          )),

          const Spacer(),

          // 确定按钮
          SizedBox(
            width: double.infinity,
            height: 50.h,
            child: Obx(() => ElevatedButton(
              onPressed: controller.selectedSsid.value.isEmpty 
                  ? null 
                  : controller.startWifiConfiguration,
              style: ElevatedButton.styleFrom(
                backgroundColor: controller.selectedSsid.value.isEmpty 
                    ? Colors.grey.shade300 
                    : Colors.grey.shade800,
                foregroundColor: controller.selectedSsid.value.isEmpty 
                    ? Colors.grey.shade600 
                    : Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.r),
                ),
                elevation: 0,
              ),
              child: Text(
                '确定',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w500,
                ),
              ),
            )),
          ),
        ],
      ),
    );
  }

  /// 配网中界面
  Widget _buildConfiguringView(WifiConfigController controller) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(
            color: Colors.blue,
            strokeWidth: 3,
          ),
          SizedBox(height: 24.h),
          Text(
            '正在配置WiFi网络',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.w500,
              color: Colors.black,
            ),
          ),
          SizedBox(height: 8.h),
          Obx(() => Text(
            '正在将设备连接到 ${controller.selectedSsid.value}',
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.grey.shade600,
            ),
            textAlign: TextAlign.center,
          )),
        ],
      ),
    );
  }

  /// 配网成功界面
  Widget _buildSuccessView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.check_circle,
            size: 80.r,
            color: Colors.green,
          ),
          SizedBox(height: 24.h),
          Text(
            '配网成功！',
            style: TextStyle(
              fontSize: 20.sp,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            '设备已成功连接到WiFi网络',
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }

  /// 显示WiFi列表对话框
  void _showWifiListDialog(WifiConfigController controller) {
    Get.dialog(
      Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.r),
        ),
        child: Container(
          width: double.infinity,
          height: 400.h,
          padding: EdgeInsets.all(16.r),
          child: Column(
            children: [
              // 标题
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '选择WiFi网络',
                    style: TextStyle(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  IconButton(
                    onPressed: () => Get.back(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
              
              SizedBox(height: 16.h),
              
              // WiFi列表
              Expanded(
                child: Obx(() => ListView.builder(
                  itemCount: controller.availableNetworks.length,
                  itemBuilder: (context, index) {
                    final network = controller.availableNetworks[index];
                    return ListTile(
                      leading: const Icon(Icons.wifi),
                      title: Text(network.ssid),
                      trailing: network.isSecured 
                          ? Icon(Icons.lock_outline, size: 16.r)
                          : null,
                      onTap: () {
                        controller.selectWifi(network.ssid);
                        Get.back();
                      },
                    );
                  },
                )),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

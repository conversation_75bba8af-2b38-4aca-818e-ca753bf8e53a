import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../controllers/device_detail_controller.dart';
import '../controllers/device_management_controller.dart';

class DeviceDetailView extends GetView<DeviceDetailController> {
  const DeviceDetailView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('产品名称'),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () => Get.back(),
        ),
      ),
      body: SafeArea(
        child: Obx(() {
          if (controller.isLoading.value) {
            return const Center(child: CircularProgressIndicator());
          }

          if (controller.device.value==null) {
            return const Center(child: Text('无法加载设备信息'));
          }

          final device = controller.device.value!;

          return SingleChildScrollView(
            padding: EdgeInsets.all(16.r),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // 产品图片
                Container(
                  width: 250.r,
                  height: 250.r,
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    border: Border.all(color: Colors.grey[300]!),
                  ),
                  child: Center(
                    child: Text(
                      '产品图',
                      style: TextStyle(
                        fontSize: 18.sp,
                        color: Colors.grey[600],
                      ),
                    ),
                  ),
                ),
                SizedBox(height: 24.h),

                // 产品名称
                Text(
                  '产品名称',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 32.h),

                // 设备信息列表
                _buildInfoItem('升级AI人偶设备',
                    device.version ?? ''),
                _buildDivider(),

                _buildInfoItem('AI人偶蓝牙名称',
                    device.wifiName ?? ''),
                _buildDivider(),

                _buildInfoItem('MAC地址※',
                    device.deviceId ?? ''),
                _buildDivider(),

                _buildInfoItem('主板名称※',
                    device.chipModelName ?? ''),
                _buildDivider(),

                _buildInfoItem('主板版本※',
                    device.version ?? ''),
                _buildDivider(),

                _buildInfoItem('PSN※',
                    'XXXXX'),
                _buildDivider(),

                SizedBox(height: 32.h),

                // 解绑按钮
                GestureDetector(
                  onTap: () =>
                      _showUnbindConfirmation(context, device.deviceId ?? ''),
                  child: Container(
                    width: double.infinity,
                    padding: EdgeInsets.symmetric(vertical: 16.r),
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(24.r),
                    ),
                    alignment: Alignment.center,
                    child: Text(
                      '解除绑定',
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w500,
                        color: Colors.black87,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        }),
      ),
    );
  }

  // 信息项
  Widget _buildInfoItem(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 12.r),
      child: Row(
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w500,
              color: Colors.grey[700],
            ),
          ),
          const Spacer(),
          Text(
            value,
            style: TextStyle(
              fontSize: 16.sp,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  // 分割线
  Widget _buildDivider() {
    return Divider(
      height: 1.h,
      thickness: 1.h,
      color: Colors.grey[300],
    );
  }

  // 显示解绑确认对话框
  void _showUnbindConfirmation(BuildContext context, String deviceId) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('解绑后将删除创建的所有信息同时自动移除所有绑定成员确定要解绑此设备吗?',
          textAlign: TextAlign.center,),
        actions: [
        Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween, // 设置左右对齐
          children: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Get.back();

              // 获取设备管理控制器来执行解绑操作
              final deviceManagementController =
                  Get.find<DeviceManagementController>();
              final device = controller.device.value;

              if (device != null) {
                deviceManagementController.unbindDevice(device);
              }
            },
            child: const Text('确定删除'),
          ),
        ],
        ),
        ],

      ),
    );
  }
}

import 'dart:io';
import 'dart:convert';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';
import 'package:flutter_sound/flutter_sound.dart';
import 'package:permission_handler/permission_handler.dart';

import '../../../core/services/blufi_service.dart';
import '../../../core/services/storage_service.dart';
import '../../../core/utils/logger.dart';
import '../../../data/models/device_model.dart';
import '../../../data/models/blufi_device.dart';
import '../../../data/repositories/device_repository.dart';

/// 设备控制器
class DeviceController extends GetxController {
  final DeviceRepository _deviceRepository;
  final StorageService _storageService;
  final BluFiService _bluFiService;

  // 设备列表
  final RxList<DeviceModel> devices = <DeviceModel>[].obs;

  // 设备绑定状态
  final RxBool isBinding = false.obs;

  // 当前用户ID
  int? _currentUserId;

  // 数据加载状态
  final RxBool isLoading = true.obs;

  // 错误信息
  final RxString errorMessage = ''.obs;

  DeviceController({
    required DeviceRepository deviceRepository,
  })  : _deviceRepository = deviceRepository,
        _storageService = Get.find<StorageService>(),
        _bluFiService = Get.find<BluFiService>();

  // 设备状态
  final RxBool isScanning = false.obs;
  final RxList<BluFiDevice> discoveredDevices = <BluFiDevice>[].obs;
  final Rx<BluFiDevice?> selectedDevice = Rx<BluFiDevice?>(null);
  final RxBool isBluetoothEnabled = true.obs;
  final RxBool isConnecting = false.obs;
  final RxBool isConnected = false.obs;

  // 语音录制相关
  final FlutterSoundRecorder _recorder = FlutterSoundRecorder();
  final RxBool isRecorderInitialized = false.obs;
  final RxBool isRecording = false.obs;
  final RxString recordingPath = ''.obs;

  // 聊天消息
  final RxList<Map<String, dynamic>> messages = <Map<String, dynamic>>[].obs;

  @override
  void onInit() {
    super.onInit();
    _initUserId();
    _checkBluetoothState();
    _listenToBluetoothState();
    _initRecorder();
  }

  @override
  void onReady() {
    super.onReady();
    fetchUserDevices();
  }

  @override
  void onClose() {
    _recorder.closeRecorder();
    super.onClose();
  }

  /// 初始化用户ID
  Future<void> _initUserId() async {
    try {
      final userData = await _storageService.getUser();
      if (userData != null) {
        try {
          _currentUserId = userData.id ?? 0;
        } catch (e) {
          AppLogger.error('无法解析用户ID', e);
        }
      }
    } catch (e) {
      AppLogger.error('初始化用户ID失败', e);
    }
  }

  /// 检查蓝牙状态
  Future<void> _checkBluetoothState() async {
    try {
      // BluFiService没有isBluetoothEnabled方法，暂时设为true
      isBluetoothEnabled.value = true;
    } catch (e) {
      AppLogger.error('检查蓝牙状态失败', e);
      isBluetoothEnabled.value = false;
    }
  }

  /// 监听蓝牙状态变化
  void _listenToBluetoothState() {
    // BluFiService没有bluetoothState流，暂时注释掉
    // TODO: 实现蓝牙状态监听
  }

  /// 停止扫描
  void _stopScan() {
    if (isScanning.value) {
      isScanning.value = false;
      // 实际停止扫描的逻辑
    }
  }

  /// 初始化录音机
  Future<void> _initRecorder() async {
    try {
      final status = await Permission.microphone.request();
      if (status != PermissionStatus.granted) {
        AppLogger.warning('未获取麦克风权限');
        return;
      }

      await _recorder.openRecorder();
      isRecorderInitialized.value = true;
      AppLogger.info('录音机初始化成功');
    } catch (e) {
      AppLogger.error('初始化录音机失败', e);
    }
  }

  /// 获取用户设备列表
  Future<void> fetchUserDevices() async {
    final result = await _deviceRepository.getUserDevices();
    if (result != null) {
      devices.value = result.whereType<DeviceModel>().toList();
    }
  }

  /// 获取用户分身信息
  Future<void> getMyCloneInfo() async {
    if (_currentUserId == null) return;

    final result = await _deviceRepository.getMyCloneInfo(_currentUserId!);
    if (result != null) {
      AppLogger.info('获取到${result.length}个分身设备');
    }
  }

  /// 解绑设备
  Future<bool> unbindDevice(String macAddress) async {
    if (_currentUserId == null) return false;

    isBinding.value = true;
    final result = await _deviceRepository.unbindDevice(
      macAddress: macAddress,
      userId: _currentUserId!.toString(),
    );
    isBinding.value = false;

    if (result == true) {
      await fetchUserDevices();
      return true;
    }
    return false;
  }


  // 开始录音
  Future<void> startRecording() async {
    if (!isRecorderInitialized.value) {
      await _initRecorder();
      if (!isRecorderInitialized.value) return;
    }

    try {
      final tempDir = await getTemporaryDirectory();
      recordingPath.value =
          '${tempDir.path}/audio_${DateTime.now().millisecondsSinceEpoch}.aac';

      await _recorder.startRecorder(
        toFile: recordingPath.value,
        codec: Codec.aacMP4,
      );

      isRecording.value = true;
      AppLogger.info('开始录音: ${recordingPath.value}');
    } catch (e) {
      AppLogger.error('开始录音失败', e);
      // _errorHandler.handleError(e); // Removed as per new_code
    }
  }

  // 停止录音
  Future<String?> stopRecording() async {
    if (!isRecording.value) return null;

    try {
      await _recorder.stopRecorder();
      isRecording.value = false;
      AppLogger.info('停止录音: ${recordingPath.value}');

      final path = recordingPath.value;
      recordingPath.value = '';
      return path;
    } catch (e) {
      AppLogger.error('停止录音失败', e);
      // _errorHandler.handleError(e); // Removed as per new_code
      return null;
    }
  }

  // 取消录音
  Future<void> cancelRecording() async {
    if (!isRecording.value) return;

    try {
      await _recorder.stopRecorder();
      isRecording.value = false;

      // 删除录音文件
      final file = File(recordingPath.value);
      if (await file.exists()) {
        await file.delete();
      }

      recordingPath.value = '';
      AppLogger.info('取消录音');
    } catch (e) {
      AppLogger.error('取消录音失败', e);
      // _errorHandler.handleError(e); // Removed as per new_code
    }
  }

  // 发送语音消息
  Future<void> sendVoiceMessage(String audioPath) async {
    try {
      if (selectedDevice.value == null || !isConnected.value) {
        // _errorHandler.showError('设备未连接'); // Removed as per new_code
        EasyLoading.showError('设备未连接');
        return;
      }

      final file = File(audioPath);
      if (!await file.exists()) {
        // _errorHandler.showError('音频文件不存在'); // Removed as per new_code
        EasyLoading.showError('音频文件不存在');
        return;
      }

      // 获取音频数据
      final bytes = await file.readAsBytes();

      // 添加到消息列表
      final message = {
        'id': DateTime.now().millisecondsSinceEpoch.toString(),
        'type': 'voice',
        'content': audioPath,
        'duration': 0, // 实际应用中应计算真实时长
        'sender': 'user',
        'timestamp': DateTime.now(),
      };
      messages.insert(0, message);

      // 通过蓝牙发送数据
      // 简化处理，实际应用可能需要分包发送大文件
      // 添加消息类型标识头
      final header = utf8.encode('VOICE:');
      final data = [...header, ...bytes];

      await sendData(data);
      AppLogger.info('语音消息已发送');
    } catch (e) {
      AppLogger.error('发送语音消息失败', e);
      // _errorHandler.handleError(e); // Removed as per new_code
    }
  }

  // 发送文本消息
  Future<void> sendTextMessage(String text) async {
    try {
      if (selectedDevice.value == null || !isConnected.value) {
        // _errorHandler.showError('设备未连接'); // Removed as per new_code
        EasyLoading.showError('设备未连接');
        return;
      }

      // 添加到消息列表
      final message = {
        'id': DateTime.now().millisecondsSinceEpoch.toString(),
        'type': 'text',
        'content': text,
        'sender': 'user',
        'timestamp': DateTime.now(),
      };
      messages.insert(0, message);

      // 通过蓝牙发送数据
      final header = utf8.encode('TEXT:');
      final textBytes = utf8.encode(text);
      final data = [...header, ...textBytes];

      await sendData(data);
      AppLogger.info('文本消息已发送');
    } catch (e) {
      AppLogger.error('发送文本消息失败', e);
      // _errorHandler.handleError(e); // Removed as per new_code
    }
  }

  // 扫描设备
  Future<void> scanForDevices() async {
    if (!isBluetoothEnabled.value) {
      return _requestEnableBluetooth();
    }

    try {
      isScanning.value = true;
      final devices = await _bluFiService.scanDevices();
      discoveredDevices.value = devices;
    } catch (e) {
      AppLogger.error('扫描设备失败', e);
      // _errorHandler.handleError(e); // Removed as per new_code
    } finally {
      isScanning.value = false;
    }
  }

  // 请求启用蓝牙
  Future<void> _requestEnableBluetooth() async {
    try {
      // BluFiService没有requestEnable方法，暂时返回true
      final enabled = true;
      isBluetoothEnabled.value = enabled;
    } catch (e) {
      AppLogger.error('请求启用蓝牙失败', e);
      // _errorHandler.handleError(e); // Removed as per new_code
    }
  }

  // 连接设备
  Future<void> connectToDevice(BluFiDevice device) async {
    try {
      selectedDevice.value = device;
      isConnecting.value = true;
      final connected = await _bluFiService.connect(device);
      isConnected.value = connected;
    } catch (e) {
      AppLogger.error('连接设备失败', e);
      // _errorHandler.handleError(e); // Removed as per new_code
      isConnected.value = false;
    } finally {
      isConnecting.value = false;
    }
  }

  // 断开设备连接
  Future<void> disconnectDevice() async {
    try {
      if (selectedDevice.value != null) {
        await _bluFiService.disconnect();
      }
      isConnected.value = false;
      selectedDevice.value = null;
    } catch (e) {
      AppLogger.error('断开设备连接失败', e);
      // _errorHandler.handleError(e); // Removed as per new_code
    }
  }

  // 发送数据
  Future<void> sendData(List<int> data) async {
    try {
      if (selectedDevice.value == null || !isConnected.value) {
        throw Exception('设备未连接');
      }

      await _bluFiService.sendCustomData(String.fromCharCodes(data));
    } catch (e) {
      AppLogger.error('发送数据失败', e);
      // _errorHandler.handleError(e); // Removed as per new_code
      rethrow;
    }
  }
}

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import '../../../data/models/device_info_model.dart';
import '../../../data/repositories/device_repository.dart';
import '../../../core/utils/logger.dart';

/// 设备详情控制器
class DeviceDetailController extends GetxController {
  final DeviceRepository _deviceRepository;

  DeviceDetailController({
    required DeviceRepository deviceRepository,
  }) : _deviceRepository = deviceRepository;

  // 当前设备
  final device = Rx<DeviceInfoModel?>(null);

  // 加载状态
  final RxBool isLoading = false.obs;

  // 编辑模式
  final RxBool isEditMode = false.obs;

  // 表单控制器
  late TextEditingController nameController;

  @override
  void onInit() {
    super.onInit();
    nameController = TextEditingController();
    _loadDevice();
  }

  @override
  void onClose() {
    nameController.dispose();
    super.onClose();
  }

  /// 加载设备详情
  void _loadDevice() {
    // 从路由参数获取设备信息
    if (Get.arguments != null && Get.arguments['agentId'] != null) {
      final agentId = Get.arguments['agentId'];
      loadDeviceById(agentId);
    }

  }

  /// 加载设备详情（从API）
  Future<void> loadDeviceById(String agentId) async {
    try {
      isLoading.value = true;

      final response = await _deviceRepository.getDeviceById(agentId);

      if (response != null ) {
        device.value = response;
        nameController.text = response.deviceName ?? '';
      } else {
        EasyLoading.showError('加载设备详情失败: ${"操作失败"}');
      }
    } catch (e) {
      AppLogger.error('加载设备详情失败', e);
      EasyLoading.showError('加载设备详情失败');
    } finally {
      isLoading.value = false;
    }
  }

  /// 进入编辑模式
  void enterEditMode() {
    isEditMode.value = true;
  }

  /// 保存设备信息
  Future<void> saveDevice() async {
    try {
      isLoading.value = true;

      // 更新设备别名
      if (device.value != null) {

        device.value?.deviceName = nameController.text.trim();
        // 调用API更新设备信息
        // 这里模拟一个成功的API调用
        await Future.delayed(const Duration(milliseconds: 500));

        // 显示成功提示
        EasyLoading.showSuccess('设备信息已更新');

        // 退出编辑模式
        isEditMode.value = false;
      }
    } catch (e) {
      AppLogger.error('保存设备信息失败', e);
      EasyLoading.showError('保存失败，请重试');
    } finally {
      isLoading.value = false;
    }
  }

  /// 取消编辑
  void cancelEdit() {
    // 恢复原始名称
    if (device.value != null) {
      nameController.text = device.value!.deviceName ?? '';
    }

    // 退出编辑模式
    isEditMode.value = false;
  }
}

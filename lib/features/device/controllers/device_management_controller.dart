import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

import '../../../core/services/app_state_service.dart';
import '../../../data/models/clone_device_info_model.dart';
//mport '../../../data/models/device_model.dart';
import '../../../data/models/device_info_model.dart';
import '../../../data/repositories/device_repository.dart';
import '../../../core/services/blufi_service.dart';
import '../../../routes/app_routes.dart';
import '../../../core/utils/logger.dart';

class DeviceManagementController extends GetxController {
  final DeviceRepository _deviceRepository;
  final BluFiService _bluFiService;
  final AppStateService _appStateService;

  DeviceManagementController({
    required DeviceRepository deviceRepository,
    required AppStateService appStateService,
  })  : _deviceRepository = deviceRepository,
        _bluFiService = Get.find<BluFiService>(),
      _appStateService = appStateService;

  // 设备列表
  final RxList<CloneDeviceInfo> devices = <CloneDeviceInfo>[].obs;

  // 加载状态
  final RxBool isLoading = false.obs;

  // 解绑确认状态
  final RxBool isUnbinding = false.obs;

  @override
  void onInit() {
    super.onInit();
    loadDevices();
  }

  /// 加载用户设备列表
  Future<void> loadDevices() async {
    try {
      isLoading.value = true;
      final userId = _appStateService.currentUserId;
      // 加载真实设备数据

      final deviceList = await _deviceRepository.getUserDevicesList(userId);
      if (deviceList.isNotEmpty) {
        devices.value = deviceList;
      }
      // 如果没有设备数据，加载一些模拟数据用于演示

    } catch (e) {
      AppLogger.error('加载设备列表失败', e);
      EasyLoading.showError('加载设备列表失败');
    } finally {
      isLoading.value = false;
    }
  }

  /// 加载模拟设备数据（仅用于演示）
  void _loadMockDevices() {
   /* final mockDevice = DeviceModel(
      deviceId: 'mock-device-001',
      deviceName: 'TotWoo智能娃娃',
      batteryLevel: '85%',
      // 兼容性字段
      id: 'mock-device-001',
      alias: 'TotWoo智能娃娃',
      macAddress: '00:11:22:33:44:55',
      appVersion: '2.16.55',
    );*/

/*    if (devices.isEmpty) {
      devices.add(mockDevice);
    }*/
  }

  /// 查看设备详情
  void viewDeviceDetail(String agentId) {
    Get.toNamed(
      Routes.DEVICE_DETAIL,
      arguments: {'agentId': agentId},
    );
  }

  /// 添加新设备（跳转到蓝牙扫描页）
  void addNewDevice() {
    Get.toNamed(Routes.BLUETOOTH_SCAN);
  }

  /// 解绑设备
  Future<void> unbindDevice(DeviceInfoModel device) async {
    try {
      isUnbinding.value = true;
      
      // 执行解绑
      final macAddress = device.deviceId ?? '';
      String userId = '';

      // userId是int类型，需要转换为String
      if (device.userId != null) {
        userId = device.userId!.toString();
      }

      final result = await _deviceRepository.unbindDevice(
        macAddress: macAddress,
        userId: userId,
      );

      if (result == true) {
        EasyLoading.showSuccess('设备已成功解绑');
        loadDevices(); // 重新加载设备列表
      } else {
        EasyLoading.showError('解绑设备失败');
      }
    } catch (e) {
      AppLogger.error('解绑设备失败', e);
      EasyLoading.showError('解绑设备失败');
    } finally {
      isUnbinding.value = false;
    }
  }
}

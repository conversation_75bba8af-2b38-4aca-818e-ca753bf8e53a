import 'package:get/get.dart';
import 'package:smartai/core/config/app_constants.dart';
import 'package:smartai/data/models/blufi_device.dart';
import '../../../core/services/blufi_service.dart';
import '../../../core/utils/logger.dart';
import '../../../core/utils/permission_helper.dart';
import '../../../routes/app_routes.dart';

/// 简化的蓝牙扫描控制器 - 清晰的权限和扫描逻辑
class BluetoothScanController extends GetxController {
  final BluFiService _bluFiService = Get.find<BluFiService>();

  // ✅ 使用Service的状态，不重复声明
  RxBool get isScanning => _bluFiService.isScanning;
  RxList<BluFiDevice> get discoveredDevices => _bluFiService.discoveredDevices;

  // 保留Controller特有的状态
  final RxBool hasPermission = false.obs;
  final RxBool isConnecting = false.obs;
  final RxBool showPermissionDenied = false.obs;

  @override
  void onInit() {
    super.onInit();
    _initialize();
  }

  /// 初始化 - 检查权限并开始扫描
  Future<void> _initialize() async {
    AppLogger.info('初始化蓝牙扫描控制器');
    
    final hasPermissions = await PermissionHelper.requestBluetoothPermissions();
    
    if (hasPermissions) {
      hasPermission.value = true;
      showPermissionDenied.value = false;
      await startScan();
    } else {
      hasPermission.value = false;
      final isPermanentlyDenied = await PermissionHelper.isBluetoothPermissionPermanentlyDenied();
      showPermissionDenied.value = isPermanentlyDenied;
      
      if (!isPermanentlyDenied) {
        Get.snackbar('权限需要', '需要蓝牙权限才能扫描设备');
      }
    }
  }

  /// 开始扫描TWO_AI_设备 - 简化版本
  Future<void> startScan() async {
    if (isScanning.value) return;

    try {
      // ✅ 直接调用Service方法，Service会自动更新discoveredDevices
      await _bluFiService.startScan(
        namePrefix: AppConstants.device_name_prefix,
        timeout: AppConstants.scan_timeout,
      );

    } catch (e) {
      AppLogger.error('扫描失败', e);
      Get.snackbar('扫描失败', e.toString());
    }
  }

  /// 连接设备
  Future<void> connectToDevice(BluFiDevice device) async {
    if (isConnecting.value) return;

    try {
      isConnecting.value = true;
      AppLogger.info('连接设备: ${device.name}');

      await _bluFiService.stopScan();
      final success = await _bluFiService.connect(device);
      
      if (success) {
        AppLogger.info('设备连接成功，跳转WiFi配置页面');
        Get.toNamed(Routes.WIFI_CONFIG, arguments: device);
      } else {
        AppLogger.info('设备连接失败，跳转连接失败页面');
        Get.toNamed(Routes.CONNECTION_FAILED, arguments: device.name);
      }
    } catch (e) {
      AppLogger.error('连接失败', e);
      Get.toNamed(Routes.CONNECTION_FAILED, arguments: device.name);
    } finally {
      isConnecting.value = false;
    }
  }

  /// 重新申请权限
  Future<void> retryPermissions() async {
    await _initialize();
  }

  /// 打开系统设置
  Future<void> openSystemSettings() async {
    await PermissionHelper.openSettings();
  }

  @override
  void onClose() {
    _bluFiService.stopScan();
    super.onClose();
  }
}

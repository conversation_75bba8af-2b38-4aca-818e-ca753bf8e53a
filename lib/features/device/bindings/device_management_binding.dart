import 'package:get/get.dart';
import '../../../core/services/app_state_service.dart';
import '../../../data/repositories/device_repository.dart';
import '../controllers/device_management_controller.dart';

/// 设备管理绑定
class DeviceManagementBinding implements Bindings {
  @override
  void dependencies() {
    // 注册DeviceRepository
    Get.lazyPut<DeviceRepository>(
      () => DeviceRepository(),
      fenix: true,
    );

    // 注册DeviceManagementController
    Get.lazyPut<DeviceManagementController>(
      () => DeviceManagementController(
        deviceRepository: Get.find<DeviceRepository>(),
        appStateService: Get.find<AppStateService>(),
      ),
      fenix: true,
    );
  }
}

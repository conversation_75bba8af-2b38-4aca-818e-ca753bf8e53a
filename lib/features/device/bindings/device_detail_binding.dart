import 'package:get/get.dart';
import '../../../data/repositories/device_repository.dart';
import '../controllers/device_detail_controller.dart';

/// 设备详情绑定
class DeviceDetailBinding implements Bindings {
  @override
  void dependencies() {
    // 注册DeviceRepository
    Get.lazyPut<DeviceRepository>(
      () => DeviceRepository(),
      fenix: true,
    );

    // 注册DeviceDetailController
    Get.lazyPut<DeviceDetailController>(
      () => DeviceDetailController(
        deviceRepository: Get.find<DeviceRepository>(),
      ),
      fenix: true,
    );
  }
}

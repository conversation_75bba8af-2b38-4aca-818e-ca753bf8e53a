import 'package:get/get.dart';
import '../../../data/repositories/device_repository.dart';
import '../controllers/device_controller.dart';

/// 设备绑定
class DeviceBinding implements Bindings {
  @override
  void dependencies() {
    // 注册DeviceRepository
    Get.lazyPut<DeviceRepository>(
      () => DeviceRepository(),
      fenix: true,
    );

    // 注册DeviceController
    Get.lazyPut<DeviceController>(
      () => DeviceController(
        deviceRepository: Get.find<DeviceRepository>(),
      ),
      fenix: true,
    );
  }
}

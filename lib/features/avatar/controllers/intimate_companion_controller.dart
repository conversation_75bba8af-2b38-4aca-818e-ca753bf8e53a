import 'package:get/get.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import '../../../core/utils/logger.dart';
import '../../../data/repositories/avatar_repository.dart';
import '../../../data/models/avatar_model.dart';
import '../../../core/services/app_state_service.dart';
import '../../../routes/app_routes.dart';

/// 亲密陪伴者控制器
class IntimateCompanionController extends GetxController {
  final AvatarRepository _avatarRepository = Get.find<AvatarRepository>();
  final AppStateService _appStateService = Get.find<AppStateService>();

  // ==================== 状态管理 ====================
  final RxBool isLoading = false.obs;

  // ==================== 表单数据 ====================
  final RxString selectedRelationship = ''.obs;
  final RxString selectedWakeWord = ''.obs;
  final RxString nickname = ''.obs;
  final RxString name = ''.obs;
  final RxString selectedGender = ''.obs;
  final RxString age = ''.obs;
  final RxString profession = ''.obs;
  final RxString selectedBirthDate = ''.obs;
  final RxString selectedBirthTime = ''.obs;
  final RxString birthPlace = ''.obs;
  final RxString selectedZodiac = ''.obs;
  final RxString relationshipStatus = ''.obs;
  final RxBool agreedToTerms = false.obs;

  // ==================== 当前分身信息 ====================
  String? currentAgentId;

  @override
  void onInit() {
    super.onInit();
    _initializeData();
  }

  /// 初始化数据
  void _initializeData() {
    // 优先从参数中获取agentId，如果没有则从AppStateService获取
    currentAgentId = Get.parameters['agentId'] ?? _appStateService.currentAgentId.value.toString();

    // 如果currentAgentId为'0'，说明还没有创建分身
    if (currentAgentId == '0') {
      currentAgentId = null;
    }

    AppLogger.info('初始化亲密陪伴者设置，agentId: $currentAgentId');
    AppLogger.info('AppStateService currentAgentId: ${_appStateService.currentAgentId.value}');
    AppLogger.info('AppStateService currentUserId: ${_appStateService.currentUserId}');
  }

  // ==================== 表单更新方法 ====================

  void updateRelationship(String relationship) {
    selectedRelationship.value = relationship;
  }

  void updateWakeWord(String wakeWord) {
    selectedWakeWord.value = wakeWord;
  }

  void updateNickname(String value) {
    nickname.value = value;
  }

  void updateName(String value) {
    name.value = value;
  }

  void updateGender(String gender) {
    selectedGender.value = gender;
  }

  void updateAge(String value) {
    age.value = value;
  }

  void updateProfession(String value) {
    profession.value = value;
  }

  void updateBirthDate(String date) {
    selectedBirthDate.value = date;
  }

  void updateBirthTime(String time) {
    selectedBirthTime.value = time;
  }

  void updateBirthPlace(String place) {
    birthPlace.value = place;
  }

  void updateZodiac(String zodiac) {
    selectedZodiac.value = zodiac;
  }

  void updateRelationshipStatus(String status) {
    relationshipStatus.value = status;
  }

  // ==================== 表单验证 ====================

  bool _validateForm() {
    AppLogger.info('开始表单验证...');

    if (currentAgentId == null || currentAgentId!.isEmpty) {
      _showError('agentId不能为空，请先创建分身');
      AppLogger.error('agentId为空: $currentAgentId');
      return false;
    }

    if (selectedRelationship.value.isEmpty) {
      _showError('请选择分身与你的关系');
      AppLogger.error('关系未选择');
      return false;
    }

    if (selectedWakeWord.value.isEmpty) {
      _showError('请选择唤醒词');
      AppLogger.error('唤醒词未选择');
      return false;
    }

    if (nickname.value.isEmpty) {
      _showError('请填写你希望分身怎么称呼你');
      AppLogger.error('称呼未填写');
      return false;
    }

    if (name.value.isEmpty) {
      _showError('请填写姓名');
      AppLogger.error('姓名未填写');
      return false;
    }

    if (_appStateService.currentUserId == null) {
      _showError('用户ID不能为空，请重新登录');
      AppLogger.error('userId为空');
      return false;
    }

    AppLogger.info('表单验证通过');
    return true;
  }

  // ==================== 业务逻辑 ====================

  /// 提交表单
  Future<void> submitForm() async {
    if (!_validateForm()) {
      return;
    }

    try {
      isLoading.value = true;
      EasyLoading.show(status: '正在保存信息...');

      // 创建用户资料DTO
      final dto = DollUserProfileInfoDTO(
        agentId: currentAgentId!,
        dollId: currentAgentId!, // dollId 与 agentId 相同
        intimateRelation: selectedRelationship.value,
        userName: name.value,
        userNickName: nickname.value,
        userGender: selectedGender.value,
        userAge: int.tryParse(age.value),
        userProfession: profession.value,
        userBirthday: _parseBirthday(selectedBirthDate.value),
        userCity: birthPlace.value,
        userZodiacSign: selectedZodiac.value,
        relationStatus: relationshipStatus.value,
        userId: _appStateService.currentUserId,
        intimacyType: 300, // 300表示创建者
      );

      // 打印调试信息
      AppLogger.info('准备创建亲密陪伴者信息:');
      AppLogger.info('agentId: ${dto.agentId}');
      AppLogger.info('userId: ${dto.userId}');
      AppLogger.info('intimateRelation: ${dto.intimateRelation}');
      AppLogger.info('userName: ${dto.userName}');
      AppLogger.info('userNickName: ${dto.userNickName}');
      AppLogger.info('DTO JSON: ${dto.toJson()}');

      // 调用API创建用户资料
      final response = await _avatarRepository.createIntimateCompanionInfo(dto);

      if (response != null) {
        EasyLoading.showSuccess('信息保存成功！');
        AppLogger.info('亲密陪伴者信息创建成功');

        // ✅ 保存dollId到全局状态
        if (response.dollId != null) {
          _appStateService.saveDollId(response.dollId.toString());
          AppLogger.info('保存dollId到全局状态: ${response.dollId}');
        }

        // 跳转到声纹录制页面
        Get.toNamed(Routes.VOICE_PRINT_RECORDING, parameters: {
          'agentId': currentAgentId!,
          'dollId': response.dollId?.toString() ?? '',
        });
      } else {
        _showError("操作失败");
      }
    } catch (e) {
      AppLogger.error('创建亲密陪伴者信息失败', e);
      _showError('保存信息失败，请重试');
    } finally {
      isLoading.value = false;
      EasyLoading.dismiss();
    }
  }

  // ==================== 辅助方法 ====================



  /// 显示错误信息
  void _showError(String message) {
    EasyLoading.showError(message);
  }



  /// 解析生日字符串为DateTime对象
  DateTime? _parseBirthday(String birthday) {
    if (birthday.isEmpty) return null;
    try {
      // 尝试解析 "YYYY-MM-DD" 格式
      if (birthday.contains('-') && birthday.length == 10) {
        final parts = birthday.split('-');
        if (parts.length == 3) {
          final year = int.parse(parts[0]);
          final month = int.parse(parts[1]);
          final day = int.parse(parts[2]);
          return DateTime(year, month, day);
        }
      }
      // 如果不是标准格式，尝试直接解析
      return DateTime.parse(birthday);
    } catch (e) {
      AppLogger.error('解析生日失败: $birthday', e);
      return null;
    }
  }

  /// 重置表单
  void resetForm() {
    selectedRelationship.value = '';
    selectedWakeWord.value = '';
    nickname.value = '';
    name.value = '';
    selectedGender.value = '';
    age.value = '';
    profession.value = '';
    selectedBirthDate.value = '';
    selectedBirthTime.value = '';
    birthPlace.value = '';
    selectedZodiac.value = '';
    relationshipStatus.value = '';
    agreedToTerms.value = false;
  }
}

import 'dart:io';
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:flutter_sound/flutter_sound.dart';
import 'package:path_provider/path_provider.dart';
import '../../../core/utils/logger.dart';
import '../../../data/repositories/avatar_repository.dart';
import '../../../core/services/app_state_service.dart';
import '../../../routes/app_routes.dart';

/// 音色克隆状态
enum VoiceCloneStatus {
  initial, // 初始状态：请朗读
  recording, // 录音中
  processing, // 录音校验中
  failed, // 校验失败
  completed, // 完成
}

/// 复刻音色控制器
class VoiceCloneController extends GetxController {
  final AvatarRepository _repository = Get.find<AvatarRepository>();
  final AppStateService _appState = Get.find<AppStateService>();

  // 录音器
  FlutterSoundRecorder? _recorder;
  Timer? _recordingTimer;

  // 状态管理
  final voiceCloneStatus = VoiceCloneStatus.initial.obs;
  final recordingText = '请朗读'.obs;
  final recordingProgress = 0.0.obs;
  final hasAgreedToTerms = false.obs;
  final isRecording = false.obs;
  final isButtonPressed = false.obs; // 按钮按下状态

  // 朗读文本
  final readingText = '正在加载朗读内容...'.obs;

  @override
  void onInit() {
    super.onInit();
    _initRecorder();
    _updateRecordingText();
    _loadReadingContent();
  }

  @override
  void onClose() {
    _recorder?.closeRecorder();
    _recordingTimer?.cancel();
    super.onClose();
  }

  /// 初始化录音器
  Future<void> _initRecorder() async {
    _recorder = FlutterSoundRecorder();
    await _recorder!.openRecorder();
  }

  /// 加载朗读内容
  Future<void> _loadReadingContent() async {
    try {
      final content = await _repository.getReadingContent();
      AppLogger.info('获取到的朗读内容: $content');

      if (content != null && content.isNotEmpty) {
        // 只做基本的清理，保持原始内容
        readingText.value = content.trim();
      } else {
        _setDefaultReadingText();
      }
    } catch (e) {
      AppLogger.error('加载朗读内容失败', e);
      _setDefaultReadingText();
    }
  }

  /// 设置默认朗读文本
  void _setDefaultReadingText() {
    readingText.value =
        '我今天去逛街发现了一家新开的咖啡店。哇，里面的环境真的是超级温馨，咖啡的味道也是相当好喝！我就一边喝着咖啡，一边享受着阳光，这种生活真是太惬意了！哦对了，顺便说一下，我最近还迷上了瑜伽，每天都会抽出一点时间来练习。所以我最近过的很充实，感觉烦恼都没有了。';
  }

  /// 更新录音状态文本
  void _updateRecordingText() {
    switch (voiceCloneStatus.value) {
      case VoiceCloneStatus.initial:
        recordingText.value = '请朗读';
        break;
      case VoiceCloneStatus.recording:
        recordingText.value = '录音中，请朗读......';
        break;
      case VoiceCloneStatus.processing:
        recordingText.value = '录音校验中，请稍等...';
        break;
      case VoiceCloneStatus.failed:
        recordingText.value = '校验失败，请按照文本朗读';
        break;
      case VoiceCloneStatus.completed:
        recordingText.value = '已完成分身创建';
        break;
    }
  }

  /// 显示声音定制规则弹窗
  void showTermsDialog() {
    Get.dialog(
      Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Container(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    '声音定制及分享规则',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  IconButton(
                    onPressed: () => Get.back(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              const Text(
                '为了更好地为您提供音定制功能的相关服务，我们会根据本服务的具体功能需要，收集必要的用户个人信息。\n\n您可通过阅读《声音定制及分享规则》来了解本功能以及您个人信息的收集和使用情况。\n\n点击"同意并继续"按钮代表您已阅读并同意遵守《声音定制及分享规则》的全部内容',
                style: TextStyle(
                  fontSize: 14,
                  height: 1.5,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 24),
              Row(
                children: [
                  Expanded(
                    child: TextButton(
                      onPressed: () => Get.back(),
                      style: TextButton.styleFrom(
                        backgroundColor: Colors.grey[200],
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text(
                        '不同意',
                        style: TextStyle(color: Colors.black87),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: TextButton(
                      onPressed: () {
                        Get.back();
                        _agreeToTerms();
                      },
                      style: TextButton.styleFrom(
                        backgroundColor: Colors.blue,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text(
                        '同意',
                        style: TextStyle(color: Colors.white),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
      barrierDismissible: false,
    );
  }

  /// 同意条款并申请录音权限
  Future<void> _agreeToTerms() async {
    hasAgreedToTerms.value = true;
    await _requestMicrophonePermission();
  }

  /// 申请录音权限
  Future<void> _requestMicrophonePermission() async {
    final status = await Permission.microphone.request();
    if (status.isGranted) {
      AppLogger.info('录音权限已获取');
    } else {
      Get.snackbar('权限不足', '需要录音权限才能使用音色复刻功能');
    }
  }

  /// 开始录音
  Future<void> startRecording() async {
    // 如果正在录音，不重复开始
    if (isRecording.value) return;

    if (!hasAgreedToTerms.value) {
      showTermsDialog();
      return;
    }

    final permission = await Permission.microphone.status;
    if (!permission.isGranted) {
      await _requestMicrophonePermission();
      return;
    }

    try {
      final directory = await getTemporaryDirectory();
      final filePath =
          '${directory.path}/voice_clone_${DateTime.now().millisecondsSinceEpoch}.wav';

      await _recorder!.startRecorder(
        toFile: filePath,
        codec: Codec.pcm16WAV,
      );

      isRecording.value = true;
      isButtonPressed.value = true;
      voiceCloneStatus.value = VoiceCloneStatus.recording;
      _updateRecordingText();
      _startRecordingTimer();
      AppLogger.info('开始录音');
    } catch (e) {
      AppLogger.error('开始录音失败', e);
      Get.snackbar('录音失败', '无法开始录音，请重试');
    }
  }

  /// 停止录音并发送
  Future<void> stopRecording() async {
    if (!isRecording.value) return;

    try {
      final filePath = await _recorder!.stopRecorder();
      isRecording.value = false;
      isButtonPressed.value = false;
      _recordingTimer?.cancel();
      recordingProgress.value = 0.0;

      if (filePath != null) {
        voiceCloneStatus.value = VoiceCloneStatus.processing;
        _updateRecordingText();
        AppLogger.info('录音完成，开始上传: $filePath');
        await _uploadVoiceFile(filePath);
      } else {
        AppLogger.error('录音文件路径为空');
        _resetToInitial();
      }
    } catch (e) {
      AppLogger.error('停止录音失败', e);
      _resetToInitial();
    }
  }

  /// 取消录音
  Future<void> cancelRecording() async {
    if (!isRecording.value) return;

    try {
      await _recorder!.stopRecorder();
      isRecording.value = false;
      isButtonPressed.value = false;
      _recordingTimer?.cancel();
      recordingProgress.value = 0.0;
      _resetToInitial();
      AppLogger.info('录音已取消，可重新录音');
      Get.snackbar('录音已取消', '可重新按住录音');
    } catch (e) {
      AppLogger.error('取消录音失败', e);
      _resetToInitial();
    }
  }

  /// 开始录音计时器
  void _startRecordingTimer() {
    recordingProgress.value = 0.0;
    _recordingTimer =
        Timer.periodic(const Duration(milliseconds: 100), (timer) {
      if (recordingProgress.value < 1.0) {
        recordingProgress.value += 0.01; // 10秒录满
      } else {
        timer.cancel();
        stopRecording(); // 自动停止录音
      }
    });
  }

  /// 上传音频文件 - 支持两种模式
  Future<void> _uploadVoiceFile(String filePath) async {
    final mode = Get.parameters['mode'];

    if (mode == 'intimate') {
      // 亲密陪伴者模式：调用 uploadVoicePrint
      await _uploadForIntimate(filePath);
    } else {
      // 分身模式：调用 uploadVoiceCloneFile
      await _uploadForAvatar(filePath);
    }
  }

  /// 分身模式上传
  Future<void> _uploadForAvatar(String filePath) async {
    try {
      final result = await _repository.uploadVoiceCloneFile(
        agentId: _appState.currentAgentId.value.toString(),
        userId: _appState.user.value?.id ?? 0,
        type: 2, // 复刻音色
        file: File(filePath),
      );

      if (result != null &&
          result.containsKey('status') &&
          result['status'] == true) {
        voiceCloneStatus.value = VoiceCloneStatus.completed;
        _updateRecordingText();
      } else {
        voiceCloneStatus.value = VoiceCloneStatus.failed;
        _updateRecordingText();
      }
    } catch (e) {
      voiceCloneStatus.value = VoiceCloneStatus.failed;
      _updateRecordingText();
      AppLogger.error('音色文件上传失败', e);
      EasyLoading.showError("音色文件上传失败");
    }
  }

  /// 亲密陪伴者模式上传
  Future<void> _uploadForIntimate(String filePath) async {
    final dollProfileUserId = Get.parameters['dollProfileUserId'] ?? _appState.dollId;

    try {
      final result = await _repository.uploadVoicePrint(
        dollProfileUserId: dollProfileUserId,
        file: File(filePath),
      );

      if (result != null) {
        voiceCloneStatus.value = VoiceCloneStatus.completed;
        _updateRecordingText();
        Get.offNamed(Routes.INTIMATE_COMPANION_COMPLETE);
      } else {
        voiceCloneStatus.value = VoiceCloneStatus.failed;
        _updateRecordingText();
      }
    } catch (e) {
      EasyLoading.showError("失败");
      voiceCloneStatus.value = VoiceCloneStatus.failed;
      _updateRecordingText();
    } finally {
      EasyLoading.dismiss();
    }
  }

  /// 重置到初始状态
  void _resetToInitial() {
    voiceCloneStatus.value = VoiceCloneStatus.initial;
    _updateRecordingText();
  }

  /// 跳过音色复刻
  void skipVoiceClone() {
    Get.toNamed(Routes.AVATAR_COMPLETE);
  }

  /// 开始测试
  void startTest() {
    Get.toNamed(Routes.AVATAR_COMPLETE);
  }
}

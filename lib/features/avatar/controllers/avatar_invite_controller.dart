import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';

import '../../../core/services/app_state_service.dart';
import '../../../core/utils/email_validator.dart';
import '../../../core/utils/logger.dart';
import '../../../data/models/partner_model.dart';
import '../../../data/repositories/avatar_repository.dart';

/// 亲密陪伴者邀请控制器
class AvatarInviteController extends GetxController {
  // 常量定义
  static const int maxPartnerCount = 2;
  static const int emptyPartnerStatus = 0;

  // 依赖注入
  final AvatarRepository _repository = Get.find<AvatarRepository>();
  final AppStateService _appStateService = Get.find<AppStateService>();

  // 响应式数据
  final RxList<PartnerModel> partners = <PartnerModel>[].obs;

  @override
  void onInit() {
    super.onInit();
    _initializeEmptySlots();
    _loadPartnerList();
  }

  /// 初始化空槽位
  void _initializeEmptySlots() {
    final emptyPartners = List.generate(
      maxPartnerCount,
      (index) => PartnerModel(
        email: '',
        status: emptyPartnerStatus,
        userId: _appStateService.currentUserId,
      ),
    );
    partners.assignAll(emptyPartners);
  }

  /// 加载伙伴列表
  Future<void> _loadPartnerList() async {
    try {
      final result = await _repository.getInviteIntimateCompanionInfo(
        _appStateService.currentUserId!,
      );

      if (result != null && result.isNotEmpty) {
        _fillSlotsWithServerData(result);
        AppLogger.info('伙伴列表加载成功，数量: ${result.length}');
      }
    } catch (e) {
      AppLogger.error('伙伴列表加载失败', e);
    }
  }

  /// 用服务器数据填充槽位
  void _fillSlotsWithServerData(List<PartnerModel> serverData) {
    for (int i = 0; i < maxPartnerCount; i++) {
      if (i < serverData.length) {
        partners[i] = serverData[i];
      } else {
        partners[i] = PartnerModel(
          email: '',
          status: emptyPartnerStatus,
          userId: _appStateService.currentUserId,
        );
      }
    }
    partners.refresh();
  }

  /// 更新指定槽位的邮箱地址
  void updatePartnerEmail(int slotIndex, String email) {
    if (!_isValidSlotIndex(slotIndex)) return;

    partners[slotIndex] = partners[slotIndex].copyWith(email: email);
    partners.refresh();
  }

  /// 获取指定槽位的邮箱地址
  String getPartnerEmail(int slotIndex) {
    if (!_isValidSlotIndex(slotIndex)) return '';
    return partners[slotIndex].email ?? '';
  }

  /// 检查邮箱格式是否有效
  bool isEmailFormatValid(String email) {
    return EmailValidator.isValidFormat(email);
  }

  /// 检查是否可以发送邀请
  bool canSendInvitation(int slotIndex) {
    if (!_isValidSlotIndex(slotIndex)) return false;

    final partner = partners[slotIndex];
    final email = partner.email ?? '';

    return email.isNotEmpty &&
        isEmailFormatValid(email) &&
        partner.status != PartnerModel.STATUS_INVITING;
  }

  /// 检查是否正在邀请中
  bool isInvitationPending(int slotIndex) {
    if (!_isValidSlotIndex(slotIndex)) return false;
    return partners[slotIndex].status == PartnerModel.STATUS_INVITING;
  }

  /// 检查是否已创建完成
  bool isPartnerCreated(int slotIndex) {
    if (!_isValidSlotIndex(slotIndex)) return false;
    return partners[slotIndex].status == PartnerModel.STATUS_CREATED;
  }

  /// 检查输入框是否可编辑
  bool isInputFieldEditable(int slotIndex) {
    if (!_isValidSlotIndex(slotIndex)) return true;
    return partners[slotIndex].status != PartnerModel.STATUS_INVITING;
  }

  /// 获取按钮显示文本
  String getButtonText(int slotIndex) {
    if (!_isValidSlotIndex(slotIndex)) return '邀请';

    final partner = partners[slotIndex];
    final email = partner.email ?? '';

    if (email.isEmpty || !isEmailFormatValid(email)) return '邀请';

    switch (partner.status) {
      case PartnerModel.STATUS_INVITING:
        return '取消邀请';
      case PartnerModel.STATUS_CREATED:
        return '已创建';
      default:
        return '邀请';
    }
  }

  /// 获取按钮颜色
  Color getButtonColor(int slotIndex) {
    if (!canSendInvitation(slotIndex) && !isInvitationPending(slotIndex)) {
      return Colors.grey[300]!;
    }

    final buttonText = getButtonText(slotIndex);
    switch (buttonText) {
      case '取消邀请':
        return Colors.orange[400]!;
      case '已创建':
        return Colors.green[400]!;
      case '邀请':
        return Colors.blue[400]!;
      default:
        return Colors.grey[300]!;
    }
  }

  /// 发送邀请
  Future<void> sendInvitation(int slotIndex) async {
    if (!canSendInvitation(slotIndex)) return;

    EasyLoading.show(status: '发送邀请中...');

    try {
      final partner = partners[slotIndex];
      final invitationData = [
        PartnerModel(
          userId: _appStateService.currentUserId,
          email: partner.email,
          intimacyType: PartnerModel.INTIMACY_TYPE_INTIMATE,
        ),
      ];

      final result =
          await _repository.inviteIntimateCompanionInfo(invitationData);

      if (result != null && result['status'] == true) {
        _updatePartnerStatus(slotIndex, PartnerModel.STATUS_INVITING);
        EasyLoading.showSuccess('邀请发送成功');
        AppLogger.info('邀请发送成功: ${partner.email}');

        // 延迟刷新获取服务器最新状态
        Future.delayed(const Duration(milliseconds: 500), _loadPartnerList);
      } else {
        EasyLoading.showError('邀请发送失败，请重试');
      }
    } catch (e) {
      AppLogger.error('邀请发送异常', e);
      EasyLoading.showError('邀请发送失败，请重试');
    }
  }

  /// 取消邀请
  Future<void> cancelInvitation(int slotIndex) async {
    if (!_isValidSlotIndex(slotIndex)) return;

    final partner = partners[slotIndex];
    final email = partner.email ?? '';

    final isConfirmed = await _showCancelConfirmationDialog(email);
    if (!isConfirmed) return;

    EasyLoading.show(status: '取消邀请中...');

    try {
      final result =
          await _repository.cancelInviteIntimateCompanionInfo([partner]);

      if (result != null && result['status'] == true) {
        _resetPartnerSlot(slotIndex);
        EasyLoading.showSuccess('已取消邀请');
        AppLogger.info('取消邀请成功: $email');
      } else {
        EasyLoading.showError('取消邀请失败，请重试');
      }
    } catch (e) {
      AppLogger.error('取消邀请异常', e);
      EasyLoading.showError('取消邀请失败，请重试');
    }
  }

  /// 处理按钮点击事件
  void handleButtonAction(int slotIndex) {
    if (isInvitationPending(slotIndex)) {
      cancelInvitation(slotIndex);
    } else if (canSendInvitation(slotIndex)) {
      sendInvitation(slotIndex);
    }
  }

  /// 检查是否有已发送的邀请
  bool hasActiveInvitations() {
    return partners.any((partner) =>
        partner.status == PartnerModel.STATUS_INVITING ||
        partner.status == PartnerModel.STATUS_CREATED);
  }

  /// 获取邀请统计信息
  Map<String, int> getInvitationStatistics() {
    int invitingCount = 0;
    int createdCount = 0;
    int totalCount = 0;

    for (final partner in partners) {
      if ((partner.email ?? '').isEmpty) continue;

      totalCount++;
      switch (partner.status) {
        case PartnerModel.STATUS_INVITING:
          invitingCount++;
          break;
        case PartnerModel.STATUS_CREATED:
          createdCount++;
          break;
      }
    }

    return {
      'inviting': invitingCount,
      'created': createdCount,
      'total': totalCount,
    };
  }

  /// 处理完成按钮点击
  void handleCompletionAction() {
    final statistics = getInvitationStatistics();
    AppLogger.info('邀请完成，统计信息: $statistics');
  }

  // 私有辅助方法

  /// 检查槽位索引是否有效
  bool _isValidSlotIndex(int slotIndex) {
    return slotIndex >= 0 && slotIndex < partners.length;
  }

  /// 更新伙伴状态
  void _updatePartnerStatus(int slotIndex, int status) {
    partners[slotIndex] = partners[slotIndex].copyWith(
      status: status,
      createTime: DateTime.now().millisecondsSinceEpoch,
      updateTime: DateTime.now().millisecondsSinceEpoch,
    );
    partners.refresh();
  }

  /// 重置伙伴槽位为空状态
  void _resetPartnerSlot(int slotIndex) {
    partners[slotIndex] = PartnerModel(
      email: '',
      status: emptyPartnerStatus,
      userId: _appStateService.currentUserId,
    );
    partners.refresh();
  }

  /// 显示取消确认对话框
  Future<bool> _showCancelConfirmationDialog(String email) async {
    final result = await Get.dialog<bool>(
      AlertDialog(
        title: const Text('取消邀请'),
        content: Text('确定要取消对 $email 的邀请吗？'),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Get.back(result: true),
            child: const Text('确定'),
          ),
        ],
      ),
    );
    return result ?? false;
  }
}

import 'package:get/get.dart';
import 'package:just_audio/just_audio.dart';

import '../../../core/services/app_state_service.dart';
import '../../../core/utils/logger.dart';
import '../../../data/models/doll_profile_info.dart';
import '../../../data/models/audio_file_model.dart';
import '../../../data/repositories/avatar_repository.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'dart:io';
import 'package:flutter/material.dart';

import '../../../routes/app_routes.dart';

/// 音色选择控制器
/// 功能：音色选择、播放、上传音频文件
class VoiceSelectionController extends GetxController {
  final AvatarRepository _repository = Get.find<AvatarRepository>();
  final AppStateService _appState = Get.find<AppStateService>();

  // 音色选择相关状态
  final isTestMode = false.obs;
  final selectedVoiceType = 1.obs; // 1:预设音色 2:克隆音色 3:上传音频
  final RxInt tabVoice = 0.obs; // 0:全部 1:女声 2:男声
  final voiceList = <VoiceDTO>[].obs;
  final selectedVoiceId = ''.obs;
  final selectedVoiceName = ''.obs;
  final currentPlayingVoiceUrl = ''.obs;
  final isPlaying = false.obs;
  final isLoading = false.obs;

  // 统一的音频文件管理 - 替代之前分散的音频相关状态
  final Rx<AudioFileModel> currentAudioFile = AudioFileModel.empty().obs;

  // 测试模式相关状态
  final Rx<TtsVoiceClone?> testModeVoiceData = Rx<TtsVoiceClone?>(null);

  // 音频播放器
  final AudioPlayer _audioPlayer = AudioPlayer();

  @override
  void onInit() {
    super.onInit();
    AppLogger.info('VoiceSelectionController onInit - 控制器实例: ${hashCode}');
    AppLogger.info(
        '初始状态: selectedVoiceId=${selectedVoiceId.value}, selectedVoiceName=${selectedVoiceName.value}, selectedVoiceType=${selectedVoiceType.value}');

    loadVoiceList();
    _setupAudioPlayerListeners();

    // 检测测试模式
    checkTestMode();
  }

  /// 检测并处理测试模式 - 可以被重复调用
  void checkTestMode() {
    AppLogger.info('检测测试模式: source=${Get.parameters['source']}');
    if (Get.parameters['source'] == 'test') {
      AppLogger.info('进入测试模式，加载音色数据');
      isTestMode.value = true;
      _loadTestModeVoice();
    }
  }

  /// 设置音频播放器监听器
  void _setupAudioPlayerListeners() {
    _audioPlayer.playerStateStream.listen((state) {
      if (state.processingState == ProcessingState.completed) {
        isPlaying.value = false;
        currentPlayingVoiceUrl.value = ''; // 重置当前播放的URL
      }
    });
  }

  @override
  void onClose() {
    _audioPlayer.dispose();
    super.onClose();
  }

  /// 加载音色列表
  Future<void> loadVoiceList() async {
    final result = await _repository.getVoiceList(tabVoice.value);
    if (result != null) {
      voiceList.assignAll(result);
    }
  }

  /// 选择音色
  void selectVoiceItem(dynamic voiceItem) {
    selectedVoiceId.value = voiceItem.voiceId ?? '';
    selectedVoiceName.value = voiceItem.remark ?? '';
    selectedVoiceType.value = 1; // 设置为选择音色类型
    AppLogger.info(
        '选择音色: voiceId=${voiceItem.voiceId}, remark=${voiceItem.remark}');
    AppLogger.info(
        '选择后状态: selectedVoiceId=${selectedVoiceId.value}, selectedVoiceName=${selectedVoiceName.value}, selectedVoiceType=${selectedVoiceType.value}');

    // 手动触发更新
    update();
  }

  /// 停止播放
  Future<void> stopPlaying() async {
    await _audioPlayer.stop();
    isPlaying.value = false;
    currentPlayingVoiceUrl.value = '';
  }

  /// 过滤音色列表
  void filterVoiceList(String filter) {
    // 根据filter设置对应的类型并重新加载数据
    if (filter == '全部') tabVoice.value = 0;
    if (filter == '女声') tabVoice.value = 1;
    if (filter == '男声') tabVoice.value = 2;

    loadVoiceList();
  }

  /// 播放音色
  Future<void> playVoiceUrl(String url) async {
    try {
      // 如果当前正在播放同一个音频，则暂停
      if (isPlaying.value && currentPlayingVoiceUrl.value == url) {
        await _audioPlayer.pause();
        isPlaying.value = false;
        return;
      }

      // 如果正在播放其他音频，先停止
      if (isPlaying.value && currentPlayingVoiceUrl.value != url) {
        await _audioPlayer.stop();
      }

      // 如果是暂停状态的同一个音频，继续播放
      if (!isPlaying.value && currentPlayingVoiceUrl.value == url) {
        await _audioPlayer.play();
        isPlaying.value = true;
        return;
      }

      // 播放新的音频
      currentPlayingVoiceUrl.value = url;
      isPlaying.value = true;

      await _audioPlayer.setUrl(url);
      await _audioPlayer.play();
    } catch (e) {
      AppLogger.error('播放失败', e);
      isPlaying.value = false;
      currentPlayingVoiceUrl.value = '';
    }
  }

  /// 设置音色文件
  void setVoiceFile(dynamic file) {
    AppLogger.info('设置音色文件: $file');
  }

  /// 显示删除音频确认对话框
  void showDeleteAudioConfirmDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('确定要删除吗？'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              _deleteUploadedAudio();
            },
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }

  /// 删除已上传的音频
  void _deleteUploadedAudio() {
    currentAudioFile.value = AudioFileModel.empty();
    _audioPlayer.stop(); // 停止播放
    AppLogger.info('已删除上传的音频文件');
  }

  /// 选择并上传音频文件
  Future<void> pickAndUploadAudioFile() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['mp3', 'm4a', 'wav'],
      );

      if (result != null && result.files.single.path != null) {
        final file = File(result.files.single.path!);

        // 检查文件大小（限制为20MB）
        final fileSizeInBytes = await file.length();
        final fileSizeInMB = fileSizeInBytes / (1024 * 1024);

        if (fileSizeInMB > 20) {
          EasyLoading.showError('文件大小不能超过20MB');
          return;
        }

        // 设置上传状态 - 使用统一的音频文件模型
        currentAudioFile.value = AudioFileModel.local(file: file);
        selectedVoiceType.value = 3; // 设置为上传音频类型

        AppLogger.info('音频文件选择成功: ${file.path}');
      }
    } catch (e) {
      AppLogger.error('选择音频文件失败', e);
      EasyLoading.showError('上传失败，选择音频文件时出错，请重试');
    }
  }

  /// 播放当前音频文件 - 统一处理本地和远程音频
  Future<void> playCurrentAudio() async {
    final audioFile = currentAudioFile.value;
    if (audioFile.isEmpty) return;

    try {
      if (isPlaying.value) {
        await _audioPlayer.stop();
        isPlaying.value = false;
      } else {
        isPlaying.value = true;

        // 根据文件类型选择播放方式
        if (audioFile.isLocal) {
          await _audioPlayer.setFilePath(audioFile.playPath);
        } else {
          await _audioPlayer.setUrl(audioFile.playPath);
        }

        await _audioPlayer.play();
      }
    } catch (e) {
      isPlaying.value = false;
      AppLogger.error('播放音频失败', e);
      EasyLoading.showError('播放失败');
    }
  }

  /// 加载测试模式音色数据
  Future<void> _loadTestModeVoice() async {
    try {
      final agentId = _appState.currentAgentId.value;
      if (agentId == 0) {
        AppLogger.error('测试模式加载音色失败: agentId为空');
        return;
      }

      final voiceData = await _repository.getVoiceCloneFile(agentId.toString());
      if (voiceData != null) {
        testModeVoiceData.value = voiceData;

        if ((voiceData.type ?? 1) > 3) {
          voiceData.type = 1;
        }

        //测试数据
        // voiceData.type = 1;
        // voiceData.referenceAudio =
        //     'https://totwoo-app.oss-cn-beijing.aliyuncs.com/d28c29db65d342e8909b6134d90344d0_1.mp3';

        AppLogger.info('测试模式加载音色成功: type=${voiceData.type}');

        selectedVoiceType.value = voiceData.type ?? 1;
        // 根据type设置对应的选中状态
        if (voiceData.type == 2) {
          // 复刻音色
        } else if (voiceData.type == 3) {
          // 上传音色 - 使用统一的音频文件模型
          if (voiceData.referenceAudio?.isNotEmpty == true) {
            currentAudioFile.value = AudioFileModel.remote(
              url: voiceData.referenceAudio!,
            );
            AppLogger.info('测试模式加载音频文件: ${currentAudioFile.value.displayName}');
          }
        } else {
          // 选择音色
          selectedVoiceId.value = voiceData.ttsVoice ?? '';
          selectedVoiceName.value = voiceData.name ?? '';
        }
      } else {
        AppLogger.error('测试模式加载音色失败: 未获取到音色数据');
      }
    } catch (e) {
      AppLogger.error('测试模式加载音色失败', e);
    }
  }

  /// 播放测试模式的远程音频 - 现在统一使用 playCurrentAudio
  Future<void> playTestModeAudio() async {
    await playCurrentAudio();
  }

  /// 上传音色文件 - 区分新建和更新模式
  Future<void> uploadVoiceFile({
    required int type,
    String? ttsVoice,
    dynamic file,
  }) async {
    try {
      EasyLoading.show(status: isTestMode.value ? '更新中...' : '上传中...');

      Map<String, bool>? result;

      // 根据模式调用不同接口，统一使用 currentAudioFile 获取文件
      final uploadFile = currentAudioFile.value.uploadFile ?? file;

      if (isTestMode.value) {
        // 测试模式使用编辑接口
        result = await _repository.editVoiceCloneFile(
          id:testModeVoiceData.value?.id??0,
          agentId: _appState.currentAgentId.value.toString(),
          type: type,
          ttsVoice: ttsVoice,
          file: uploadFile is File ? uploadFile : null,
        );
      } else {
        // 新建模式使用上传接口
        result = await _repository.uploadVoiceCloneFile(
          agentId: _appState.currentAgentId.value.toString(),
          userId: _appState.currentUserId!,
          type: type,
          ttsVoice: ttsVoice,
          file: uploadFile is File ? uploadFile : null,
        );
      }

      EasyLoading.dismiss();

      if (result != null) {
        EasyLoading.showSuccess(isTestMode.value ? '音色更新成功' : '音色设置成功');

        if (isTestMode.value) {
          Get.back();
        } else {
          Get.offNamed(Routes.AVATAR_COMPLETE);
        }
      } else {
        EasyLoading.showError(isTestMode.value ? '音色更新失败' : '音色设置失败');
      }
    } catch (e) {
      EasyLoading.dismiss();
      AppLogger.error('音色文件操作失败', e);
      EasyLoading.showError(isTestMode.value ? '音色更新失败，请重试' : '音色设置失败，请重试');
    }
  }
}

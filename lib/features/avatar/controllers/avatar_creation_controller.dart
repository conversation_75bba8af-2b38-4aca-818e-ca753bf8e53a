import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';

import '../../../core/utils/logger.dart';
import '../../../data/models/doll_profile_info.dart';
import '../../../data/repositories/avatar_repository.dart';
import '../../../core/services/app_state_service.dart';
import '../../../routes/app_routes.dart';

/// 分身创建控制器 - 简化版本
/// 核心逻辑：表单数据 → DollProfileInfo → 创建分身
class AvatarCreationController extends GetxController {
  final AvatarRepository _repository = Get.find<AvatarRepository>();
  final AppStateService _appState = Get.find<AppStateService>();

  // 核心业务数据
  final dollProfile = DollProfileInfo().obs;

  // UI状态
  final currentStep = 0.obs; // 0:基础信息 1:能力设定 2:对话示例 3:完成

  @override
  void onInit() {
    super.onInit();
    // 初始化空的 DollProfileInfo
    if (dollProfile.value.dollName == null) {
      dollProfile.value = DollProfileInfo();
    }
  }

  /// 下一步
  void nextStep() {
    if (currentStep.value < 3) {
      currentStep.value++;
    }
  }

  /// 上一步
  void previousStep() {
    if (currentStep.value > 0) {
      currentStep.value--;
    }
  }

  /// 跳转到指定步骤
  void goToStep(int step) {
    currentStep.value = step;
  }

  /// 创建分身
  Future<void> createAvatar() async {
    EasyLoading.show(status: '创建...');
    // 确保用户ID设置
    final profile = dollProfile.value;
    profile.userId = _appState.currentUserId;

    final result = await _repository.createCloneInfo(profile,
        userId: _appState.currentUserId!);
    EasyLoading.dismiss();
    if (result != null) {
      dollProfile.value = result;
      // 将 agentId 转换为 int 并设置到 AppState
      if (result.agentId != null) {
        _appState.currentAgentId.value = int.tryParse(result.agentId!) ?? 0;
      }
      AppLogger.info('分身创建成功: ${result.agentId}');
      // 跳转到预览页面，传递来源参数
      Get.offNamed(Routes.AVATAR_PREVIEW, parameters: {'source': 'create'});
    } else {
      EasyLoading.showError("分身创建失败");
    }
  }

  /// 根据agentId获取分身信息
  Future<void> getCloneInfoByAgentId(String agentId) async {
    try {
      EasyLoading.show(status: '加载中...');
      final result = await _repository.getCloneInfoByAgentId(agentId);
      EasyLoading.dismiss();

      if (result != null) {
        dollProfile.value = result;
        AppLogger.info('分身信息获取成功: ${result.agentId}');
      } else {
        EasyLoading.showError("获取分身信息失败");
      }
    } catch (e) {
      EasyLoading.dismiss();
      AppLogger.error('获取分身信息失败', e);
      EasyLoading.showError("获取分身信息失败");
    }
  }

  /// 更新分身信息
  Future<void> updateAvatar() async {
    try {
      final result = await _repository.updateCloneInfo(dollProfile.value);
      if (result != null) {
        AppLogger.info('分身更新成功');
      }
    } catch (e) {
      AppLogger.error('更新分身失败', e);
    }
  }

  /// 重置表单
  void resetForm() {
    dollProfile.value = DollProfileInfo();
    currentStep.value = 0;
  }

  /// 验证表单数据
  bool validateCurrentStep() {
    final profile = dollProfile.value;

    switch (currentStep.value) {
      case 0: // 基础信息
        if (profile.dollName?.isEmpty ?? true) {
          Get.snackbar('提示', '请填写分身姓名');
          return false;
        }
        if (profile.dollGender?.isEmpty ?? true) {
          Get.snackbar('提示', '请选择分身性别');
          return false;
        }
        if (profile.dollAge?.isEmpty ?? true) {
          Get.snackbar('提示', '请填写分身年龄');
          return false;
        }
        if (profile.dollProfession?.isEmpty ?? true) {
          Get.snackbar('提示', '请填写分身职业');
          return false;
        }
        if (profile.dollBirthday?.isEmpty ?? true) {
          Get.snackbar('提示', '请选择出生日期');
          return false;
        }
        break;
      case 1: // 能力设定
        if (profile.dollCharacterDescription?.isEmpty ?? true) {
          Get.snackbar('提示', '请填写分身性格描述');
          return false;
        }
        break;
      case 2: // 对话示例
        if (profile.exampleConversation1?.isEmpty ?? true) {
          Get.snackbar('提示', '请填写对话示例');
          return false;
        }
        break;
    }
    return true;
  }

  /// 更新简介
  Future<bool> updateProfileBrief(String newText) async {
    dollProfile.update((val) {
      val?.profileBrief = newText;
    });
    await updateAvatar();
    return true;
  }
}

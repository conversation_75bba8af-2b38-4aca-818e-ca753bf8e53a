import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../routes/app_routes.dart';
import '../../../shared/widgets/custom_button.dart';
import '../controllers/avatar_invite_controller.dart';

/// 亲密陪伴者邀请页面
/// 功能：
/// - 提供2个邮箱输入框用于邀请亲密陪伴者
/// - 支持邀请发送、取消操作
/// - 显示邀请状态和相关UI反馈
class AvatarInviteView extends GetView<AvatarInviteController> {
  const AvatarInviteView({super.key});

  // 常量定义
  static const String _pageTitle = '请输入亲密陪伴者邮箱';
  static const String _skipButtonText = '暂不邀请';
  static const String _completeButtonText = '完成邀请';
  static const String _emailHintText = '输入对方的邮箱';
  static const String _maxPartnersHint = '最多可绑定两位亲密陪伴者';
  static const String _managementHint = '后续可前往 "我的"-"绑定成员管理中"，进行邀请或编辑成员';
  static const String _emailFormatError = '邮箱账号错误';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: _buildAppBar(),
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                padding: EdgeInsets.symmetric(horizontal: 24.w),
                child: _buildScrollableContent(),
              ),
            ),
            _buildBottomSection(),
          ],
        ),
      ),
    );
  }

  /// 构建应用栏
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      elevation: 0,
      leading: null,
      actions: [
        TextButton(
          onPressed: () => Get.toNamed(Routes.INTIMATE_COMPANION_FORM),
          child: Text(
            _skipButtonText,
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 16.sp,
            ),
          ),
        ),
      ],
    );
  }

  /// 构建可滚动内容
  Widget _buildScrollableContent() {
    return Column(
      children: [
        SizedBox(height: 60.h),
        _buildPageTitle(),
        SizedBox(height: 60.h),
        _buildFirstEmailInputRow(),
        SizedBox(height: 24.h),
        _buildSecondEmailInputRow(),
        SizedBox(height: 40.h), // 底部留白，避免内容贴边
      ],
    );
  }

  /// 构建底部区域
  Widget _buildBottomSection() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 24.w),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(height: 16.h),
          _buildCompleteButton(),
          SizedBox(height: 16.h),
          _buildHintTexts(),
          SizedBox(height: 24.h),
        ],
      ),
    );
  }

  /// 构建页面标题
  Widget _buildPageTitle() {
    return Text(
      _pageTitle,
      style: TextStyle(
        fontSize: 20.sp,
        fontWeight: FontWeight.w500,
        color: Colors.black87,
      ),
    );
  }

  /// 构建第一个邮箱输入行
  Widget _buildFirstEmailInputRow() {
    return _buildEmailInputRow(slotIndex: 0);
  }

  /// 构建第二个邮箱输入行
  Widget _buildSecondEmailInputRow() {
    return _buildEmailInputRow(slotIndex: 1);
  }

  /// 构建完成按钮
  Widget _buildCompleteButton() {
    return CustomButton(
      text: _completeButtonText,
      onPressed: () => Get.offNamed(Routes.INTIMATE_COMPANION_FORM),
      color: Colors.blue[400],
      height: 48.h,
      borderRadius: 24.r,
    );
  }

  /// 构建提示文本
  Widget _buildHintTexts() {
    return Column(
      children: [
        Text(
          _maxPartnersHint,
          style: TextStyle(
            fontSize: 12.sp,
            color: Colors.grey[600],
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: 4.h),
        Text(
          _managementHint,
          style: TextStyle(
            fontSize: 12.sp,
            color: Colors.grey[600],
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  /// 构建邮箱输入行
  Widget _buildEmailInputRow({required int slotIndex}) {
    return Row(
      children: [
        Expanded(child: _buildEmailInputField(slotIndex)),
        SizedBox(width: 12.w),
        _buildActionButton(slotIndex),
      ],
    );
  }

  /// 构建邮箱输入框
  Widget _buildEmailInputField(int slotIndex) {
    return Obx(() {
      final email = controller.getPartnerEmail(slotIndex);
      final isEmailFormatValid =
          email.isEmpty || controller.isEmailFormatValid(email);
      final isFieldEditable = controller.isInputFieldEditable(slotIndex);
      final isInvitationPending = controller.isInvitationPending(slotIndex);

      return Container(
        height: 48.h,
        decoration: BoxDecoration(
          color: isFieldEditable ? Colors.white : Colors.grey[100],
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(
            color: _getInputBorderColor(
                email, isEmailFormatValid, isInvitationPending),
            width: isInvitationPending ? 2 : 1,
          ),
        ),
        child: TextField(
          enabled: isFieldEditable,
          onChanged: (value) => controller.updatePartnerEmail(slotIndex, value),
          keyboardType: TextInputType.emailAddress,
          controller: TextEditingController.fromValue(
            TextEditingValue(
              text: email,
              selection: TextSelection.collapsed(offset: email.length),
            ),
          ),
          style: TextStyle(
            fontSize: 16.sp,
            color: isFieldEditable ? Colors.black87 : Colors.grey[500],
          ),
          decoration: InputDecoration(
            hintText: _emailHintText,
            hintStyle: TextStyle(
              fontSize: 16.sp,
              color: Colors.grey[400],
            ),
            border: InputBorder.none,
            contentPadding: EdgeInsets.symmetric(
              horizontal: 16.w,
              vertical: 12.h,
            ),
            errorText: email.isNotEmpty && !isEmailFormatValid
                ? _emailFormatError
                : null,
            errorStyle: TextStyle(
              fontSize: 12.sp,
              color: Colors.red[600],
            ),
            suffixIcon: _buildInputSuffixIcon(slotIndex),
          ),
        ),
      );
    });
  }

  /// 构建操作按钮
  Widget _buildActionButton(int slotIndex) {
    return Obx(() {
      final buttonText = controller.getButtonText(slotIndex);
      final buttonColor = controller.getButtonColor(slotIndex);
      final canSendInvitation = controller.canSendInvitation(slotIndex);
      final isInvitationPending = controller.isInvitationPending(slotIndex);
      final isPartnerCreated = controller.isPartnerCreated(slotIndex);

      return SizedBox(
        width: 80.w,
        height: 48.h,
        child: ElevatedButton(
          onPressed: _getButtonOnPressed(canSendInvitation, isInvitationPending,
              isPartnerCreated, slotIndex),
          style: ElevatedButton.styleFrom(
            backgroundColor: buttonColor,
            foregroundColor: Colors.white,
            elevation: 0,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8.r),
            ),
            padding: EdgeInsets.zero,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildButtonIcon(isInvitationPending, isPartnerCreated),
              if (buttonText.length <= 4) SizedBox(width: 2.w),
              Flexible(
                child: Text(
                  buttonText,
                  style: TextStyle(
                    fontSize: buttonText.length > 4 ? 10.sp : 12.sp,
                    fontWeight: FontWeight.w500,
                    color: Colors.white,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      );
    });
  }

  /// 构建输入框后缀图标
  Widget? _buildInputSuffixIcon(int slotIndex) {
    if (controller.isInvitationPending(slotIndex)) {
      return Icon(
        Icons.access_time,
        color: Colors.blue[400],
        size: 20.w,
      );
    } else if (controller.isPartnerCreated(slotIndex)) {
      return Icon(
        Icons.check_circle,
        color: Colors.green[400],
        size: 20.w,
      );
    }
    return null;
  }

  /// 构建按钮图标
  Widget _buildButtonIcon(bool isInvitationPending, bool isPartnerCreated) {
    if (isInvitationPending) {
      return Icon(Icons.cancel_outlined, size: 14.w);
    } else if (isPartnerCreated) {
      return Icon(Icons.check_circle_outline, size: 14.w);
    } else {
      return Icon(Icons.send_outlined, size: 14.w);
    }
  }

  // 私有辅助方法

  /// 获取输入框边框颜色
  Color _getInputBorderColor(
      String email, bool isEmailFormatValid, bool isInvitationPending) {
    if (email.isNotEmpty && !isEmailFormatValid) {
      return Colors.red[300]!;
    } else if (isInvitationPending) {
      return Colors.blue[300]!;
    } else {
      return Colors.grey[300]!;
    }
  }

  /// 获取按钮点击回调
  VoidCallback? _getButtonOnPressed(
    bool canSendInvitation,
    bool isInvitationPending,
    bool isPartnerCreated,
    int slotIndex,
  ) {
    if ((canSendInvitation || isInvitationPending) && !isPartnerCreated) {
      return () => controller.handleButtonAction(slotIndex);
    }
    return null;
  }
}

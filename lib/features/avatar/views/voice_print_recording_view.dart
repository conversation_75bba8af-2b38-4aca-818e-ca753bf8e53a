import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/voice_print_controller.dart';

/// 声纹录制页面 - 亲密陪伴者设置专用
class VoicePrintRecordingView extends GetView<VoicePrintController> {
  const VoicePrintRecordingView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text('录制声纹'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
            const SizedBox(height: 20),

            // 标题
            const Text(
              '请选择其中一种录入声纹的方式',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 12),

            // 副标题
            const Text(
              '使用过程中设备将通过声纹来识别你~',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 60),

            // 复制音色区域
            _buildVoiceCloneSection(),

            const SizedBox(height: 60),

            // 上传音频区域
            _buildUploadSection(),

            const SizedBox(height: 60),

            // 完成按钮
            SizedBox(
              width: double.infinity,
              child: Obx(() => ElevatedButton(
                onPressed: controller.canComplete()
                    ? () => controller.completeVoicePrint()
                    : null,
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  backgroundColor: Colors.grey.shade400,
                  disabledBackgroundColor: Colors.grey.shade300,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(25),
                  ),
                ),
                child: const Text(
                        '完成',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
              )),
            ),

            const SizedBox(height: 40),
          ],
        ),
      ),
      ),
    );
  }

  /// 构建复制音色区域
  Widget _buildVoiceCloneSection() {
    return Column(
      children: [
        const Text(
          '复制音色：',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),

        const SizedBox(height: 30),

        // 大麦克风图标 - 跳转到朗读页面
        GestureDetector(
          onTap:  () => controller.startVoiceClone(),
          child: Container(
            width: 160,
            height: 160,
            decoration: BoxDecoration(
              color: Colors.grey.shade600,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(80),
                topRight: Radius.circular(80),
                bottomLeft: Radius.circular(20),
                bottomRight: Radius.circular(20),
              ),
            ),
            child: Stack(
              alignment: Alignment.center,
              children: [
                // 麦克风主体
                Container(
                  width: 80,
                  height: 100,
                  decoration: BoxDecoration(
                    color: Colors.grey.shade600,
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(40),
                      topRight: Radius.circular(40),
                      bottomLeft: Radius.circular(10),
                      bottomRight: Radius.circular(10),
                    ),
                  ),
                ),
                // 播放按钮
                Container(
                  width: 40,
                  height: 40,
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.play_arrow,
                    color: Colors.grey,
                    size: 24,
                  ),
                ),
                // 麦克风底座
                Positioned(
                  bottom: 20,
                  child: Container(
                    width: 120,
                    height: 20,
                    decoration: BoxDecoration(
                      color: Colors.grey.shade600,
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),

        const SizedBox(height: 16),

        // 状态文本
        Obx(() => Text(
          controller.hasVoiceClone.value
              ? '复刻完成'
              : '点击进入朗读录制',
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey.shade600,
          ),
          textAlign: TextAlign.center,
        )),
      ],
    );
  }

  /// 构建上传音频区域
  Widget _buildUploadSection() {
    return Column(
      children: [
        const Text(
          '上传音频：',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        
        const SizedBox(height: 20),
        
        // 上传按钮
        Obx(() => GestureDetector(
          onTap: controller.selectAudioFile,
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.upload_file,
                  color: Colors.grey.shade600,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    controller.uploadedFile.value != null
                        ? '已选择: ${controller.uploadedFile.value!.path.split('/').last}'
                        : '手机文件上传',
                    style: TextStyle(
                      fontSize: 14,
                      color: controller.uploadedFile.value != null
                          ? Colors.black87
                          : Colors.grey.shade600,
                    ),
                  ),
                ),
                if (controller.uploadedFile.value != null)
                  GestureDetector(
                    onTap: controller.removeUploadedFile,
                    child: Icon(
                      Icons.close,
                      color: Colors.grey.shade600,
                      size: 20,
                    ),
                  ),
                if (controller.uploadedFile.value == null)
                  Icon(
                    Icons.chevron_right,
                    color: Colors.grey.shade600,
                    size: 20,
                  ),
              ],
            ),
          ),
        )),

        const SizedBox(height: 12),

        // 上传说明
        Text(
          '请上传 MP3、M4A、WAV 格式的音频文件，时长在10-60秒之间，大小不超过20MB',
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}

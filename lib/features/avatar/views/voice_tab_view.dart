import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/voice_selection_controller.dart';
import '../../../shared/widgets/custom_button.dart';

/// 音色选择页面
class VoiceTabView extends GetView<VoiceSelectionController> {
  const VoiceTabView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            controller.stopPlaying();
            Get.back();
          },
        ),
        title: const Text('选择音色'),
        centerTitle: true,
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
      ),
      body: SafeArea(
        child: Column(
          children: [
            // 筛选栏
            Container(
              padding: const EdgeInsets.all(16),
              child: _buildFilterTabs(),
            ),

            // 分割线
            Container(
              height: 1,
              color: Colors.grey[200],
            ),

            // 音色列表
            Expanded(
              child: Obx(() => _buildVoiceList()),
            ),

            // 底部保存按钮
            Container(
              padding: const EdgeInsets.all(16),
              child: CustomButton(
                text: '保存',
                onPressed: () {
                  controller.stopPlaying();
                  Get.back();
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建筛选标签
  Widget _buildFilterTabs() {
    final filters = ['全部', '女声', '男声'];

    return Row(
      children: filters.map((filter) {
        return Obx(() {
          // 根据controller.tabVoice.value来判断当前选中的tab
          final selectedIndex = controller.tabVoice.value;
          final selectedName = switch (selectedIndex) {
            0 => '全部',
            1 => '女声',
            2 => '男声',
            _ => '全部',
          };

          final isSelected = selectedName == filter;

          return Expanded(
            child: GestureDetector(
              onTap: () {
                // 点击tab时调用对应的API
                controller.filterVoiceList(filter);
              },
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 4),
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: isSelected ? Colors.grey[400] : Colors.grey[200],
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  filter,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: isSelected ? Colors.white : Colors.black87,
                    fontSize: 14,
                    fontWeight: isSelected ? FontWeight.w500 : FontWeight
                        .normal,
                  ),
                ),
              ),
            ),
          );
        });
      }).toList(),
    );
  }

  /// 构建音色列表
  Widget _buildVoiceList() {
    if (controller.isLoading.value) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (controller.voiceList.isEmpty) {
      return const Center(
        child: Text(
          '暂无音色数据',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey,
          ),
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: controller.voiceList.length,
      itemBuilder: (context, index) {
        final voice = controller.voiceList[index];
        return _buildVoiceItem(voice);
      },
    );
  }

  /// 构建单个音色项
  Widget _buildVoiceItem(voice) {
    final voiceId = voice.voiceId ?? '';
    final voiceName = voice.model ?? '';
    final remark = voice.remark ?? '';

    return Obx(() {
      final isSelected = controller.selectedVoiceId.value == voiceId;
      final voice = controller.voiceList.firstWhereOrNull((v) =>
      v.voiceId == voiceId);
      final isPlaying = voice?.url != null &&
          controller.currentPlayingVoiceUrl.value == voice!.url &&
          controller.isPlaying.value;

      return Container(
        margin: const EdgeInsets.only(bottom: 12),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.grey[200],
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            // 播放按钮
            GestureDetector(
              onTap: () {
                final voice = controller.voiceList.firstWhereOrNull((v) =>
                v.voiceId == voiceId);
                if (voice?.url != null) controller.playVoiceUrl(voice!.url!);
              },
              child: Container(
                width: 40,
                height: 40,
                decoration: const BoxDecoration(
                  color: Colors.grey,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  isPlaying ? Icons.pause : Icons.play_arrow,
                  color: Colors.white,
                  size: 20,
                ),
              ),
            ),

            const SizedBox(width: 16),

            // 音色信息
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    voiceName,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    remark,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),

            // 选中状态
            GestureDetector(
              onTap: () {
                final voice = controller.voiceList.firstWhereOrNull((v) =>
                v.voiceId == voiceId);
                if (voice != null) controller.selectVoiceItem(voice);
              },
              child: Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: isSelected ? Colors.blue : Colors.grey,
                    width: 2,
                  ),
                  color: isSelected ? Colors.blue : Colors.transparent,
                ),
                child: isSelected
                    ? const Icon(Icons.check, size: 16, color: Colors.white)
                    : null,
              ),
            ),
          ],
        ),
      );
    });
  }
}

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/avatar_creation_controller.dart';
import '../../../shared/widgets/custom_button.dart';
import '../../../routes/app_routes.dart';

/// 性格与背景预览页面 - 展示profileBrief
class AvatarPreviewView extends GetView<AvatarCreationController> {
  const AvatarPreviewView({super.key});

  @override
  Widget build(BuildContext context) {
    final bool isTestMode  = Get.parameters['source'] == 'test';
    return Scaffold(
      appBar: AppBar(
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => Get.back(),
          ),
          title: const Text('性格与背景预览'),
          centerTitle: true,
          backgroundColor: Colors.white,
          foregroundColor: Colors.black,
          elevation: 0,
      ),
      body: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: <PERSON>umn(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        const SizedBox(height: 40),

                        // 标题
                        const Text(
                          '这是关于你创建的人设描述',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        const Text(
                          '请检查一下',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 30),
                        // 预览内容区域
                        Obx(() => Container(
                              width: double.infinity,
                              padding: const EdgeInsets.all(20),
                              decoration: BoxDecoration(
                                color: Colors.grey[100],
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // 显示生成的人设描述
                                  Text(
                                    controller.dollProfile.value.profileBrief
                                                ?.isNotEmpty ==
                                            true
                                        ? controller
                                            .dollProfile.value.profileBrief!
                                        : '正在生成人设描述...',
                                    style: const TextStyle(
                                      fontSize: 14,
                                      height: 1.6,
                                      color: Colors.black87,
                                    ),
                                  ),
                                  const SizedBox(height: 20),

                                  // 字数统计和修改按钮
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        '${controller.dollProfile.value.profileBrief?.length ?? 0}/400',
                                        style: const TextStyle(
                                          fontSize: 12,
                                          color: Colors.grey,
                                        ),
                                      ),
                                      // 修改按钮
                                      TextButton.icon(
                                        onPressed: () {
                                          _showEditDialog(context);
                                        },
                                        icon: const Icon(Icons.edit, size: 16),
                                        label: const Text('修改'),
                                        style: TextButton.styleFrom(
                                          foregroundColor: Colors.blue,
                                          textStyle:
                                              const TextStyle(fontSize: 12),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            )),
                        const SizedBox(height: 40),

                        // 重新开始按钮 - test模式隐藏
                        if (Get.parameters['source'] != 'test')
                          InkWell(
                            onTap: () {
                              // 重新开始逻辑
                              Get.offAllNamed(Routes.AVATAR_BASIC_INFO);
                            },
                            child: Row(
                              children: [
                                Container(
                                  width: 20,
                                  height: 20,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    border: Border.all(
                                        color: Colors.blue, width: 2),
                                  ),
                                  child: const Icon(Icons.refresh,
                                      size: 12, color: Colors.blue),
                                ),
                                const SizedBox(width: 8),
                                const Text(
                                  '重新开始',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.blue,
                                  ),
                                ),
                              ],
                            ),
                          ),
                      ],
                    ),
                  ),
                ),

                // 底部按钮 - 根据来源显示不同按钮
                Padding(
                  padding: EdgeInsets.only(
                    bottom: MediaQuery.of(context).padding.bottom > 0
                        ? 0
                        : 16, // 如果没有系统导航栏，添加额外间距
                  ),
                  child: CustomButton(
                    text: isTestMode ? '完成' : '下一步',
                    onPressed: () async {
                      if (isTestMode) {
                        Get.back();
                      } else {
                        Get.offNamed(Routes.AVATAR_UPLOAD_FILE);
                      }
                    },
                  ),
                ),
              ],
            ),
          ),
        ));
  }




  /// 显示编辑弹窗
  void _showEditDialog(BuildContext context) {
    final TextEditingController editController = TextEditingController();
    final currentText = controller.dollProfile.value.profileBrief ?? '';
    editController.text = currentText;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Container(
            padding: const EdgeInsets.all(20),
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                // 标题
                const Text(
                  '性格与背景预览',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  '这是关于你创建的人设描述\n请检查一下',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
                const SizedBox(height: 20),

                // 编辑框
                Container(
                  height: 200,
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: TextField(
                    controller: editController,
                    maxLines: null,
                    expands: true,
                    maxLength: 400,
                    decoration: const InputDecoration(
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.all(16),
                      counterText: '',
                    ),
                    style: const TextStyle(
                      fontSize: 14,
                      height: 1.5,
                    ),
                  ),
                ),
                const SizedBox(height: 8),

                // 字数统计
                Align(
                  alignment: Alignment.centerRight,
                  child: ValueListenableBuilder<TextEditingValue>(
                    valueListenable: editController,
                    builder: (context, value, child) {
                      return Text(
                        '${value.text.length}/400',
                        style: const TextStyle(
                          fontSize: 12,
                          color: Colors.grey,
                        ),
                      );
                    },
                  ),
                ),
                const SizedBox(height: 20),

                // 按钮
                Row(
                  children: [
                    Expanded(
                      child: TextButton(
                        onPressed: () => Get.back(),
                        child: const Text(
                          '取消',
                          style: TextStyle(color: Colors.grey),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () async {
                          final newText = editController.text.trim();
                          if (newText.isNotEmpty) {
                            Get.back();
                            await _updateProfileBrief(newText);
                          }
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: const Text('保存'),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            ),
          ),
        );
      },
    );
  }

  /// 更新人设描述
  Future<void> _updateProfileBrief(String newText) async {
    try {
      // 显示加载状态
      Get.dialog(
        const Center(
          child: CircularProgressIndicator(),
        ),
        barrierDismissible: false,
      );

      // 调用更新接口
      final success = await controller.updateProfileBrief(newText);

      // 关闭加载对话框
      Get.back();

      if (success) {
        Get.snackbar('成功', '人设描述更新成功');
      } else {
        Get.snackbar('失败', '人设描述更新失败，请重试');
      }
    } catch (e) {
      // 关闭加载对话框
      if (Get.isDialogOpen == true) {
        Get.back();
      }
      Get.snackbar('错误', '更新过程中出现错误：$e');
    }
  }
}

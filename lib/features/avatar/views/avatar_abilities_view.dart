import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/avatar_creation_controller.dart';
import '../../../shared/widgets/custom_button.dart';
import '../../../routes/app_routes.dart';

/// 第二部分：能力设定页面 -
class AvatarAbilitiesView extends GetView<AvatarCreationController> {
  AvatarAbilitiesView({Key? key}) : super(key: key);

  // 多选题选项
  final List<String> _multipleChoiceOptions = [
    '日常生活琐事',
    '情绪与压力的缓解',
    '对未来的鼓励与陪伴',
    '我们的故事与生活点滴',
    '兴趣与爱好的探索',
  ];

  // 单选题选项
  final List<String> _singleChoiceOptions = [
    '更聪明',
    '更有趣',
    '更温柔',
    '百科能手',
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
        title: const Text('性格与背景'),
        centerTitle: true,
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 第二部分标题
                      const Center(
                        child: Text(
                          '第二部分：能力设定',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      const SizedBox(height: 30),

                      // 多选题
                      const Text(
                        '* 您希望创建的分身和分身使用者主要聊些什么？\n（多选）',
                        style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                      ),
                      const SizedBox(height: 16),

                      // 多选题选项
                      ...List.generate(_multipleChoiceOptions.length, (index) {
                        return _buildMultipleChoiceOption(_multipleChoiceOptions[index], index);
                      }),

                      const SizedBox(height: 30),

                      // 单选题
                      const Text(
                        '选择超能力属性（单选）\n（在你所创建的分身属性外希望"他"具有以下特点）',
                        style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                      ),
                      const SizedBox(height: 16),

                      // 单选题选项
                      Row(
                        children: List.generate(_singleChoiceOptions.length, (index) {
                          return Expanded(
                            child: Padding(
                              padding: EdgeInsets.only(right: index < _singleChoiceOptions.length - 1 ? 8 : 0),
                              child: _buildSingleChoiceOption(_singleChoiceOptions[index], index),
                            ),
                          );
                        }),
                      ),
                  ],
                ),
              ),
            ),

              // 底部按钮 - 只显示下一步，添加底部安全区域
              Padding(
                padding: EdgeInsets.only(
                  bottom: MediaQuery.of(context).padding.bottom > 0
                      ? 0
                      : 16, // 如果没有系统导航栏，添加额外间距
                ),
                child: CustomButton(
                  text: '下一步',
                  onPressed: () {
                    if (_validate()) {
                      Get.offNamed(Routes.AVATAR_DIALOGUE);
                    }
                  },
                ),
              ),
          ],
        ),
      ),
    )
    );
  }

  /// 构建多选题选项
  Widget _buildMultipleChoiceOption(String option, int index) {
    return Obx(() {
      final selectedTopics = _getSelectedTopics();
      final isSelected = selectedTopics.contains(option);
      return Container(
        margin: const EdgeInsets.only(bottom: 12),
        child: InkWell(
          onTap: () {
            final currentTopics = _getSelectedTopics();
            if (isSelected) {
              currentTopics.remove(option);
            } else {
              currentTopics.add(option);
            }
            _updateSelectedTopics(currentTopics);
          },
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: isSelected ? Colors.blue : Colors.transparent,
                width: 2,
              ),
            ),
            child: Row(
              children: [
                Container(
                  width: 20,
                  height: 20,
                  decoration: BoxDecoration(
                    shape: BoxShape.rectangle,
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(
                      color: isSelected ? Colors.blue : Colors.grey,
                      width: 2,
                    ),
                    color: isSelected ? Colors.blue : Colors.transparent,
                  ),
                  child: isSelected
                      ? const Icon(Icons.check, size: 14, color: Colors.white)
                      : null,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    option,
                    style: TextStyle(
                      fontSize: 14,
                      color: isSelected ? Colors.blue : Colors.black87,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    });
  }

  /// 构建单选题选项
  Widget _buildSingleChoiceOption(String option, int index) {
    return Obx(() {
      final isSelected = controller.dollProfile.value.dollCharacterDescription?.contains(option) == true;
      return InkWell(
        onTap: () {
          controller.dollProfile.update((val) {
            val?.dollCharacterDescription = option;
          });
        },
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
          decoration: BoxDecoration(
            color: isSelected ? Colors.blue : Colors.grey[200],
            borderRadius: BorderRadius.circular(20),
          ),
          child: Center(
            child: Text(
              option,
              style: TextStyle(
                fontSize: 12,
                color: isSelected ? Colors.white : Colors.black87,
                fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
              ),
            ),
          ),
        ),
      );
    });
  }

  /// 获取已选择的聊天主题
  List<String> _getSelectedTopics() {
    final topics = controller.dollProfile.value.dollLanguageCharacteristic ?? '';
    if (topics.isEmpty) return [];
    return topics.split(',').where((t) => t.isNotEmpty).toList();
  }

  /// 更新已选择的聊天主题
  void _updateSelectedTopics(List<String> topics) {
    controller.dollProfile.update((val) {
      val?.dollLanguageCharacteristic = topics.join(',');
    });
  }

  /// 验证表单
  bool _validate() {
    if (_getSelectedTopics().isEmpty) {
      Get.snackbar('提示', '请至少选择一个聊天主题');
      return false;
    }
    if (controller.dollProfile.value.dollCharacterDescription?.isEmpty ?? true) {
      Get.snackbar('提示', '请选择一个超能力属性');
      return false;
    }
    return true;
  }
}

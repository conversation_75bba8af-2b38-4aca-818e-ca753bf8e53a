import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../routes/app_routes.dart';
import '../controllers/avatar_creation_controller.dart';
import '../../../shared/widgets/custom_button.dart';

class AvatarSelectionView extends GetView<AvatarCreationController> {
  const AvatarSelectionView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
        title: const Text('创建分身'),
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const Spacer(),
              const Text(
                '你将如何使用这台人偶？',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(flex: 2),
              CustomButton(
                text: '我要创建分身',
                onPressed: () {
                  // 设置为创建分身模式
                  controller.nextStep();
                  // 直接导航到性格与背景基础信息页面
                  Get.toNamed(Routes.AVATAR_BASIC_INFO);
                },
                height: 56,
              ),
              const SizedBox(height: 16),
              CustomButton(
                text: '邀请他人来创建分身',
                onPressed: () {
                  // 导航到邀请页面
                  Get.toNamed(Routes.AVATAR_INVITE);
                },
                height: 56,
                color: Colors.grey[200],
                textColor: Colors.black87,
              ),
              const Spacer(),
            ],
          ),
        ),
      ),
    );
  }
}

import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/voice_selection_controller.dart';
import '../../../routes/app_routes.dart';

/// 音色设置页面 - 音色选择和聊天记录上传（音色克隆跳转到专门页c面）
class AvatarVoiceView extends GetView<VoiceSelectionController> {
  const AvatarVoiceView({super.key});

  @override
  Widget build(BuildContext context) {
    // 每次构建时检测测试模式，确保控制器状态正确
    controller.checkTestMode();

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Get.back(),
        ),
        title: const Text(
          '设置音色',
          style: TextStyle(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.w500,
          ),
        ),
        centerTitle: true,
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            children: [
              // 页面说明
              const Text(
                '请选择下列一种方式设置音色',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w700,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 20),

              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      // 选择音色
                      _buildVoiceSelectionSection(),

                      const SizedBox(height: 40),

                      // 复刻音色 - 跳转到专门页面
                      _buildVoiceCloneButton(),

                      const SizedBox(height: 40),

                      // 上传音频
                      _buildAudioUploadSection(),
                    ],
                  ),
                ),
              ),

              // 底部按钮
              Obx(() => Container(
                    width: double.infinity,
                    height: 50,
                    decoration: BoxDecoration(
                      color: _canProceed() ? Colors.blue : Colors.grey[300],
                      borderRadius: BorderRadius.circular(25),
                    ),
                    child: TextButton(
                      onPressed: _canProceed() ? _handleSubmit : null,
                      child: Text(
                        _getButtonText(),
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: _canProceed() ? Colors.white : Colors.black87,
                        ),
                      ),
                    ),
                  )),
            ],
          ),
        ),
      ),
    );
  }

  /// 选择音色区域
  Widget _buildVoiceSelectionSection() {
    return Obx(() => Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                // 单选按钮
                GestureDetector(
                  onTap: () => controller.selectedVoiceType.value = 1,
                  child: Container(
                    width: 20,
                    height: 20,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: controller.selectedVoiceType.value == 1
                            ? Colors.blue
                            : Colors.grey,
                        width: 2,
                      ),
                      color: controller.selectedVoiceType.value == 1
                          ? Colors.blue
                          : Colors.transparent,
                    ),
                    child: controller.selectedVoiceType.value == 1
                        ? const Icon(Icons.check, size: 12, color: Colors.white)
                        : null,
                  ),
                ),
                const SizedBox(width: 12),
                const Text(
                  '选择音色:',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            // 音色选择卡片
            Obx(() => GestureDetector(
                  onTap: () {
                    controller.selectedVoiceType.value = 1;
                    Get.toNamed(Routes.VOICE_SELECTION);
                  },
                  child: Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.grey[200],
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      children: [
                        // 播放按钮
                        Container(
                          width: 40,
                          height: 40,
                          decoration: const BoxDecoration(
                            color: Colors.grey,
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.play_arrow,
                            color: Colors.white,
                            size: 20,
                          ),
                        ),
                        const SizedBox(width: 16),
                        // 音色信息
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                controller.selectedVoiceName.value.isNotEmpty
                                    ? controller.selectedVoiceName.value
                                    : '请选择',
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                  color: Colors.black87,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                controller.selectedVoiceId.value.isNotEmpty
                                    ? controller.selectedVoiceId.value
                                    : '',
                                style: const TextStyle(
                                  fontSize: 12,
                                  color: Colors.black54,
                                ),
                              ),
                            ],
                          ),
                        ),
                        // 右箭头
                        const Icon(
                          Icons.arrow_forward_ios,
                          color: Colors.black54,
                          size: 16,
                        ),
                      ],
                    ),
                  ),
                )),
          ],
        ));
  }

  /// 复刻音色按钮 - 跳转到专门的音色克隆页面
  Widget _buildVoiceCloneButton() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Icon(
            Icons.mic,
            size: 48,
            color: Colors.grey[600],
          ),
          const SizedBox(height: 12),
          const Text(
            '复刻音色',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            '录制您的声音，生成专属音色',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              Get.toNamed(Routes.VOICE_CLONE);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(24),
              ),
            ),
            child: const Text('开始录制'),
          ),
        ],
      ),
    );
  }

  /// 上传音频区域
  Widget _buildAudioUploadSection() {
    return Obx(() => Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                // 单选按钮
                GestureDetector(
                  onTap: () => controller.selectedVoiceType.value = 3,
                  child: Container(
                    width: 20,
                    height: 20,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: controller.selectedVoiceType.value == 3
                            ? Colors.blue
                            : Colors.grey,
                        width: 2,
                      ),
                      color: controller.selectedVoiceType.value == 3
                          ? Colors.blue
                          : Colors.transparent,
                    ),
                    child: controller.selectedVoiceType.value == 3
                        ? const Icon(Icons.check, size: 12, color: Colors.white)
                        : null,
                  ),
                ),
                const SizedBox(width: 12),
                const Text(
                  '上传音频:',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            // 上传区域
            _buildUploadArea(),
            const SizedBox(height: 16),
            // 提示文本
            Text(
              '请上传 WAV 格式的音频文件，时长在10-60秒之间，大小不超过20MB',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
          ],
        ));
  }

  /// 构建上传区域
  Widget _buildUploadArea() {
    final audioFile = controller.currentAudioFile.value;
    if (!audioFile.isEmpty) {
      // 已上传状态 - 统一处理本地和远程音频文件
      return Container(
        width: double.infinity,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.grey[200],
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            // 播放按钮
            GestureDetector(
              onTap: () => _handlePlayAudio(),
              child: Container(
                width: 40,
                height: 40,
                decoration: const BoxDecoration(
                  color: Colors.grey,
                  shape: BoxShape.circle,
                ),
                child: Obx(() => Icon(
                      controller.isPlaying.value
                          ? Icons.pause
                          : Icons.play_arrow,
                      color: Colors.white,
                      size: 20,
                    )),
              ),
            ),
            const SizedBox(width: 16),
            // 文件信息
            Expanded(
              child: Text(
                _getUploadedFileName(),
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                ),
              ),
            ),
            // 删除/重新上传按钮
            GestureDetector(
              onTap: () => _handleFileAction(),
              child: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  controller.isTestMode.value
                      ? Icons.refresh
                      : Icons.delete_outline,
                  color: Colors.black54,
                  size: 20,
                ),
              ),
            ),
          ],
        ),
      );
    } else {
      // 未上传状态
      return GestureDetector(
        onTap: () {
          controller.selectedVoiceType.value = 3;
          controller.pickAndUploadAudioFile();
        },
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey[200],
            borderRadius: BorderRadius.circular(12),
          ),
          child: const Row(
            children: [
              Expanded(
                child: Text(
                  '手机文件上传',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Colors.black87,
                  ),
                ),
              ),
              // 右箭头
              Icon(
                Icons.arrow_forward_ios,
                color: Colors.black54,
                size: 16,
              ),
            ],
          ),
        ),
      );
    }
  }

  /// 获取上传文件的文件名 - 使用统一的音频文件模型
  String _getUploadedFileName() {
    final audioFile = controller.currentAudioFile.value;
    return audioFile.isEmpty ? '已上传' : audioFile.displayName;
  }

  /// 处理音频播放 - 使用统一的播放方法
  void _handlePlayAudio() {
    controller.playCurrentAudio();
  }

  /// 处理文件操作（删除/重新上传）
  void _handleFileAction() {
    if (controller.isTestMode.value) {
      // 测试模式：重新上传
      controller.selectedVoiceType.value = 3;
      controller.pickAndUploadAudioFile();
    } else {
      // 新建模式：删除确认
      controller.showDeleteAudioConfirmDialog();
    }
  }

  /// 获取按钮文字
  String _getButtonText() {
    return controller.isTestMode.value ? '完成' : '确定';
  }

  /// 判断是否可以提交
  bool _canProceed() {
    switch (controller.selectedVoiceType.value) {
      case 1: // 选择音色
        return controller.selectedVoiceId.value.isNotEmpty;
      case 3: // 上传音频
        return !controller.currentAudioFile.value.isEmpty;
      default:
        return false;
    }
  }

  /// 处理提交
  Future<void> _handleSubmit() async {
    // 普通模式的提交逻辑
    switch (controller.selectedVoiceType.value) {
      case 1: // 选择音色
        await _submitSelectedVoice();
        break;
      case 3: // 上传音频
        await _submitUploadedAudio();
        break;
      default:
        break;
    }
  }

  /// 提交选择的音色
  Future<void> _submitSelectedVoice() async {
    await controller.uploadVoiceFile(
      type: 1,
      ttsVoice: controller.selectedVoiceId.value,
    );
  }

  /// 提交上传的音频
  Future<void> _submitUploadedAudio() async {
    await controller.uploadVoiceFile(
      type: 3,
      file: controller.currentAudioFile.value.uploadFile,
    );
  }
}

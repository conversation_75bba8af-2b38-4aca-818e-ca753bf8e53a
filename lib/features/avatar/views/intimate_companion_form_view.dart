import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/intimate_companion_controller.dart';

/// 亲密陪伴者信息填写页面
class IntimateCompanionFormView extends GetView<IntimateCompanionController> {
  const IntimateCompanionFormView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('亲密陪伴者设置'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '请按照以下步骤进行亲密陪伴者设置',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 24),
            
            // 分身与你的关系
            _buildSectionTitle('* 分身与你的关系：'),
            const SizedBox(height: 12),
            _buildRelationshipOptions(),

            const SizedBox(height: 24),

            // 唤醒词
            _buildSectionTitle('* 唤醒词：'),
            const SizedBox(height: 12),
            _buildWakeWordDropdown(),

            const SizedBox(height: 24),

            // 你希望分身怎么称呼你
            _buildSectionTitle('* 你希望"分身"怎么称呼你：'),
            const SizedBox(height: 8),
            Text(
              '(可以填写多个称呼，例如：宝贝、亲爱的)',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 12),
            _buildTextField(
              hint: '填写"分身"对你的称呼',
              onChanged: (value) => controller.updateNickname(value),
            ),
            
            const SizedBox(height: 24),
            
            // 姓名
            _buildSectionTitle('* 姓名：'),
            const SizedBox(height: 12),
            _buildTextField(
              hint: '填写你的姓名',
              onChanged: (value) => controller.updateName(value),
            ),

            const SizedBox(height: 24),

            // 性别
            _buildSectionTitle('* 性别：'),
            const SizedBox(height: 12),
            _buildGenderOptions(),

            const SizedBox(height: 24),

            // 年龄
            _buildSectionTitle('* 年龄：'),
            const SizedBox(height: 12),
            _buildTextField(
              hint: '填写分身的年龄',
              keyboardType: TextInputType.number,
              onChanged: (value) => controller.updateAge(value),
            ),

            const SizedBox(height: 24),

            // 职业
            _buildSectionTitle('* 职业：'),
            const SizedBox(height: 12),
            _buildTextField(
              hint: '填写分身的职业',
              onChanged: (value) => controller.updateProfession(value),
            ),
            
            const SizedBox(height: 24),
            
            // 出生日期
            _buildSectionTitle('* 出生日期：'),
            const SizedBox(height: 12),
            _buildDatePicker(),
            
            const SizedBox(height: 24),
            
            // 你们目前的关系状态
            _buildSectionTitle('* 你们目前的关系状态：'),
            const SizedBox(height: 8),
            Text(
              '(例如：我们认识大概3年左右，目前已经相恋2年多，准备今年筹备办婚礼结婚了)',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 12),
            _buildTextField(
              hint: '填写"分身"对你的称呼',
              maxLines: 3,
              onChanged: (value) => controller.updateRelationshipStatus(value),
            ),

            const SizedBox(height: 24),

            // 具体出生时间
            _buildSectionTitle('具体出生时间：'),
            const SizedBox(height: 8),
            Text(
              '(如填写具体出生时间将更有助于系统推算其性格特征，用于角色个性化匹配格式为：xx点xx分 例如：12点32分)',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 12),
            _buildTextField(
              hint: '填写分身具体出生时间，非必填',
              onChanged: (value) => controller.updateBirthTime(value),
            ),

            const SizedBox(height: 24),

            // 出生地点
            _buildSectionTitle('出生地点(城市)：'),
            const SizedBox(height: 8),
            Text(
              '(如填写准确地点将更有助于系统推算其性格特征，用于角色个性化匹配)',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 12),
            _buildTextField(
              hint: '填写分身出生地点（城市），非必填',
              onChanged: (value) => controller.updateBirthPlace(value),
            ),

            const SizedBox(height: 24),

            // 星座
            _buildSectionTitle('星座：'),
            const SizedBox(height: 8),
            Text(
              '(如填写星座将更有助于系统推算其性格特征，用于角色个性化匹配)',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 12),
            _buildZodiacDropdown(),
            
            const SizedBox(height: 24),
            
            // 同意条款
            Obx(() => Row(
              children: [
                Checkbox(
                  value: controller.agreedToTerms.value,
                  onChanged: (value) => controller.agreedToTerms.value = value ?? false,
                ),
                const Text('同意分身的重置，非必填'),
              ],
            )),
            
            const SizedBox(height: 32),
            
            // 下一步按钮
            SizedBox(
              width: double.infinity,
              child: Obx(() => ElevatedButton(
                onPressed: controller.isLoading.value 
                    ? null 
                    : () => controller.submitForm(),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  backgroundColor: Theme.of(context).primaryColor,
                ),
                child: controller.isLoading.value
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Text(
                        '下一步',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
              )),
            ),
            
            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }

  /// 构建章节标题
  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: const TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.w500,
      ),
    );
  }

  /// 构建文本输入框
  Widget _buildTextField({
    required String hint,
    required Function(String) onChanged,
    TextInputType? keyboardType,
    int maxLines = 1,
  }) {
    return TextField(
      onChanged: onChanged,
      keyboardType: keyboardType,
      maxLines: maxLines,
      decoration: InputDecoration(
        hintText: hint,
        hintStyle: TextStyle(color: Colors.grey.shade400),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: Theme.of(Get.context!).primaryColor),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
      ),
    );
  }

  /// 构建关系选项
  Widget _buildRelationshipOptions() {
    final relationshipRows = [
      ['本人', '女朋友', '男朋友', '丈夫', '妻子'],
      ['父亲', '母亲', '儿子', '母亲', '朋友'],
      ['偶像', '自定义']
    ];

    return Obx(() => Column(
      children: relationshipRows.map((relationships) {

        return Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: Row(
            children: relationships.map((relationship) {
              final isSelected = controller.selectedRelationship.value == relationship;
              return Expanded(
                child: GestureDetector(
                  onTap: () => controller.updateRelationship(relationship),
                  child: Container(
                    margin: const EdgeInsets.only(right: 8),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    decoration: BoxDecoration(
                      color: isSelected ? Theme.of(Get.context!).primaryColor : Colors.grey.shade200,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      relationship,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: isSelected ? Colors.white : Colors.black,
                        fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
                        fontSize: 14,
                      ),
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        );
      }).toList(),
    ));
  }



  /// 构建性别选项
  Widget _buildGenderOptions() {
    final genders = ['男', '女'];
    
    return Obx(() => Row(
      children: genders.map((gender) {
        final isSelected = controller.selectedGender.value == gender;
        return Expanded(
          child: GestureDetector(
            onTap: () => controller.updateGender(gender),
            child: Container(
              margin: const EdgeInsets.only(right: 12),
              padding: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: isSelected ? Theme.of(Get.context!).primaryColor : Colors.grey.shade200,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                gender,
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: isSelected ? Colors.white : Colors.black,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            ),
          ),
        );
      }).toList(),
    ));
  }

  /// 构建日期选择器
  Widget _buildDatePicker() {
    return Obx(() => GestureDetector(
      onTap: () => _selectDate(),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Icon(Icons.calendar_today, color: Colors.grey.shade600),
            const SizedBox(width: 12),
            Text(
              controller.selectedBirthDate.value.isEmpty
                  ? '选择你的出生日期'
                  : controller.selectedBirthDate.value,
              style: TextStyle(
                color: controller.selectedBirthDate.value.isEmpty
                    ? Colors.grey.shade400
                    : Colors.black,
              ),
            ),
          ],
        ),
      ),
    ));
  }



  /// 构建唤醒词下拉框
  Widget _buildWakeWordDropdown() {
    return Obx(() => DropdownButtonFormField<String>(
      value: controller.selectedWakeWord.value.isEmpty
          ? null
          : controller.selectedWakeWord.value,
      decoration: InputDecoration(
        hintText: '选择唤醒词',
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: Theme.of(Get.context!).primaryColor),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
      ),
      items: const [
        DropdownMenuItem(value: '你好,totwoo', child: Text('你好,totwoo')),
        DropdownMenuItem(value: '嗨,totwoo', child: Text('嗨,totwoo')),
        DropdownMenuItem(value: 'Hello,totwoo', child: Text('Hello,totwoo')),
      ],
      onChanged: (value) => controller.updateWakeWord(value ?? ''),
    ));
  }

  /// 构建星座下拉框
  Widget _buildZodiacDropdown() {
    return Obx(() => DropdownButtonFormField<String>(
      value: controller.selectedZodiac.value.isEmpty
          ? null
          : controller.selectedZodiac.value,
      decoration: InputDecoration(
        hintText: '选择分身的星座，非必填',
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: Theme.of(Get.context!).primaryColor),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
      ),
      items: const [
        DropdownMenuItem(value: '白羊座', child: Text('白羊座')),
        DropdownMenuItem(value: '金牛座', child: Text('金牛座')),
        DropdownMenuItem(value: '双子座', child: Text('双子座')),
        DropdownMenuItem(value: '巨蟹座', child: Text('巨蟹座')),
        DropdownMenuItem(value: '狮子座', child: Text('狮子座')),
        DropdownMenuItem(value: '处女座', child: Text('处女座')),
        DropdownMenuItem(value: '天秤座', child: Text('天秤座')),
        DropdownMenuItem(value: '天蝎座', child: Text('天蝎座')),
        DropdownMenuItem(value: '射手座', child: Text('射手座')),
        DropdownMenuItem(value: '摩羯座', child: Text('摩羯座')),
        DropdownMenuItem(value: '水瓶座', child: Text('水瓶座')),
        DropdownMenuItem(value: '双鱼座', child: Text('双鱼座')),
      ],
      onChanged: (value) => controller.updateZodiac(value ?? ''),
    ));
  }

  /// 选择日期
  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: Get.context!,
      initialDate: DateTime.now(),
      firstDate: DateTime(1900),
      lastDate: DateTime.now(),
    );

    if (picked != null) {
      controller.updateBirthDate(
        "${picked.year}-${picked.month.toString().padLeft(2, '0')}-${picked.day.toString().padLeft(2, '0')}"
      );
    }
  }


}

import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

import '../../../data/models/user_file_model.dart';
import '../controllers/chat_file_controller.dart';
import '../../../shared/widgets/custom_button.dart';
import '../../../routes/app_routes.dart';



/// 上传聊天文件页面 - 重新设计逻辑
class AvatarChatFileView extends GetView<ChatFileController> {
   const AvatarChatFileView({super.key});



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
        title: const Text('上传聊天文件'),
        centerTitle: true,
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
        actions: Get.parameters['source'] == 'test' ? null : [
          TextButton(
            onPressed: () => Get.offNamed(Routes.AVATAR_VOICE),
            child: const Text(
              '跳过',
              style: TextStyle(
                color: Colors.blue,
                fontSize: 16,
              ),
            ),
          ),
        ],
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 页面说明
                      const Center(
                        child: Text(
                          '上传所创建的分身与他人的聊天记录\n将帮助AI分身更精准地学习语言习惯和思维方式\n有效地提升互动体验',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.black87,
                            height: 1.5,
                          ),
                        ),
                      ),
                      const SizedBox(height: 40),

                      // 文件格式说明
                      const Text(
                        '文件建议使用.txt文件，也支持.pdf、.docx格式，大小不超过20M，支持最多上传5份文件。',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey,
                          height: 1.4,
                        ),
                      ),
                      const SizedBox(height: 30),

                      // 文件条目列表
                      Obx(() => _buildFileItemsList()),

                      // 添加按钮
                      _buildAddButton(),
                    ],
                  ),
                ),
              ),

              // 底部说明和按钮
              Column(
                children: [
                  const Text(
                    '您上传的内容仅用于AI模型训练，不会对外公开',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey,
                    ),
                  ),
                  const SizedBox(height: 20),
                  CustomButton(
                    text: Get.parameters['source'] == 'test' ? '完成' : '下一步',
                    onPressed: () {
                      if (Get.parameters['source'] == 'test') {
                        Get.back();
                      } else {
                        // 检查是否有已上传的文件
                        final hasUploadedFiles = controller.userFiles.any(
                          (file) => file.status == FileUploadStatus.uploaded
                        );
                        if (!hasUploadedFiles) {
                          EasyLoading.showError('请先上传文件');
                          return;
                        }
                        Get.offNamed(Routes.AVATAR_VOICE);
                      }
                    },
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建文件列表
  Widget _buildFileItemsList() {
    if (controller.userFiles.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      children: controller.userFiles.asMap().entries.map((entry) {
        final index = entry.key;
        final userFile = entry.value;
        return _buildUserFileCard(index, userFile);
      }).toList(),
    );
  }

  /// 构建用户文件卡片 - 使用原来的样式结构
  Widget _buildUserFileCard(int index, UserFile userFile) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () => controller.handleUserFileTap(index),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: _getUserFileBackgroundColor(userFile.status),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey[200]!),
          ),
          child: _buildUserFileContent(userFile, index),
        ),
      ),
    );
  }

  /// 构建用户文件内容 - 统一处理所有状态
  Widget _buildUserFileContent(UserFile userFile, int index) {
    if (userFile.status == FileUploadStatus.pending) {
      return Row(
        children: [
          const Text('手机文件上传', style: TextStyle(fontSize: 16, color: Colors.black87)),
          const Spacer(),
          Icon(Icons.arrow_forward_ios, size: 16, color: Colors.grey[600]),
        ],
      );
    }

    // 其他状态统一处理
    final uploadDate = userFile.createDateTime != null
        ? DateFormat('yyyy/MM/dd上传').format(userFile.createDateTime!)
        : '';

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: Text(
                userFile.fileName,
                style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500, color: Colors.black87),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            if (userFile.status == FileUploadStatus.uploading)
              const SizedBox(width: 16, height: 16, child: CircularProgressIndicator(strokeWidth: 2))
            else
              TextButton(
                onPressed: () => controller.showDeleteConfirmDialog(index),
                child: const Text('删除', style: TextStyle(color: Colors.red, fontSize: 12)),
              ),
          ],
        ),
        if (uploadDate.isNotEmpty) ...[
          const SizedBox(height: 4),
          Text(uploadDate, style: TextStyle(fontSize: 12, color: Colors.grey[600])),
        ],
        const SizedBox(height: 8),
        Row(
          children: [
            if (userFile.fileSizeText.isNotEmpty) ...[
              Text('大小：${userFile.fileSizeText}', style: TextStyle(fontSize: 12, color: Colors.grey[600])),
              const SizedBox(width: 16),
            ],
            _buildStatusBadge(userFile.status),
          ],
        ),
      ],
    );
  }

  /// 构建状态标签
  Widget _buildStatusBadge(FileUploadStatus status) {
    String text;
    Color bgColor;
    Color textColor;

    switch (status) {
      case FileUploadStatus.pending:
        return const SizedBox.shrink();
      case FileUploadStatus.uploading:
        text = '上传中';
        bgColor = Colors.blue[100]!;
        textColor = Colors.blue[700]!;
        break;
      case FileUploadStatus.uploaded:
        text = '已上传';
        bgColor = Colors.blue[100]!;
        textColor = Colors.blue[700]!;
        break;
      case FileUploadStatus.failed:
        text = '上传失败';
        bgColor = Colors.red[100]!;
        textColor = Colors.red[700]!;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(color: bgColor, borderRadius: BorderRadius.circular(4)),
      child: Text(text, style: TextStyle(fontSize: 10, color: textColor)),
    );
  }



  /// 获取用户文件背景色
  Color _getUserFileBackgroundColor(FileUploadStatus status) {
    switch (status) {
      case FileUploadStatus.pending:
        return Colors.grey[100]!;
      case FileUploadStatus.uploading:
        return Colors.blue[50]!;
      case FileUploadStatus.uploaded:
        return Colors.green[50]!;
      case FileUploadStatus.failed:
        return Colors.red[50]!;
    }
  }








  /// 构建添加按钮
  Widget _buildAddButton() {
    // 当文件数量达到5个时隐藏添加按钮
    if (controller.userFiles.length >= 5) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.only(top: 20),
      child: Center(
        child: InkWell(
          onTap: controller.addUserFile,
          child: Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: Colors.grey[600],
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.add,
              color: Colors.white,
              size: 30,
            ),
          ),
        ),
      ),
    );
  }


}

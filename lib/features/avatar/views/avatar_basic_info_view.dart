import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/avatar_creation_controller.dart';
import '../../../shared/widgets/custom_button.dart';
import '../../../shared/widgets/custom_text_field.dart';
import '../../../routes/app_routes.dart';

/// 性格与背景 - 第一部分：基础信息 - 
class AvatarBasicInfoView extends GetView<AvatarCreationController> {
  const AvatarBasicInfoView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
        title: const Text('性格与背景'),
        centerTitle: true,
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                    // 创建规则说明
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(12),
                      margin: const EdgeInsets.only(bottom: 20),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade50,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            '创建规则：',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 4),
                          const Text(
                            '(1) 您可以创建一个自己的分身，也可以创建他人的分身；',
                            style: TextStyle(fontSize: 12, color: Colors.grey),
                          ),
                          const Text(
                            '(2) 在完成分身创建后，需要您按照下步骤填写关于所创建分身的信息。',
                            style: TextStyle(fontSize: 12, color: Colors.grey),
                          ),
                        ],
                      ),
                    ),

                    // 第一部分标题
                    const Text(
                      '第一部分：基础信息',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 20),
                    // 姓名 - 必填项
                    Row(
                      children: [
                        const Text(
                          '* ',
                          style: TextStyle(color: Colors.red, fontSize: 16),
                        ),
                        const Text(
                          '姓名：',
                          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    CustomTextField(
                      hintText: '填写分身的姓名',
                      onChanged: (value) => controller.dollProfile.update((val) {
                        val?.dollName = value;
                      }),
                    ),
                    const SizedBox(height: 16),

                    // 昵称 - 非必填项
                    Row(
                      children: [
                        const Text(
                          '* ',
                          style: TextStyle(color: Colors.transparent, fontSize: 16),
                        ),
                        const Text(
                          '昵称：',
                          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                        ),
                        const Text(
                          '可以填写多个昵称，如：小明、宝贝、宝宝',
                          style: TextStyle(fontSize: 12, color: Colors.grey),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    CustomTextField(
                      hintText: '填写分身的昵称',
                      onChanged: (value) => controller.dollProfile.update((val) {
                        val?.dollNickName = value;
                      }),
                    ),
                    const SizedBox(height: 16),

                    // 性别 - 必填项
                    Row(
                      children: [
                        const Text(
                          '* ',
                          style: TextStyle(color: Colors.red, fontSize: 16),
                        ),
                        const Text(
                          '性别：',
                          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Obx(() => Row(
                      children: [
                        Expanded(
                          child: _buildGenderButton('男', controller.dollProfile.value.dollGender == '男'),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: _buildGenderButton('女', controller.dollProfile.value.dollGender == '女'),
                        ),
                      ],
                    )),
                    const SizedBox(height: 16),

                    // 年龄 - 必填项
                    Row(
                      children: [
                        const Text(
                          '* ',
                          style: TextStyle(color: Colors.red, fontSize: 16),
                        ),
                        const Text(
                          '年龄：',
                          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    CustomTextField(
                      hintText: '填写分身的年龄',
                      keyboardType: TextInputType.number,
                      onChanged: (value) => controller.dollProfile.update((val) {
                        val?.dollAge = value;
                      }),
                    ),
                    const SizedBox(height: 16),

                    // 职业 - 必填项
                    Row(
                      children: [
                        const Text(
                          '* ',
                          style: TextStyle(color: Colors.red, fontSize: 16),
                        ),
                        const Text(
                          '职业：',
                          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    CustomTextField(
                      hintText: '填写分身的职业',
                      onChanged: (value) => controller.dollProfile.update((val) {
                        val?.dollProfession = value;
                      }),
                    ),
                    const SizedBox(height: 16),

                    // 出生日期 - 必填项
                    Row(
                      children: [
                        const Text(
                          '* ',
                          style: TextStyle(color: Colors.red, fontSize: 16),
                        ),
                        const Text(
                          '出生日期：',
                          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Obx(() => InkWell(
                      onTap: () => _selectDate(context),
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                        decoration: BoxDecoration(
                          color: Colors.grey[200],
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            const Icon(Icons.calendar_today, size: 20),
                            const SizedBox(width: 8),
                            Text(
                              controller.dollProfile.value.dollBirthday?.isNotEmpty == true
                                  ? controller.dollProfile.value.dollBirthday!
                                  : '选择出生日期',
                              style: TextStyle(
                                color: controller.dollProfile.value.dollBirthday?.isNotEmpty == true ? Colors.black : Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      ),
                    )),
                    const SizedBox(height: 16),

                    // 具体出生时间 - 非必填项，带说明
                    const Text(
                      '具体出生时间：',
                      style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                    ),
                    const Text(
                      '(如填写具体出生时间将更有助于系统推算其性格特征，用于角色个性化匹配)',
                      style: TextStyle(fontSize: 12, color: Colors.grey),
                    ),
                    const SizedBox(height: 8),
                    Obx(() => InkWell(
                      onTap: () => _selectTime(context),
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                        decoration: BoxDecoration(
                          color: Colors.grey[200],
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            const Icon(Icons.access_time, size: 20),
                            const SizedBox(width: 8),
                            Text(
                              controller.dollProfile.value.dollBirthdayTime?.isNotEmpty == true
                                  ? controller.dollProfile.value.dollBirthdayTime!
                                  : '填写分身具体出生时间，非必填',
                              style: TextStyle(
                                color: controller.dollProfile.value.dollBirthdayTime?.isNotEmpty == true ? Colors.black : Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      ),
                    )),
                    const SizedBox(height: 16),

                    // 出生地点(城市) - 非必填项，带说明
                    const Text(
                      '出生地点(城市)：',
                      style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                    ),
                    const Text(
                      '(如填写准确地点将更有助于系统推算其性格特征，用于角色个性化匹配)',
                      style: TextStyle(fontSize: 12, color: Colors.grey),
                    ),
                    const SizedBox(height: 8),
                    CustomTextField(
                      hintText: '填写分身出生地点（城市），非必填',
                      onChanged: (value) => controller.dollProfile.update((val) {
                        val?.dollCity = value;
                      }),
                    ),
                    const SizedBox(height: 16),

                    // 星座 - 非必填项，带说明和复选框样式
                    const Text(
                      '星座：',
                      style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                    ),
                    const Text(
                      '(如填写星座将更有助于系统推算其性格特征，用于角色个性化匹配)',
                      style: TextStyle(fontSize: 12, color: Colors.grey),
                    ),
                    const SizedBox(height: 8),
                    Obx(() => InkWell(
                      onTap: () => _selectZodiac(context),
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                        decoration: BoxDecoration(
                          color: Colors.grey[200],
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            Container(
                              width: 20,
                              height: 20,
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.grey),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: controller.dollProfile.value.dollZodiacSign?.isNotEmpty == true
                                  ? const Icon(Icons.check, size: 16, color: Colors.blue)
                                  : null,
                            ),
                            const SizedBox(width: 12),
                            Text(
                              controller.dollProfile.value.dollZodiacSign?.isNotEmpty == true
                                  ? controller.dollProfile.value.dollZodiacSign!
                                  : '选择分身的星座，非必填',
                              style: TextStyle(
                                color: controller.dollProfile.value.dollZodiacSign?.isNotEmpty == true ? Colors.black : Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      ),
                    )),
                  ],
                ),
              ),
            ),

            // 底部按钮 - 只显示下一步，添加底部安全区域
            Padding(
              padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).padding.bottom > 0
                    ? 0
                    : 16, // 如果没有系统导航栏，添加额外间距
              ),
              child: CustomButton(
                text: '下一步',
                onPressed: () {
                  if (_validate()) {
                    Get.offNamed(Routes.AVATAR_ABILITIES);
                  }
                },
              ),
            ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildGenderButton(String gender, bool isSelected) {
    return GestureDetector(
      onTap: () {
        controller.dollProfile.update((val) {
          val?.dollGender = gender;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12),
        decoration: BoxDecoration(
          color: isSelected ? Colors.blue.shade100 : Colors.grey[200],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected ? Colors.blue : Colors.grey.shade300,
          ),
        ),
        child: Text(
          gender,
          textAlign: TextAlign.center,
          style: TextStyle(
            color: isSelected ? Colors.blue : Colors.black,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  Future<void> _selectDate(BuildContext context) async {
    DateTime initialDate = DateTime.now();
    if (controller.dollProfile.value.dollBirthday?.isNotEmpty == true) {
      try {
        initialDate = DateTime.parse(controller.dollProfile.value.dollBirthday!);
      } catch (e) {
        // 解析失败使用默认值
      }
    }

    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: DateTime(1900),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      controller.dollProfile.update((val) {
        val?.dollBirthday = '${picked.year}-${picked.month.toString().padLeft(2, '0')}-${picked.day.toString().padLeft(2, '0')}';
      });
    }
  }

  Future<void> _selectTime(BuildContext context) async {
    TimeOfDay initialTime = TimeOfDay.now();
    if (controller.dollProfile.value.dollBirthdayTime?.isNotEmpty == true) {
      try {
        final parts = controller.dollProfile.value.dollBirthdayTime!.split(':');
        initialTime = TimeOfDay(hour: int.parse(parts[0]), minute: int.parse(parts[1]));
      } catch (e) {
        // 解析失败使用默认值
      }
    }

    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: initialTime,
    );
    if (picked != null) {
      controller.dollProfile.update((val) {
        val?.dollBirthdayTime = '${picked.hour.toString().padLeft(2, '0')}:${picked.minute.toString().padLeft(2, '0')}';
      });
    }
  }

  Future<void> _selectZodiac(BuildContext context) async {
    const zodiacs = ['白羊座', '金牛座', '双子座', '巨蟹座', '狮子座', '处女座', '天秤座', '天蝎座', '射手座', '摩羯座', '水瓶座', '双鱼座'];
    
    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('选择星座'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: zodiacs.length,
            itemBuilder: (context, index) {
              return ListTile(
                title: Text(zodiacs[index]),
                onTap: () {
                  controller.dollProfile.update((val) {
                    val?.dollZodiacSign = zodiacs[index];
                  });
                  Navigator.pop(context);
                },
              );
            },
          ),
        ),
      ),
    );
  }

  bool _validate() {
    final profile = controller.dollProfile.value;

    // 验证必填项：姓名、性别、年龄、职业、出生日期
    if (profile.dollName?.isEmpty ?? true) {
      Get.snackbar('提示', '请填写分身姓名');
      return false;
    }
    if (profile.dollGender?.isEmpty ?? true) {
      Get.snackbar('提示', '请选择分身性别');
      return false;
    }
    if (profile.dollAge?.isEmpty ?? true) {
      Get.snackbar('提示', '请填写分身年龄');
      return false;
    }
    if (profile.dollProfession?.isEmpty ?? true) {
      Get.snackbar('提示', '请填写分身职业');
      return false;
    }
    if (profile.dollBirthday?.isEmpty ?? true) {
      Get.snackbar('提示', '请选择出生日期');
      return false;
    }
    return true;
  }
}

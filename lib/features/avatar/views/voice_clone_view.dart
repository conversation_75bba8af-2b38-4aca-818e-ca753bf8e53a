import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/voice_clone_controller.dart';

class VoiceCloneView extends StatelessWidget {
  const VoiceCloneView({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(VoiceCloneController());
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Get.back(),
        ),
        title: const Text(
          '复刻音色',
          style: TextStyle(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.w500,
          ),
        ),
        centerTitle: true,
      ),
      body: Obx(() => _buildBody(controller)),
    );
  }

  Widget _buildBody(VoiceCloneController controller) {
    switch (controller.voiceCloneStatus.value) {
      case VoiceCloneStatus.initial:
        return _buildInitialView(controller);
      case VoiceCloneStatus.recording:
        return _buildRecording<PERSON>iew(controller);
      case VoiceCloneStatus.processing:
        return _buildProcessingView(controller);
      case VoiceCloneStatus.failed:
        return _buildFailedView(controller);
      case VoiceCloneStatus.completed:
        return _buildCompletedView(controller);
    }
  }

  /// 初始状态：请朗读
  Widget _buildInitialView(VoiceCloneController controller) {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        children: [
          const SizedBox(height: 60),
          // 状态文本
          Text(
            controller.recordingText.value,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 40),
          // 朗读文本 - 可展开滑动
          Expanded(
            child: Container(
              width: double.infinity,
              margin: const EdgeInsets.only(bottom: 20),
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(12),
              ),
              child: SingleChildScrollView(
                child: Text(
                  controller.readingText.value,
                  style: const TextStyle(
                    fontSize: 16,
                    height: 1.6,
                    color: Colors.black87,
                  ),
                ),
              ),
            ),
          ),
          // 按住朗读按钮
          Listener(
            onPointerDown: (_) => controller.startRecording(),
            onPointerUp: (_) => controller.stopRecording(),
            onPointerMove: (event) {
              // 检查是否上滑出屏幕取消录音
              if (event.localPosition.dy < -50) {
                controller.cancelRecording();
              }
            },
            onPointerCancel: (_) => controller.cancelRecording(),
            child: Obx(() => Container(
              width: double.infinity,
              height: 60,
              decoration: BoxDecoration(
                color: controller.isButtonPressed.value ? Colors.blue[700] : Colors.blue,
                borderRadius: BorderRadius.circular(30),
                boxShadow: [
                  BoxShadow(
                    color: Colors.blue.withOpacity(0.3),
                    blurRadius: controller.isButtonPressed.value ? 4 : 8,
                    offset: Offset(0, controller.isButtonPressed.value ? 2 : 4),
                  ),
                ],
              ),
              child: const Center(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.mic,
                      color: Colors.white,
                      size: 20,
                    ),
                    SizedBox(width: 8),
                    Text(
                      '按住朗读',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
            )),
          ),
          const SizedBox(height: 40),
        ],
      ),
    );
  }


  /// 录音状态
  Widget _buildRecordingView(VoiceCloneController controller) {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        children: [
          const SizedBox(height: 60),
          // 状态文本
          Text(
            controller.recordingText.value,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 40),
          // 朗读文本 - 可展开滑动
          Expanded(
            child: Container(
              width: double.infinity,
              margin: const EdgeInsets.only(bottom: 20),
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(12),
              ),
              child: SingleChildScrollView(
                child: Text(
                  controller.readingText.value,
                  style: const TextStyle(
                    fontSize: 16,
                    height: 1.6,
                    color: Colors.black87,
                  ),
                ),
              ),
            ),
          ),
          const Spacer(),
          // 录音进度提示
          const Text(
            '松手发送，上滑出屏幕取消',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 16),
          // 录音进度条
          Container(
            width: double.infinity,
            height: 50,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(25),
            ),
            child: Stack(
              children: [
                // 进度条背景
                Container(
                  width: double.infinity,
                  height: 50,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(25),
                  ),
                ),
                // 进度条
                AnimatedContainer(
                  duration: const Duration(milliseconds: 100),
                  width: MediaQuery.of(Get.context!).size.width *
                         controller.recordingProgress.value * 0.85, // 减去padding
                  height: 50,
                  decoration: BoxDecoration(
                    color: Colors.blue[300],
                    borderRadius: BorderRadius.circular(25),
                  ),
                ),
                // 进度条纹理
                Container(
                  width: double.infinity,
                  height: 50,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(25),
                  ),
                  child: const Center(
                    child: Text(
                      '||||||||||||||||||||||||||||||||||||||||',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.black54,
                        letterSpacing: 1,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 40),
        ],
      ),
    );
  }

  /// 处理状态
  Widget _buildProcessingView(VoiceCloneController controller) {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        children: [
          const SizedBox(height: 60),
          // 状态文本
          Text(
            controller.recordingText.value,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 40),
          // 朗读文本 - 可展开滑动
          Expanded(
            child: Container(
              width: double.infinity,
              margin: const EdgeInsets.only(bottom: 20),
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(12),
              ),
              child: SingleChildScrollView(
                child: Text(
                  controller.readingText.value,
                  style: const TextStyle(
                    fontSize: 16,
                    height: 1.6,
                    color: Colors.black87,
                  ),
                ),
              ),
            ),
          ),
          const Spacer(),
          // 按住朗读按钮（禁用状态）
          Container(
            width: double.infinity,
            height: 50,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(25),
            ),
            child: const Center(
              child: Text(
                '按住朗读',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Colors.black54,
                ),
              ),
            ),
          ),
          const SizedBox(height: 40),
        ],
      ),
    );
  }

  /// 失败状态
  Widget _buildFailedView(VoiceCloneController controller) {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        children: [
          const SizedBox(height: 60),
          // 状态文本
          Text(
            controller.recordingText.value,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 40),
          // 朗读文本 - 可展开滑动
          Expanded(
            child: Container(
              width: double.infinity,
              margin: const EdgeInsets.only(bottom: 20),
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(12),
              ),
              child: SingleChildScrollView(
                child: Text(
                  controller.readingText.value,
                  style: const TextStyle(
                    fontSize: 16,
                    height: 1.6,
                    color: Colors.black87,
                  ),
                ),
              ),
            ),
          ),
          const Spacer(),
          // 提示文本
          const Text(
            '松手发送，上滑出屏幕取消',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 16),
          // 按住朗读按钮
          Listener(
            onPointerDown: (_) => controller.startRecording(),
            onPointerUp: (_) => controller.stopRecording(),
            onPointerMove: (event) {
              // 检查是否上滑出屏幕取消录音
              if (event.localPosition.dy < -50) {
                controller.cancelRecording();
              }
            },
            onPointerCancel: (_) => controller.cancelRecording(),
            child: Obx(() => Container(
              width: double.infinity,
              height: 60,
              decoration: BoxDecoration(
                color: controller.isButtonPressed.value ? Colors.blue[700] : Colors.blue,
                borderRadius: BorderRadius.circular(30),
                boxShadow: [
                  BoxShadow(
                    color: Colors.blue.withOpacity(0.3),
                    blurRadius: controller.isButtonPressed.value ? 4 : 8,
                    offset: Offset(0, controller.isButtonPressed.value ? 2 : 4),
                  ),
                ],
              ),
              child: const Center(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.mic,
                      color: Colors.white,
                      size: 20,
                    ),
                    SizedBox(width: 8),
                    Text(
                      '按住朗读',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
            )),
          ),
          const SizedBox(height: 40),
        ],
      ),
    );
  }

  /// 完成状态
  Widget _buildCompletedView(VoiceCloneController controller) {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        children: [
          const SizedBox(height: 120),
          // 完成标题
          const Text(
            '已完成分身创建',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 40),
          // 提示文本
          const Text(
            '若人偶在你身边，可以试着与人偶对话，\n验证人偶是否符合你的预期~',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 16,
              height: 1.5,
              color: Colors.black54,
            ),
          ),
          const Spacer(),
          // 底部按钮
          Row(
            children: [
              // 跳过按钮
              Expanded(
                child: GestureDetector(
                  onTap: () => controller.skipVoiceClone(),
                  child: Container(
                    height: 50,
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(25),
                    ),
                    child: const Center(
                      child: Text(
                        '跳过',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: Colors.black87,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              // 开始测试按钮
              Expanded(
                child: GestureDetector(
                  onTap: () => controller.startTest(),
                  child: Container(
                    height: 50,
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(25),
                    ),
                    child: const Center(
                      child: Text(
                        '开始测试',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: Colors.black87,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 40),
        ],
      ),
    );
  }
}

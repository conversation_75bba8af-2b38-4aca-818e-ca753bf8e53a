import 'package:get/get.dart';
import '../controllers/voice_selection_controller.dart';
import '../../../data/repositories/avatar_repository.dart';
import '../../../core/services/app_state_service.dart';

/// 音色选择功能绑定
class VoiceSelectionBinding extends Bindings {
  @override
  void dependencies() {
    // 注册必要的依赖
    if (!Get.isRegistered<AvatarRepository>()) {
      Get.lazyPut<AvatarRepository>(
        () => AvatarRepository(),
        fenix: true,
      );
    }

    if (!Get.isRegistered<AppStateService>()) {
      Get.lazyPut<AppStateService>(
        () => AppStateService(),
        fenix: true,
      );
    }

    // 注册音色选择控制器 - 确保全局单例
    if (!Get.isRegistered<VoiceSelectionController>()) {
      Get.put<VoiceSelectionController>(
        VoiceSelectionController(),
        permanent: true, // 永久保持，不会被销毁
      );
    }
  }
}

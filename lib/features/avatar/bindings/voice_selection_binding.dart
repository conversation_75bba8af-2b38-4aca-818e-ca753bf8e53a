import 'package:get/get.dart';
import '../controllers/voice_selection_controller.dart';
import '../../../data/repositories/avatar_repository.dart';
import '../../../core/services/app_state_service.dart';

/// 音色选择功能绑定
class VoiceSelectionBinding extends Bindings {
  @override
  void dependencies() {
    // 注册必要的依赖
    if (!Get.isRegistered<AvatarRepository>()) {
      Get.lazyPut<AvatarRepository>(
        () => AvatarRepository(),
        fenix: true,
      );
    }

    if (!Get.isRegistered<AppStateService>()) {
      Get.lazyPut<AppStateService>(
        () => AppStateService(),
        fenix: true,
      );
    }

    // 注册音色选择控制器 - 使用fenix模式，页面销毁时自动清理，但可以在需要时重新创建
    Get.lazyPut<VoiceSelectionController>(
      () => VoiceSelectionController(),
      fenix: true, // 页面销毁时清理，但保持状态直到真正不需要
    );
  }
}

import 'package:get/get.dart';
import '../controllers/intimate_companion_controller.dart';
import '../controllers/voice_print_controller.dart';
import '../../../data/repositories/avatar_repository.dart';

/// 亲密陪伴者绑定
class IntimateCompanionBinding extends Bindings {
  @override
  void dependencies() {
    // 注册仓库
    Get.lazyPut<AvatarRepository>(() => AvatarRepository());
    
    // 注册控制器
    Get.lazyPut<IntimateCompanionController>(() => IntimateCompanionController());
    Get.lazyPut<VoicePrintController>(() => VoicePrintController());
  }
}

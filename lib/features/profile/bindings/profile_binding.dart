import 'package:get/get.dart';
import 'package:smartai/features/avatar/controllers/avatar_creation_controller.dart';
import '../../../core/services/app_state_service.dart';
import '../../../data/repositories/avatar_repository.dart';
import '../../../data/repositories/user_repository.dart';
import '../controllers/profile_controller.dart';

/// 个人资料绑定
class ProfileBinding implements Bindings {
  @override
  void dependencies() {
    // 注册UserRepository
    Get.lazyPut<UserRepository>(
      () => UserRepository(),
      fenix: true,
    );
    // 注册avatarRepository
    Get.lazyPut<AvatarRepository>(() => AvatarRepository(), fenix: true);
    // 注册ProfileController
    Get.lazyPut<ProfileController>(() => ProfileController(), fenix: true);
    // 注册AvatarController
    Get.lazyPut<AvatarCreationController>(() => AvatarCreationController(), fenix: true);
  }
}

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import '../../../data/models/user_model.dart';
import '../../../data/models/partner_model.dart';
import '../../../data/repositories/user_repository.dart';
import '../../../data/repositories/avatar_repository.dart';
import '../../../core/services/app_state_service.dart';
import '../../../core/utils/logger.dart';

/// 个人资料控制器 - 重构版本
/// 使用真实API调用获取用户信息
class ProfileController extends GetxController {
  final UserRepository _userRepository = Get.find<UserRepository>();
  final AvatarRepository _avatarRepository = Get.find<AvatarRepository>();
  final AppStateService _appStateService = Get.find<AppStateService>();

  // 加载状态
  final RxBool isLoading = false.obs;

  // 编辑模式
  final RxBool isEditMode = false.obs;

  // 表单控制器
  late TextEditingController nameController;
  late TextEditingController emailController;
  late TextEditingController phoneController;

  // 邀请相关
  late TextEditingController inviteEmailController;
  final RxList<PartnerModel> invitationList = <PartnerModel>[].obs;
  final RxBool isInviting = false.obs;
  final RxBool isLoadingInvitations = false.obs;
  final RxString inviteEmailError = ''.obs;

  // 代理到AppStateService的用户信息
  Rx<UserModel?> get user => _appStateService.user;

  // 邮箱验证状态
  bool get isInviteEmailValid =>
      inviteEmailController.text.isNotEmpty &&
      _isEmailValid(inviteEmailController.text);

  // 检查邮箱格式是否有效
  bool _isEmailValid(String email) {
    final emailRegExp =
        RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');
    return emailRegExp.hasMatch(email);
  }

  @override
  void onInit() {
    super.onInit();
    _initControllers();
    _loadUserProfile();
    _loadInvitationList();
  }

  @override
  void onClose() {
    _disposeControllers();
    super.onClose();
  }

  // 初始化控制器
  void _initControllers() {
    nameController = TextEditingController();
    emailController = TextEditingController();
    phoneController = TextEditingController();
    inviteEmailController = TextEditingController();

    // 监听邮箱输入变化
    inviteEmailController.addListener(_validateInviteEmail);
  }

  // 释放控制器资源
  void _disposeControllers() {
    nameController.dispose();
    emailController.dispose();
    phoneController.dispose();
    inviteEmailController.dispose();
  }

  // 验证邀请邮箱
  void _validateInviteEmail() {
    final email = inviteEmailController.text.trim();
    if (email.isEmpty) {
      inviteEmailError.value = '';
    } else if (!_isEmailValid(email)) {
      inviteEmailError.value = '请输入正确的邮箱格式';
    } else {
      inviteEmailError.value = '';
    }
  }

  /// 加载用户资料 - 使用真实API
  Future<void> _loadUserProfile() async {
    final userId = _appStateService.currentUserId;
    if (userId == null) {
      AppLogger.warning('用户ID为空，无法加载用户资料');
      return;
    }

    try {
      isLoading.value = true;
      AppLogger.info('开始加载用户资料，userId: $userId');

      // 调用真实API获取用户信息

      // 更新表单控制器
      if (user.value != null) {
        _updateControllers(user.value!);
        AppLogger.info('用户资料加载成功');
      }
    } catch (e) {
      AppLogger.error('加载用户资料失败', e);
      EasyLoading.showError('加载资料失败');
    } finally {
      isLoading.value = false;
    }
  }

  /// 加载邀请列表
  Future<void> _loadInvitationList() async {
    final userId = _appStateService.currentUserId;
    if (userId == null) {
      AppLogger.warning('用户ID为空，无法加载邀请列表');
      return;
    }

    try {
      isLoadingInvitations.value = true;
      AppLogger.info('开始加载邀请列表，userId: $userId');

      final result =
          await _avatarRepository.getInviteIntimateCompanionInfo(userId);
      if (result != null) {
        invitationList.assignAll(result);
        AppLogger.info('邀请列表加载成功，数量: ${result.length}');
      }
    } catch (e) {
      AppLogger.error('加载邀请列表失败', e);
      EasyLoading.showError('加载邀请列表失败');
    } finally {
      isLoadingInvitations.value = false;
    }
  }

  /// 邀请亲密陪伴者
  Future<void> inviteCompanion() async {
    if (!isInviteEmailValid) return;

    final email = inviteEmailController.text.trim();

    // 检查是否已经邀请过
    final existingInvite =
        invitationList.firstWhereOrNull((partner) => partner.email == email);
    if (existingInvite != null) {
      EasyLoading.showError('该邮箱已被邀请');
      return;
    }

    try {
      isInviting.value = true;
      AppLogger.info('开始邀请亲密陪伴者: $email');

      final partner = PartnerModel(
        userId: _appStateService.currentUserId,
        email: email,
        status: PartnerModel.STATUS_INVITING,
        intimacyType: PartnerModel.INTIMACY_TYPE_INTIMATE,
        createTime: DateTime.now().millisecondsSinceEpoch,
      );

      final result =
          await _avatarRepository.inviteIntimateCompanionInfo([partner]);

      if (result != null && result[email] == true) {
        // 邀请成功，清空输入框并刷新列表
        inviteEmailController.clear();
        EasyLoading.showSuccess('邀请发送成功');
        await _loadInvitationList();
        AppLogger.info('邀请发送成功: $email');
      } else {
        EasyLoading.showError('邀请发送失败');
        AppLogger.error('邀请发送失败: $email', null);
      }
    } catch (e) {
      AppLogger.error('邀请亲密陪伴者失败', e);
      EasyLoading.showError('邀请发送失败，请重试');
    } finally {
      isInviting.value = false;
    }
  }

  /// 取消邀请
  Future<void> cancelInvitation(PartnerModel partner) async {
    if (!partner.canCancel) {
      EasyLoading.showError('该邀请无法取消');
      return;
    }

    try {
      isLoadingInvitations.value = true;
      AppLogger.info('开始取消邀请: ${partner.email}');

      final result =
          await _avatarRepository.cancelInviteIntimateCompanionInfo([partner]);

      if (result != null && result[partner.email] == true) {
        EasyLoading.showSuccess('邀请已取消');
        await _loadInvitationList();
        AppLogger.info('邀请取消成功: ${partner.email}');
      } else {
        EasyLoading.showError('取消邀请失败');
        AppLogger.error('取消邀请失败: ${partner.email}', null);
      }
    } catch (e) {
      AppLogger.error('取消邀请失败', e);
      EasyLoading.showError('取消邀请失败，请重试');
    } finally {
      isLoadingInvitations.value = false;
    }
  }

  /// 刷新邀请列表
  Future<void> refreshInvitations() async {
    await _loadInvitationList();
  }

  /// 根据用户信息更新表单控制器
  void _updateControllers(UserModel user) {
    nameController.text = user.userName ?? '';
    emailController.text = user.email ?? '';
  }

  /// 进入编辑模式
  void enterEditMode() {
    isEditMode.value = true;
  }

  /// 取消编辑
  void cancelEdit() {
    if (user.value != null) {
      _updateControllers(user.value!);
    }
    isEditMode.value = false;
  }

  /// 保存用户资料
  Future<void> saveProfile() async {
    try {
      isLoading.value = true;

      if (user.value == null) return;

      // 构建更新后的用户模型
      final updatedUser = user.value!.copyWith(
        userName: nameController.text.trim(),
        email: emailController.text.trim(),
      );

      // 模拟API请求
      await Future.delayed(Duration(seconds: 1));

      // 更新本地用户数据
      user.value = updatedUser;

      // 退出编辑模式
      isEditMode.value = false;

      // 显示成功提示
      EasyLoading.showSuccess('资料已更新');
    } catch (e) {
      AppLogger.error('保存用户资料失败', e);
      EasyLoading.showError('保存失败');
    } finally {
      isLoading.value = false;
    }
  }
}

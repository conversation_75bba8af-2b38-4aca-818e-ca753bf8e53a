import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../controllers/profile_controller.dart';
import '../../../routes/app_routes.dart';

class ProfileDetailView extends GetView<ProfileController> {
  const ProfileDetailView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('个人资料'),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.toNamed(Routes.EDIT_PROFILE),
            child: const Text('编辑'),
          ),
        ],
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return const Center(child: CircularProgressIndicator());
        }
        return _buildProfileContent(context);
      }),
    );
  }

  // 构建个人资料内容
  Widget _buildProfileContent(BuildContext context) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.r),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // 头像
          _buildAvatar(context),
          SizedBox(height: 24.h),

          // 用户信息
          _buildUserInfoCard(context),
          SizedBox(height: 24.h),

          // 用户角色
          _buildUserRoleCard(context),
        ],
      ),
    );
  }

  // 头像
  Widget _buildAvatar(BuildContext context) {
    return Center(
      child: Obx(() {
        final avatarUrl = controller.user.value?.avatar;
        return CircleAvatar(
          radius: 50.r,
          backgroundColor: Colors.grey[200],
          backgroundImage:
              avatarUrl != null && !avatarUrl.contains('default_avatar')
                  ? NetworkImage(avatarUrl)
                  : null,
          child: avatarUrl == null || avatarUrl.contains('default_avatar')
              ? Icon(
                  Icons.person,
                  size: 50.r,
                  color: Colors.grey[400],
                )
              : null,
        );
      }),
    );
  }

  // 用户信息卡片
  Widget _buildUserInfoCard(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '基本信息',
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16.h),

            // 昵称
            _buildInfoRow(
              context,
              label: '昵称',
              value: controller.user.value?.userName ?? '未设置',
              icon: Icons.person,
            ),
            Divider(height: 24.h),

            // 手机号
            _buildInfoRow(
              context,
              label: '手机号',
              value:  '未绑定',
              icon: Icons.phone,
            ),
            Divider(height: 24.h),

            // 邮箱
            _buildInfoRow(
              context,
              label: '邮箱',
              value: controller.user.value?.email ?? '未设置',
              icon: Icons.email,
            ),
          ],
        ),
      ),
    );
  }

  // 用户角色卡片
  Widget _buildUserRoleCard(BuildContext context) {
    // 简化为固定角色，之前是从controller.userType中获取
    const String roleText = '普通用户';
    const IconData roleIcon = Icons.person_outline;
    const Color roleColor = Colors.blue;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '用户角色',
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16.h),
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(8.r),
                  decoration: BoxDecoration(
                    color: roleColor.withOpacity(0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    roleIcon,
                    color: roleColor,
                    size: 24.r,
                  ),
                ),
                SizedBox(width: 16.w),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      roleText,
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      "可以查看和管理个人信息",
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // 信息行
  Widget _buildInfoRow(
    BuildContext context, {
    required String label,
    required String value,
    required IconData icon,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          color: Colors.grey[600],
          size: 20.r,
        ),
        SizedBox(width: 12.w),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.grey[600],
              ),
            ),
            SizedBox(height: 4.h),
            Text(
              value,
              style: TextStyle(
                fontSize: 16.sp,
              ),
            ),
          ],
        ),
      ],
    );
  }
}

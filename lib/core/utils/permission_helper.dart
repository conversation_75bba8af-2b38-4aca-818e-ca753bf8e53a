import 'dart:io';
import 'package:permission_handler/permission_handler.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'logger.dart';

/// 权限管理工具类 - 简化权限处理逻辑
class PermissionHelper {
  static final DeviceInfoPlugin _deviceInfo = DeviceInfoPlugin();

  /// 检查并请求蓝牙扫描所需权限
  static Future<bool> requestBluetoothPermissions() async {
    try {
      AppLogger.info('开始检查蓝牙权限');
      
      // 获取Android版本
      final androidInfo = await _deviceInfo.androidInfo;
      final sdkInt = androidInfo.version.sdkInt;
      
      List<Permission> requiredPermissions = [];
      
      if (sdkInt >= 31) {
        // Android 12+ (API 31+) 需要新的蓝牙权限
        requiredPermissions = [
          Permission.bluetoothScan,
          Permission.bluetoothConnect,
          Permission.location,
        ];
        AppLogger.info('Android 12+，使用新蓝牙权限');
      } else {
        // Android 12以下需要位置权限
        requiredPermissions = [
          Permission.bluetooth,
          Permission.location,
        ];
        AppLogger.info('Android 12以下，需要位置权限');
      }

      // 检查权限状态
      Map<Permission, PermissionStatus> statuses = {};
      for (Permission permission in requiredPermissions) {
        statuses[permission] = await permission.status;
      }

      // 找出需要申请的权限
      List<Permission> needRequest = [];
      for (Permission permission in requiredPermissions) {
        if (!statuses[permission]!.isGranted) {
          needRequest.add(permission);
        }
      }

      if (needRequest.isEmpty) {
        AppLogger.info('所有权限已授权');
        return true;
      }

      // 申请权限
      AppLogger.info('申请权限: ${needRequest.map((p) => p.toString()).join(', ')}');
      final requestStatuses = await needRequest.request();

      // 检查申请结果
      bool allGranted = requestStatuses.values.every((status) => status.isGranted);
      
      if (allGranted) {
        AppLogger.info('权限申请成功');
        return true;
      } else {
        AppLogger.error('权限申请失败', null);
        return false;
      }

    } catch (e) {
      AppLogger.error('权限检查失败', e);
      return false;
    }
  }

  /// 检查权限是否被永久拒绝
  static Future<bool> isBluetoothPermissionPermanentlyDenied() async {
    try {
      final androidInfo = await _deviceInfo.androidInfo;
      final sdkInt = androidInfo.version.sdkInt;
      
      List<Permission> permissions = [];
      
      if (sdkInt >= 31) {
        permissions = [Permission.bluetoothScan, Permission.bluetoothConnect];
      } else {
        permissions = [Permission.bluetooth, Permission.location];
      }

      for (Permission permission in permissions) {
        if (await permission.isPermanentlyDenied) {
          return true;
        }
      }
      
      return false;
    } catch (e) {
      AppLogger.error('检查权限永久拒绝状态失败', e);
      return false;
    }
  }

  /// 打开应用设置页面
  static Future<void> openSettings() async {
    try {
      await openAppSettings();
      AppLogger.info('已打开应用设置页面');
    } catch (e) {
      AppLogger.error('打开应用设置失败', e);
    }
  }
}

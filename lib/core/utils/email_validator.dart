/// 邮箱校验工具类
class EmailValidator {
  /// 邮箱格式校验
  /// 
  /// 校验规则：
  /// 1. 必须包含且仅包含1个@符号
  /// 2. @符号前后都必须有内容
  /// 3. 开头不能是符号（.、_、%、+、-）
  /// 4. 必须包含至少1个.符号在@符号之后
  /// 5. 基本的邮箱格式校验
  static ValidationResult validate(String email) {
    // 空值检查
    if (email.isEmpty) {
      return ValidationResult(false, '请输入邮箱地址');
    }

    // 去除首尾空格
    email = email.trim();

    // 检查是否包含@符号
    final atCount = email.split('@').length - 1;
    if (atCount == 0) {
      return ValidationResult(false, '邮箱账号错误');
    }
    if (atCount > 1) {
      return ValidationResult(false, '邮箱账号错误');
    }

    // 分割@符号前后部分
    final parts = email.split('@');
    final localPart = parts[0];
    final domainPart = parts[1];

    // 检查@符号前后是否有内容
    if (localPart.isEmpty || domainPart.isEmpty) {
      return ValidationResult(false, '邮箱账号错误');
    }

    // 检查开头是否为符号
    final invalidStartChars = ['.', '_', '%', '+', '-'];
    if (invalidStartChars.contains(localPart[0])) {
      return ValidationResult(false, '邮箱账号错误');
    }

    // 检查域名部分是否包含.符号
    if (!domainPart.contains('.')) {
      return ValidationResult(false, '邮箱账号错误');
    }

    // 使用正则表达式进行更严格的邮箱格式校验
    final emailRegex = RegExp(
      r'^[a-zA-Z0-9][a-zA-Z0-9._-]*[a-zA-Z0-9]@[a-zA-Z0-9][a-zA-Z0-9.-]*\.[a-zA-Z]{2,}$'
    );

    // 特殊情况：本地部分只有一个字符时，不能以符号结尾
    if (localPart.length == 1) {
      final singleCharRegex = RegExp(r'^[a-zA-Z0-9]@[a-zA-Z0-9][a-zA-Z0-9.-]*\.[a-zA-Z]{2,}$');
      if (!singleCharRegex.hasMatch(email)) {
        return ValidationResult(false, '邮箱账号错误');
      }
    } else {
      if (!emailRegex.hasMatch(email)) {
        return ValidationResult(false, '邮箱账号错误');
      }
    }

    return ValidationResult(true, '');
  }

  /// 简单的邮箱格式检查（用于实时校验）
  static bool isValidFormat(String email) {
    return validate(email).isValid;
  }
}

/// 校验结果类
class ValidationResult {
  final bool isValid;
  final String errorMessage;

  ValidationResult(this.isValid, this.errorMessage);
}

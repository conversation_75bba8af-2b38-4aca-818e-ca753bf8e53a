import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';
import 'package:get/get.dart';

/// 安全工具类
/// 提供应用安全相关的工具方法
class SecurityUtils {
  SecurityUtils._();

  /// 生成安全的随机字符串
  static String generateSecureRandomString(int length) {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = Random.secure();
    return String.fromCharCodes(
      Iterable.generate(length, (_) => chars.codeUnitAt(random.nextInt(chars.length))),
    );
  }

  /// 验证邮箱格式
  static bool isValidEmail(String email) {
    if (email.isEmpty) return false;
    return GetUtils.isEmail(email);
  }

  /// 验证密码强度
  static PasswordStrength validatePasswordStrength(String password) {
    if (password.isEmpty) return PasswordStrength.empty;
    if (password.length < 6) return PasswordStrength.tooShort;
    if (password.length < 8) return PasswordStrength.weak;
    
    bool hasUppercase = password.contains(RegExp(r'[A-Z]'));
    bool hasLowercase = password.contains(RegExp(r'[a-z]'));
    bool hasDigits = password.contains(RegExp(r'[0-9]'));
    bool hasSpecialCharacters = password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'));
    
    int score = 0;
    if (hasUppercase) score++;
    if (hasLowercase) score++;
    if (hasDigits) score++;
    if (hasSpecialCharacters) score++;
    
    if (password.length >= 12 && score >= 3) return PasswordStrength.strong;
    if (password.length >= 8 && score >= 2) return PasswordStrength.medium;
    return PasswordStrength.weak;
  }

  /// 获取密码强度描述
  static String getPasswordStrengthDescription(PasswordStrength strength) {
    switch (strength) {
      case PasswordStrength.empty:
        return '请输入密码';
      case PasswordStrength.tooShort:
        return '密码至少需要6位';
      case PasswordStrength.weak:
        return '密码强度：弱';
      case PasswordStrength.medium:
        return '密码强度：中等';
      case PasswordStrength.strong:
        return '密码强度：强';
    }
  }

  /// 生成MD5哈希
  static String generateMD5(String input) {
    return md5.convert(utf8.encode(input)).toString();
  }

  /// 生成SHA256哈希
  static String generateSHA256(String input) {
    return sha256.convert(utf8.encode(input)).toString();
  }

  /// 清理敏感数据（用于日志输出）
  static String sanitizeForLog(String input) {
    if (input.isEmpty) return input;
    
    // 邮箱脱敏
    if (isValidEmail(input)) {
      final parts = input.split('@');
      if (parts.length == 2) {
        final username = parts[0];
        final domain = parts[1];
        final maskedUsername = username.length > 2 
            ? '${username.substring(0, 2)}***'
            : '***';
        return '$maskedUsername@$domain';
      }
    }
    
    // 手机号脱敏
    if (input.length == 11 && input.startsWith(RegExp(r'1[3-9]'))) {
      return '${input.substring(0, 3)}****${input.substring(7)}';
    }
    
    // 其他敏感信息脱敏
    if (input.length > 6) {
      return '${input.substring(0, 3)}***${input.substring(input.length - 3)}';
    }
    
    return '***';
  }

  /// 验证输入是否包含恶意内容
  static bool containsMaliciousContent(String input) {
    // 检查SQL注入
    final sqlInjectionPatterns = [
      r"('|(\\')|(;)|(\\;))",
      r"((\%27)|(\'))((\%6F)|o|(\%4F))((\%72)|r|(\%52))",
      r"((\%27)|(\'))((\%75)|u|(\%55))((\%6E)|n|(\%4E))((\%69)|i|(\%49))((\%6F)|o|(\%4F))((\%6E)|n|(\%4E))",
    ];
    
    for (final pattern in sqlInjectionPatterns) {
      if (RegExp(pattern, caseSensitive: false).hasMatch(input)) {
        return true;
      }
    }
    
    // 检查XSS
    final xssPatterns = [
      r"<script[^>]*>.*?</script>",
      r"javascript:",
      r"on\w+\s*=",
    ];
    
    for (final pattern in xssPatterns) {
      if (RegExp(pattern, caseSensitive: false).hasMatch(input)) {
        return true;
      }
    }
    
    return false;
  }

  /// 清理用户输入
  static String sanitizeInput(String input) {
    if (input.isEmpty) return input;
    
    // 移除潜在的恶意字符
    String cleaned = input
        .replaceAll(RegExp(r'<script[^>]*>.*?</script>', caseSensitive: false), '')
        .replaceAll(RegExp(r'javascript:', caseSensitive: false), '')
        .replaceAll(RegExp(r'on\w+\s*=', caseSensitive: false), '')
        .trim();
    
    return cleaned;
  }

  /// 生成设备指纹
  static String generateDeviceFingerprint() {
    // 在实际应用中，这里应该收集设备信息
    // 如设备型号、操作系统版本、屏幕分辨率等
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = generateSecureRandomString(8);
    return generateSHA256('$timestamp-$random');
  }

  /// 验证Token格式
  static bool isValidToken(String? token) {
    if (token == null || token.isEmpty) return false;
    
    // 简单的Token格式验证
    // 实际应用中应该根据具体的Token格式进行验证
    return token.length >= 10 && !containsMaliciousContent(token);
  }

  /// 生成请求签名
  static String generateRequestSignature(Map<String, dynamic> params, String secret) {
    // 对参数进行排序
    final sortedKeys = params.keys.toList()..sort();
    
    // 构建签名字符串
    final signString = sortedKeys
        .map((key) => '$key=${params[key]}')
        .join('&');
    
    // 添加密钥并生成签名
    final fullString = '$signString&key=$secret';
    return generateSHA256(fullString);
  }

  /// 验证请求签名
  static bool verifyRequestSignature(
    Map<String, dynamic> params,
    String signature,
    String secret,
  ) {
    final expectedSignature = generateRequestSignature(params, secret);
    return expectedSignature == signature;
  }

  /// 检查是否为调试模式
  static bool isDebugMode() {
    bool inDebugMode = false;
    assert(inDebugMode = true);
    return inDebugMode;
  }

  /// 安全的字符串比较（防止时序攻击）
  static bool secureStringCompare(String a, String b) {
    if (a.length != b.length) return false;
    
    int result = 0;
    for (int i = 0; i < a.length; i++) {
      result |= a.codeUnitAt(i) ^ b.codeUnitAt(i);
    }
    
    return result == 0;
  }
}

/// 密码强度枚举
enum PasswordStrength {
  empty,
  tooShort,
  weak,
  medium,
  strong,
}

/// 安全配置类
class SecurityConfig {
  static const int maxLoginAttempts = 5;
  static const Duration loginLockoutDuration = Duration(minutes: 15);
  static const Duration tokenExpiryDuration = Duration(hours: 24);
  static const int minPasswordLength = 6;
  static const int maxPasswordLength = 128;
  static const int sessionTimeoutMinutes = 30;
  
  /// 敏感操作需要重新验证的时间间隔
  static const Duration sensitiveOperationTimeout = Duration(minutes: 5);
  
  /// API请求超时时间
  static const Duration apiTimeout = Duration(seconds: 30);
  
  /// 文件上传大小限制（MB）
  static const int maxFileUploadSizeMB = 10;
}

/// 表单验证工具
class Validators {
  /// 验证电子邮箱
  static String? validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return '请输入电子邮箱';
    }
    
    final emailRegExp = RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');
    if (!emailRegExp.hasMatch(value)) {
      return '请输入有效的电子邮箱';
    }
    
    return null;
  }
  
  /// 验证密码
  static String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return '请输入密码';
    }
    
    if (value.length < 5) {
      return '密码长度不能少于5位';
    }
    
    return null;
  }
  
  /// 验证确认密码
  static String? validateConfirmPassword(String? value, String password) {
    if (value == null || value.isEmpty) {
      return '请确认密码';
    }
    
    if (value != password) {
      return '两次输入的密码不一致';
    }
    
    return null;
  }
  
  /// 验证用户名
  static String? validateUsername(String? value) {
    if (value == null || value.isEmpty) {
      return '请输入用户名';
    }
    
    if (value.length < 3) {
      return '用户名长度不能少于3位';
    }
    
    return null;
  }
  
  /// 验证手机号
  static String? validatePhone(String? value) {
    if (value == null || value.isEmpty) {
      return '请输入手机号';
    }
    
    final phoneRegExp = RegExp(r'^1[3-9]\d{9}$');
    if (!phoneRegExp.hasMatch(value)) {
      return '请输入有效的手机号';
    }
    
    return null;
  }
  
  /// 验证验证码
  static String? validateVerificationCode(String? value) {
    if (value == null || value.isEmpty) {
      return '请输入验证码';
    }
    
    if (value.length != 6 || int.tryParse(value) == null) {
      return '请输入6位数字验证码';
    }
    
    return null;
  }
  
  /// 验证非空
  static String? validateRequired(String? value, String fieldName) {
    if (value == null || value.isEmpty) {
      return '请输入$fieldName';
    }
    
    return null;
  }
} 
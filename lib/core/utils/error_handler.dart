import 'package:dio/dio.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart' hide Response;
import '../services/storage_service.dart';
import '../../routes/app_routes.dart';
import 'logger.dart';

/// 统一错误处理器
class ErrorHandler {
  static final ErrorHandler _instance = ErrorHandler._internal();
  factory ErrorHandler() => _instance;
  ErrorHandler._internal();

  static ErrorHandler get instance => _instance;

  /// 处理网络错误
  static void handleNetworkError(dynamic error, {String? customMessage}) {
    AppLogger.error('网络错误处理', error);

    String message = customMessage ?? _getErrorMessage(error);
    
    if (error is DioException) {
      switch (error.type) {
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.sendTimeout:
        case DioExceptionType.receiveTimeout:
          message = '网络连接超时，请检查网络设置';
          break;
        case DioExceptionType.connectionError:
          message = '网络连接失败，请检查网络设置';
          break;
        case DioExceptionType.badResponse:
          if (error.response?.statusCode == 401) {
            _handleUnauthorized();
            return;
          } else if (error.response?.statusCode == 403) {
            message = '访问被拒绝，请检查权限';
          } else if (error.response?.statusCode == 404) {
            message = '请求的资源不存在';
          } else if (error.response?.statusCode == 500) {
            message = '服务器内部错误，请稍后重试';
          } else {
            message = '网络请求失败 (${error.response?.statusCode})';
          }
          break;
        case DioExceptionType.cancel:
          message = '请求已取消';
          break;
        case DioExceptionType.unknown:
          message = '网络连接异常，请检查网络设置';
          break;
        default:
          message = '网络请求失败，请稍后重试';
      }
    }

    _showErrorMessage(message);
  }

  /// 处理业务逻辑错误
  static void handleBusinessError(String message, {int? errorCode}) {
    AppLogger.error('业务错误: $message (code: $errorCode)');
    
    // 根据错误码进行特殊处理
    switch (errorCode) {
      case 401:
        _handleUnauthorized();
        break;
      case 403:
        _showErrorMessage('权限不足，无法执行此操作');
        break;
      case 1001: // 自定义业务错误码
        _showErrorMessage('用户信息已过期，请重新登录');
        _handleUnauthorized();
        break;
      default:
        _showErrorMessage(message);
    }
  }

  /// 处理蓝牙相关错误
  static void handleBluetoothError(dynamic error, {String? operation}) {
    AppLogger.error('蓝牙错误: $operation', error);
    
    String message = '蓝牙操作失败';
    if (operation != null) {
      message = '$operation失败';
    }
    
    if (error.toString().contains('permission')) {
      message = '蓝牙权限不足，请在设置中开启蓝牙权限';
    } else if (error.toString().contains('disabled')) {
      message = '蓝牙未开启，请开启蓝牙后重试';
    } else if (error.toString().contains('timeout')) {
      message = '蓝牙操作超时，请重试';
    }
    
    _showErrorMessage(message);
  }

  /// 处理文件操作错误
  static void handleFileError(dynamic error, {String? operation}) {
    AppLogger.error('文件错误: $operation', error);
    
    String message = '文件操作失败';
    if (operation != null) {
      message = '$operation失败';
    }
    
    if (error.toString().contains('permission')) {
      message = '文件权限不足，请检查应用权限';
    } else if (error.toString().contains('space')) {
      message = '存储空间不足，请清理后重试';
    } else if (error.toString().contains('not found')) {
      message = '文件不存在或已被删除';
    }
    
    _showErrorMessage(message);
  }

  /// 处理音频相关错误
  static void handleAudioError(dynamic error, {String? operation}) {
    AppLogger.error('音频错误: $operation', error);
    
    String message = '音频操作失败';
    if (operation != null) {
      message = '$operation失败';
    }
    
    if (error.toString().contains('permission')) {
      message = '录音权限不足，请在设置中开启录音权限';
    } else if (error.toString().contains('format')) {
      message = '音频格式不支持，请选择其他文件';
    } else if (error.toString().contains('device')) {
      message = '音频设备不可用，请检查设备连接';
    }
    
    _showErrorMessage(message);
  }

  /// 处理未授权错误
  static void _handleUnauthorized() {
    try {
      final storageService = Get.find<StorageService>();
      storageService.clearLoginData();
    } catch (e) {
      AppLogger.error('清理登录数据失败', e);
    }
    
    EasyLoading.showInfo('登录已过期，请重新登录');
    
    // 延迟跳转，确保提示显示
    Future.delayed(const Duration(milliseconds: 1500), () {
      if (Get.currentRoute != Routes.LOGIN) {
        Get.offAllNamed(Routes.LOGIN);
      }
    });
  }

  /// 显示错误消息
  static void _showErrorMessage(String message) {
    if (message.isNotEmpty) {
      EasyLoading.showError(message);
    }
  }

  /// 获取错误消息
  static String _getErrorMessage(dynamic error) {
    if (error is DioException) {
      return error.message ?? '网络请求失败';
    } else if (error is Exception) {
      return error.toString().replaceAll('Exception: ', '');
    } else {
      return error?.toString() ?? '未知错误';
    }
  }

  /// 是否为网络错误
  static bool isNetworkError(dynamic error) {
    if (error is DioException) {
      return error.type == DioExceptionType.connectionError ||
             error.type == DioExceptionType.connectionTimeout ||
             error.type == DioExceptionType.sendTimeout ||
             error.type == DioExceptionType.receiveTimeout;
    }
    return false;
  }

  /// 是否可以重试
  static bool canRetry(dynamic error) {
    if (error is DioException) {
      // 网络错误和服务器错误可以重试
      return error.type == DioExceptionType.connectionError ||
             error.type == DioExceptionType.connectionTimeout ||
             error.type == DioExceptionType.sendTimeout ||
             error.type == DioExceptionType.receiveTimeout ||
             (error.response?.statusCode != null && 
              error.response!.statusCode! >= 500);
    }
    return false;
  }
}

/// 错误类型枚举
enum ErrorType {
  network,
  business,
  bluetooth,
  file,
  audio,
  permission,
  unknown,
}

/// 自定义异常类
class AppException implements Exception {
  final String message;
  final ErrorType type;
  final int? errorCode;
  final dynamic originalError;

  AppException({
    required this.message,
    required this.type,
    this.errorCode,
    this.originalError,
  });

  @override
  String toString() {
    return 'AppException: $message (type: $type, code: $errorCode)';
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import '../../routes/app_routes.dart';

/// 路由导航辅助类
/// 提供页面导航、对话框、Toast等统一处理
class RouterHelper {
  /// 导航到新页面
  static Future<T?>? navigateTo<T>({
    required Widget screen,
    dynamic arguments,
    bool preventDuplicates = true,
    Transition? transition,
    Duration? duration,
  }) {
    return Get.to<T>(
      () => screen,
      arguments: arguments,
      preventDuplicates: preventDuplicates,
      transition: transition,
      duration: duration,
    );
  }

  /// 使用命名路由导航
  static Future<T?>? navigateToNamed<T>({
    required String routeName,
    dynamic arguments,
    Map<String, String>? parameters,
    bool preventDuplicates = true,
  }) {
    return Get.toNamed<T>(
      routeName,
      arguments: arguments,
      parameters: parameters,
      preventDuplicates: preventDuplicates,
    );
  }

  /// 返回上一页面
  static void goBack<T>([T? result]) {
    Get.back<T>(result: result);
  }

  /// 显示对话框
  static Future<T?> showDialog<T>({
    required String title,
    required String message,
    String confirmText = '确定',
    String? cancelText,
    Function()? onConfirm,
    Function()? onCancel,
  }) {
    return Get.dialog<T>(
      AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          if (cancelText != null)
            TextButton(
              onPressed: () {
                Get.back();
                if (onCancel != null) onCancel();
              },
              child: Text(cancelText),
            ),
          TextButton(
            onPressed: () {
              Get.back();
              if (onConfirm != null) onConfirm();
            },
            child: Text(confirmText),
          ),
        ],
      ),
    );
  }

  /// 显示底部Sheet
  static Future<T?> showBottomSheet<T>({
    required Widget content,
    bool isDismissible = true,
    Color? backgroundColor,
    double? elevation,
    ShapeBorder? shape,
  }) {
    return Get.bottomSheet<T>(
      content,
      isDismissible: isDismissible,
      backgroundColor: backgroundColor,
      elevation: elevation,
      shape: shape,
    );
  }

  /// 显示Toast提示
  static void showToast(String message, {bool isError = false}) {
    if (isError) {
      EasyLoading.showError(message);
    } else {
      EasyLoading.showToast(message);
    }
  }

  /// 显示成功提示
  static void showSuccess(String message) {
    EasyLoading.showSuccess(message);
  }

  /// 显示信息提示
  static void showInfo(String message) {
    EasyLoading.showInfo(message);
  }

  /// 导航到首页
  static void toHome() {
    Get.offAllNamed(Routes.HOME);
  }

  static void toAvatarTestGuide() {
    Get.toNamed(Routes.AVATAR_TEST_GUIDE);
  }

  /// 导航到头像邀请页面
  static void toAvatarInvite() {
    Get.toNamed(Routes.AVATAR_INVITE);
  }
}

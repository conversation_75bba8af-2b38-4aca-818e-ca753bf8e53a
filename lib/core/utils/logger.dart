import 'package:flutter/foundation.dart';
import 'package:intl/intl.dart';

/// 日志级别
enum LogLevel {
  debug,
  info,
  warning,
  error,
}

/// 应用日志工具类
class AppLogger {
  // 当前日志级别
  static LogLevel level = kDebugMode ? LogLevel.debug : LogLevel.info;

  // 是否显示时间戳
  static bool showTimestamp = true;

  // 是否启用日志
  static bool enabled = true;

  /// 格式化时间戳
  static String _getTimestamp() {
    final now = DateTime.now();
    final formatter = DateFormat('HH:mm:ss.SSS');
    return formatter.format(now);
  }

  /// 获取日志前缀
  static String _getPrefix(LogLevel logLevel) {
    final timestamp = showTimestamp ? '${_getTimestamp()} ' : '';
    final levelStr = logLevel.toString().split('.').last.toUpperCase();
    return '$timestamp[$levelStr]';
  }

  /// 记录调试级别日志
  static void debug(String message, [Object? error, StackTrace? stackTrace]) {
    if (!enabled || level.index > LogLevel.debug.index) return;
    debugPrint('${_getPrefix(LogLevel.debug)} $message');
    if (error != null) {
      debugPrint('${_getPrefix(LogLevel.debug)} Error details: $error');
      if (stackTrace != null) {
        debugPrint('${_getPrefix(LogLevel.debug)} $stackTrace');
      }
    }
  }

  /// 记录信息级别日志
  static void info(String message) {
    if (!enabled || level.index > LogLevel.info.index) return;
    debugPrint('${_getPrefix(LogLevel.info)} $message');
  }

  /// 记录警告级别日志
  static void warning(String message, [Object? error]) {
    if (!enabled || level.index > LogLevel.warning.index) return;
    debugPrint('${_getPrefix(LogLevel.warning)} $message');
    if (error != null) {
      debugPrint('${_getPrefix(LogLevel.warning)} Error details: $error');
    }
  }

  /// 记录错误级别日志
  static void error(String message, [Object? error, StackTrace? stackTrace]) {
    if (!enabled || level.index > LogLevel.error.index) return;
    debugPrint('${_getPrefix(LogLevel.error)} $message', wrapWidth: 1024);
    if (error != null) {
      debugPrint('${_getPrefix(LogLevel.error)} Error details: $error',
          wrapWidth: 1024);
      if (stackTrace != null) {
        debugPrint('${_getPrefix(LogLevel.error)} $stackTrace');
      }
    }
  }
}

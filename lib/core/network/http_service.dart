import 'package:dio/dio.dart' as dio;
import 'package:dio/io.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:pretty_dio_logger/pretty_dio_logger.dart';
import 'api_config.dart';
import 'interceptors/auth_interceptor.dart';
import 'interceptors/business_interceptor.dart';
import 'interceptors/retry_interceptor.dart';

/// HTTP服务 - 最终简化版本
class HttpService {
  static HttpService? _instance;
  final dio.Dio _dio;

  HttpService._internal() : _dio = dio.Dio() {
    _init();
  }

  static HttpService get instance {
    _instance ??= HttpService._internal();
    return _instance!;
  }

  void _init() {
    //// Proxyman的抓包
    // (_dio.httpClientAdapter as DefaultHttpClientAdapter).onHttpClientCreate =
    //     (client) {
    //   client.findProxy = (uri) {
    //     return "PROXY *************:9090;";
    //   };
    //   client.badCertificateCallback = (cert, host, port) => true; // 允许自签名证书
    // };

    _dio.options = dio.BaseOptions(
      baseUrl: ApiConfig.baseUrl,
      connectTimeout: const Duration(milliseconds: 15000),
      receiveTimeout: const Duration(milliseconds: 30000),
      contentType: 'application/json',
    );

    _dio.interceptors.add(AuthInterceptor());
    _dio.interceptors.add(RetryInterceptor());

    if (kDebugMode) {
      _dio.interceptors.add(PrettyDioLogger(
        requestHeader: true,
        requestBody: true,
        responseHeader: false,
        responseBody: true,
        compact: true,
        maxWidth: 90,
      ));
    }
    _dio.interceptors.add(BusinessInterceptor());
  }

  /// GET 请求
  Future<T?> get<T>({
    required String path,
    Map<String, dynamic>? queryParams,
    T Function(dynamic json)? fromJson,
  }) async {
    final response = await _dio.get(path, queryParameters: queryParams);
    return _parseResponse<T>(response.data, fromJson);
  }

  /// GET 请求 - 带 Loading
  Future<T?> getWithLoading<T>({
    required String path,
    Map<String, dynamic>? queryParams,
    T Function(dynamic json)? fromJson,
    String loadingText = '加载中...',
  }) async {
    EasyLoading.show(status: loadingText);
    final result = await get<T>(
      path: path,
      queryParams: queryParams,
      fromJson: fromJson,
    );
    EasyLoading.dismiss();
    return result;
  }

  /// POST 请求
  Future<T?> post<T>({
    required String path,
    dynamic data,
    Map<String, dynamic>? queryParams,
    T Function(dynamic json)? fromJson,
  }) async {
    final response = await _dio.post(
      path,
      data: data,
      queryParameters: queryParams,
    );
    return _parseResponse<T>(response.data, fromJson);
  }

  /// POST 请求 - 带 Loading
  Future<T?> postWithLoading<T>({
    required String path,
    dynamic data,
    Map<String, dynamic>? queryParams,
    T Function(dynamic json)? fromJson,
    String loadingText = '提交中...',
  }) async {
    EasyLoading.show(status: loadingText);
    try {
      final result = await post<T>(
        path: path,
        data: data,
        queryParams: queryParams,
        fromJson: fromJson,
      );
      return result;
    } finally {
      // ✅ 确保无论成功还是失败都会隐藏loading
      EasyLoading.dismiss();
    }
  }

  /// 文件上传
  Future<T?> upload<T>({
    required String path,
    required String filePath,
    String fileKey = 'file',
    Map<String, dynamic>? extraData,
    T Function(dynamic json)? fromJson,
    Function(int progress)? onProgress,
    String loadingText = '上传中...',
  }) async {
    EasyLoading.show(status: loadingText);

    final formData = dio.FormData.fromMap({
      fileKey: await dio.MultipartFile.fromFile(filePath),
      ...?extraData,
    });

    final response = await _dio.post(
      path,
      data: formData,
      onSendProgress: (sent, total) {
        final progress = (sent / total * 100).toInt();
        onProgress?.call(progress);
        // EasyLoading.showProgress(progress / 100,
        //     status: '$loadingText $progress%');
      },
    );

    EasyLoading.dismiss();
    return _parseResponse<T>(response.data, fromJson);
  }

  T? _parseResponse<T>(dynamic data, T Function(dynamic json)? fromJson) {
    if (data == null) return null;
    if (fromJson != null) return fromJson(data);
    return data as T?;
  }
}

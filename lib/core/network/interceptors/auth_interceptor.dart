import 'package:dio/dio.dart';
import 'package:get/get.dart';
import '../../services/storage_service.dart';

/// 认证拦截器
/// 为请求添加认证令牌
class AuthInterceptor extends Interceptor {
  final StorageService _storageService;

  AuthInterceptor() : _storageService = Get.find<StorageService>();

  @override
  void onRequest(
      RequestOptions options, RequestInterceptorHandler handler) async {
    // 从本地存储获取令牌
    final token = await _storageService.getToken();

    // 如果令牌存在，添加到请求头
    if (token != null && token.isNotEmpty) {
      options.headers['token'] = token;
    }

    // 继续处理请求
    handler.next(options);
  }
}

import 'package:dio/dio.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart' hide Response;

import '../../../routes/app_routes.dart';
import '../../services/storage_service.dart';
import '../../utils/error_handler.dart';

/// 业务拦截器 - 最终方案
class BusinessInterceptor extends Interceptor {
  final StorageService _storageService = Get.find<StorageService>();

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    // 1. 检查 HTTP 状态码
    if (response.statusCode != 200) {
      final error = DioException(
        requestOptions: response.requestOptions,
        response: response,
        type: DioExceptionType.badResponse,
      );
      ErrorHandler.handleNetworkError(error);
      handler.reject(error);
      return;
    }

    // 2. 检查响应数据格式
    if (response.data is! Map<String, dynamic>) {
      handler.next(response);
      return;
    }

    final data = response.data as Map<String, dynamic>;
    final code = data['code'] ?? -1;
    final msg = data['msg'] ?? data['message'] ?? '未知错误';
    final businessData = data['data'];

    // 3. 处理业务响应
    if (code == 0) {
      response.data = businessData;
      handler.next(response);
    } else {
      // 使用统一错误处理
      ErrorHandler.handleBusinessError(msg, errorCode: code);

      handler.reject(DioException(
        requestOptions: response.requestOptions,
        response: response,
        message: msg
      ));
    }
  }

  void _handleTokenExpired() {
    _storageService.clearLoginData();
    EasyLoading.showInfo('登录已过期，请重新登录');
    Future.delayed(const Duration(milliseconds: 1500), () {
      if (Get.currentRoute != Routes.LOGIN) {
        Get.offAllNamed(Routes.LOGIN);
      }
    });
  }
}

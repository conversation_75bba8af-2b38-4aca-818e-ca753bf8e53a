import 'package:dio/dio.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart' hide Response;

import '../../../routes/app_routes.dart';
import '../../services/storage_service.dart';

/// 业务拦截器 - 最终方案
class BusinessInterceptor extends Interceptor {
  final StorageService _storageService = Get.find<StorageService>();

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    // 1. 检查 HTTP 状态码
    if (response.statusCode != 200) {
      EasyLoading.showError('网络请求失败');
      handler.reject(DioException(
        requestOptions: response.requestOptions,
        response: response,
        type: DioExceptionType.badResponse,
      ));
      return;
    }

    // 2. 检查响应数据格式
    if (response.data is! Map<String, dynamic>) {
      handler.next(response);
      return;
    }

    final data = response.data as Map<String, dynamic>;
    final code = data['code'] ?? -1;
    final msg = data['msg'] ?? data['message'] ?? '未知错误';
    final businessData = data['data'];

    // 3. 最终方案：成功返回业务数据，失败显示toast
    if (code == 0) {
      response.data = businessData;
      handler.next(response);
    } else {
      if (code == 401) {
        _handleTokenExpired();
      } else {
        // 确保错误提示能正确显示
        EasyLoading.showError(msg);
      }

      handler.reject(DioException(
        requestOptions: response.requestOptions,
        response: response,
        message: msg
      ));
    }
  }

  void _handleTokenExpired() {
    _storageService.clearLoginData();
    EasyLoading.showInfo('登录已过期，请重新登录');
    Future.delayed(const Duration(milliseconds: 1500), () {
      if (Get.currentRoute != Routes.LOGIN) {
        Get.offAllNamed(Routes.LOGIN);
      }
    });
  }
}

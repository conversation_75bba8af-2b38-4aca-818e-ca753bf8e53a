import 'dart:async';
import 'package:dio/dio.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import '../../utils/logger.dart';
import '../api_config.dart';

/// 重试拦截器
/// 处理请求失败时的重试逻辑
class RetryInterceptor extends Interceptor {
  final Dio _dio;

  RetryInterceptor() : _dio = Dio();

  @override
  Future<void> onError(
      DioException err, ErrorInterceptorHandler handler) async {
    // 获取请求选项中的重试配置
    final options = err.requestOptions;
    final maxRetries = options.extra['maxRetries'] as int? ?? 1;
    final retryDelay = options.extra['retryDelay'] as int? ?? 500;
    final currentRetry = options.extra['currentRetry'] as int? ?? 0;
    final bool showLoading = options.extra['showLoading'] as bool? ?? false;

    // 如果已达到最大重试次数，则不再重试
    if (currentRetry >= maxRetries) {
      return handler.next(err);
    }

    // 判断是否需要重试的错误类型
    final shouldRetry = _shouldRetry(err);
    if (!shouldRetry) {
      return handler.next(err);
    }

    // 等待指定的延迟时间
    await Future.delayed(Duration(milliseconds: retryDelay));

    // 准备重试
    final newOptions = Options(
      method: options.method,
      headers: options.headers,
      extra: {
        ...options.extra,
        'currentRetry': currentRetry + 1,
        'showLoading': showLoading,
      },
    );

    try {
      // 重新发起请求
      final response = await _dio.request(
        options.path,
        data: options.data,
        queryParameters: options.queryParameters,
        options: newOptions,
      );

      // 如果请求成功，返回响应
      handler.resolve(response);
    } catch (e) {
      // 如果重试仍然失败，交给下一个错误处理器
      if (e is DioException) {
        handler.next(e);
      } else {
        handler.next(
          DioException(requestOptions: options, error: e),
        );
      }
    }
  }

  /// 判断是否应该重试
  bool _shouldRetry(DioException error) {
    // 仅在网络错误或超时错误时重试
    return error.type == DioExceptionType.connectionTimeout ||
        error.type == DioExceptionType.receiveTimeout ||
        error.type == DioExceptionType.sendTimeout ||
        error.type == DioExceptionType.connectionError;
  }
}

import 'dart:convert';
import 'dart:async';

import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:get/get.dart';
import '../../utils/logger.dart';

import 'socket_resp.dart';

class WebSocketInstance {
  WebSocketInstance._();

  static WebSocketInstance instance = WebSocketInstance._();

  WebSocketChannel? socketChannel;

  // WiFi连接状态 - 临时mock，实际应从WebSocket消息获取
  final RxBool _isWifiConnected = true.obs; // Mock为true
  bool get isWifiConnected => _isWifiConnected.value;
  RxBool get wifiConnectedRx => _isWifiConnected;
  
  // 消息回调函数
  Function(SocketResp)? _onMessageReceived;

  /// 设置消息接收回调
  void setMessageListener(Function(SocketResp) callback) {
    _onMessageReceived = callback;
  }

  void connectSocket() {
    socketChannel =
        WebSocketChannel.connect(Uri.parse("ws://8.130.69.251:8060/ws/xiaozhi/v1"));
    var stream = socketChannel?.stream;
    stream?.listen((event) {
      print("WebSocketInstance event=$event");
      try {
        //心跳
        var socketResp = SocketResp.fromJson(jsonDecode(event));
        if (socketResp.code?.toInt() == 333) {
          sendMessage(event);
        }else if(socketResp.code?.toInt() == 666){
          // 处理WiFi状态消息
          _handleWifiStatusMessage(socketResp);
        }else if(socketResp.code?.toInt() == 200){
          // 处理聊天回复消息
          _handleChatMessage(socketResp);
        }
      } catch (e) {
        print("WebSocketInstance e=$e");
      }
    });

    // 启动WiFi状态mock
    _startWifiStatusMock();
  }

  /// 处理WiFi状态消息
  void _handleWifiStatusMessage(SocketResp socketResp) {
    try {
      // 这里应该根据实际的WebSocket消息格式来解析WiFi状态
      // 目前先保持mock状态
      AppLogger.info('Received WiFi status message: ${socketResp.toString()}');
    } catch (e) {
      AppLogger.error('Failed to handle WiFi status message: $e');
    }
  }

  /// 处理聊天消息
  void _handleChatMessage(SocketResp socketResp) {
    try {
      // 将消息转发给注册的回调函数
      if (_onMessageReceived != null) {
        _onMessageReceived!(socketResp);
      }
      AppLogger.info('Received chat message: ${socketResp.toString()}');
    } catch (e) {
      AppLogger.error('Failed to handle chat message: $e');
    }
  }

  /// 临时mock WiFi状态 - 实际应该从WebSocket消息中获取
  void _startWifiStatusMock() {
    // 模拟WiFi状态，实际使用时应该删除这个方法
    Timer.periodic(Duration(seconds: 30), (timer) {
      // 保持WiFi连接状态为true
      _isWifiConnected.value = true;
      AppLogger.info('Mock WiFi status: ${_isWifiConnected.value}');
    });
  }

  void sendMessage(dynamic data) {
    socketChannel?.sink.add(data);
  }

  void close() {
    socketChannel?.sink.close();
  }
}

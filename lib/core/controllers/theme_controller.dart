import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../services/storage_service.dart';

class ThemeController extends GetxController {
  final StorageService _storageService = Get.find<StorageService>();
  static const String _themeKey = 'theme_mode';

  // 主题模式
  final Rx<ThemeMode> _themeMode = ThemeMode.system.obs;

  // 获取当前主题模式
  ThemeMode get themeMode => _themeMode.value;

  // 当前是否是暗色模式
  bool get isDarkMode {
    if (_themeMode.value == ThemeMode.system) {
      return WidgetsBinding.instance.platformDispatcher.platformBrightness ==
          Brightness.dark;
    }
    return _themeMode.value == ThemeMode.dark;
  }

  @override
  void onInit() {
    super.onInit();
    _loadThemeMode();
  }

  // 加载主题设置
  void _loadThemeMode() {
    String? themeModeStr = _storageService.getThemeMode();
    if (themeModeStr != null) {
      switch (themeModeStr) {
        case 'light':
          _themeMode.value = ThemeMode.light;
          break;
        case 'dark':
          _themeMode.value = ThemeMode.dark;
          break;
        default:
          _themeMode.value = ThemeMode.system;
      }
    }
  }

  // 切换主题模式
  void changeThemeMode(ThemeMode mode) {
    _themeMode.value = mode;

    String themeModeStr;
    switch (mode) {
      case ThemeMode.light:
        themeModeStr = 'light';
        break;
      case ThemeMode.dark:
        themeModeStr = 'dark';
        break;
      default:
        themeModeStr = 'system';
    }

    _storageService.saveThemeMode(themeModeStr);
    update();
  }

  // 切换明暗模式
  void toggleThemeMode() {
    if (_themeMode.value == ThemeMode.light) {
      changeThemeMode(ThemeMode.dark);
    } else {
      changeThemeMode(ThemeMode.light);
    }
  }
}

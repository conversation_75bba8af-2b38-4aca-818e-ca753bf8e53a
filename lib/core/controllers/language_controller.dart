import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../services/storage_service.dart';

class LanguageController extends GetxController {
  final StorageService _storageService = Get.find<StorageService>();
  static const String _languageKey = 'language';

  // 支持的语言
  final Map<String, Locale> supportedLanguages = {
    '简体中文': const Locale('zh', 'CN'),
    'English': const Locale('en', 'US'),
  };

  // 当前语言
  final Rx<Locale> _currentLocale = const Locale('zh', 'CN').obs;

  Locale get currentLocale => _currentLocale.value;

  // 当前语言名称
  String get currentLanguageName {
    return supportedLanguages.entries
        .firstWhere(
          (element) =>
              element.value.languageCode == _currentLocale.value.languageCode,
          orElse: () => const MapEntry('简体中文', Locale('zh', 'CN')),
        )
        .key;
  }

  @override
  void onInit() {
    super.onInit();
    _loadLanguage();
  }

  // 加载语言设置
  Future<void> _loadLanguage() async {
    String? languageCode = _storageService.getLanguage();

    if (languageCode != null) {
      final parts = languageCode.split('_');
      if (parts.length >= 2) {
        _currentLocale.value = Locale(parts[0], parts[1]);
      } else if (parts.isNotEmpty) {
        _currentLocale.value = Locale(parts[0]);
      }
    }

    // 更新应用语言
    _updateAppLanguage();
  }

  // 公共方法，供外部调用加载语言
  Future<void> loadLanguage() async {
    await _loadLanguage();
  }

  // 更改语言
  Future<void> changeLanguage(String languageName) async {
    if (supportedLanguages.containsKey(languageName)) {
      final locale = supportedLanguages[languageName]!;
      _currentLocale.value = locale;

      // 保存语言设置
      await _storageService
          .saveLanguage('${locale.languageCode}_${locale.countryCode}');

      // 更新应用语言
      _updateAppLanguage();
    }
  }

  // 更新应用语言
  void _updateAppLanguage() {
    Get.updateLocale(_currentLocale.value);
    update();
  }
}

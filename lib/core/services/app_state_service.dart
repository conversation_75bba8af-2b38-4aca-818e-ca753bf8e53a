import 'package:get/get.dart';
import '../../data/models/user_model.dart';
import '../../data/models/current_info_model.dart';
import '../../core/utils/logger.dart';
import '../../core/services/storage_service.dart';
import '../../data/repositories/auth_repository.dart';

class AppStateService extends GetxService {
  static AppStateService get to => Get.find<AppStateService>();

  final StorageService _storageService = Get.find<StorageService>();
  final AuthRepository _authRepository = Get.find<AuthRepository>();

  // 用户信息（登录后保存）
  final Rx<UserModel?> user = Rx<UserModel?>(null);

  // 当前用户进度信息（getCurrentInfo接口返回）
  final Rx<CurrentInfoModel?> currentInfo = Rx<CurrentInfoModel?>(null);

  // 登录状态
  final RxBool isLoggedIn = false.obs;

  final RxInt currentAgentId = 0.obs;
  String dollId = '';

  @override
  Future<void> onInit() async {
    super.onInit();
    // 启动时检查本地登录状态
    _checkLocalLoginStatus();
  }

  /// 检查本地登录状态 - 简化版本
  void _checkLocalLoginStatus() {
    try {
      if (_storageService.isLoggedIn()) {
        final savedUser = _storageService.getUser();
        if (savedUser != null) {
          user.value = savedUser;
          isLoggedIn.value = true;
          AppLogger.info('从本地恢复用户状态: ${savedUser.userName}');
        }
      }
    } catch (e) {
      AppLogger.error('检查本地登录状态失败', e);
    }
  }

  /// 用户登录成功后的处理 - 简化版本
  Future<void> onUserLoginSuccess(UserModel userModel) async {
    try {
      // 保存用户信息和登录状态
      user.value = userModel;
      isLoggedIn.value = true;

      // 保存到本地存储
      await _storageService.saveUser(userModel);
      if (userModel.token?.isNotEmpty == true) {
        await _storageService.saveToken(userModel.token!);
      }

      AppLogger.info('用户登录成功: ${userModel.userName}');
    } catch (e) {
      AppLogger.error('登录成功后处理失败', e);
    }
  }

  /// 获取用户当前进度信息 - 统一调用入口
  Future<void> fetchCurrentUserInfo() async {
    if (user.value?.id != null) {
      await getCurrentInfo(user.value!.id!);
    }
  }

  /// 获取用户当前进度信息 - 通过 AuthRepository
  Future<void> getCurrentInfo(int userId) async {
    final result = await _authRepository.getCurrentInfo(userId);

    if (result != null) {
      currentInfo.value = result;
      currentAgentId.value = result.agentId ?? 0;

      // 修复dollId提取逻辑：从数组中取第一个元素
      if (result.dollUserId != null && result.dollUserId!.isNotEmpty) {
        dollId = result.dollUserId!.first.toString();
        AppLogger.info('提取dollId成功: $dollId');
      } else {
        dollId = '';
        AppLogger.warning('dollUserId为空或不存在');
      }
    } else {
      currentInfo.value = null;
      AppLogger.warning('getCurrentInfo失败');
    }
  }

  /// 保存dollId到全局状态
  void saveDollId(String dollId) {
    this.dollId = dollId;
    AppLogger.info('保存dollId到全局状态: $dollId');
  }

  /// ✅ 确保用户信息已加载（如果没有则自动获取）
  Future<void> ensureCurrentInfoLoaded() async {
    if (currentInfo.value == null && user.value?.id != null) {
      AppLogger.info('用户信息未加载，自动获取中...');
      await getCurrentInfo(user.value!.id!);
    }
  }

  /// 核心业务状态判断方法 - 基于新的 getCurrentInfo 接口

  /// 是否需要配网设备（deviceId为空）
  bool get needsDeviceConfiguration {
    return currentInfo.value?.needsDeviceConfiguration ?? true;
  }

  /// 是否需要创建分身（agentId为0或空）
  bool get needsAvatarCreation {
    return currentInfo.value?.needsAvatarCreation ?? true;
  }

  /// 是否需要上传聊天文件（fileIds为空）
  bool get needsChatFileUpload {
    return currentInfo.value?.needsChatFileUpload ?? true;
  }

  /// 是否需要设置音色（cloneVoiceId为空）
  bool get needsVoiceConfiguration {
    return currentInfo.value?.needsVoiceConfiguration ?? true;
  }

  /// 是否需要设置亲密陪伴者（dollUserId为空）
  bool get needsIntimateCompanionSetup {
    return currentInfo.value?.needsIntimateCompanionSetup ?? true;
  }

  /// 是否需要录制声纹（voicePrintIds为空）
  bool get needsVoicePrintRecording {
    return currentInfo.value?.needsVoicePrintRecording ?? true;
  }

  /// 是否可以进入首页（所有步骤都完成）
  bool get canEnterHome {
    return currentInfo.value?.canEnterHome ?? false;
  }

  /// 获取当前用户ID
  int? get currentUserId => user.value?.id;

  /// 用户登出 - 简化版本
  Future<void> logout() async {
    try {
      // 清除本地存储
      await _storageService.clearLoginData();

      // 重置状态
      user.value = null;
      currentInfo.value = null;
      isLoggedIn.value = false;

      AppLogger.info('用户已登出');
    } catch (e) {
      AppLogger.error('登出失败', e);
    }
  }
}

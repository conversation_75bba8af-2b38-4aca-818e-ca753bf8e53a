import 'dart:convert';

import 'package:shared_preferences/shared_preferences.dart';

import '../../data/models/user_model.dart';
import '../utils/logger.dart';

/// 只使用 SharedPreferences 进行所有数据存储
class StorageService {
  // SharedPreferences实例
  final SharedPreferences _prefs;

  // 密钥常量
  static const String _tokenKey = 'auth_token';
  static const String _userKey = 'user_data';
  static const String _deviceIdKey = 'device_id';
  static const String _themeKey = 'app_theme';
  static const String _languageKey = 'app_language';
  static const String _rememberMeKey = 'remember_me';

  // 构造函数
  StorageService({
    required SharedPreferences prefs,
  }) : _prefs = prefs;

  /// 保存登录令牌
  Future<void> saveToken(String token) async {
    try {
      await _prefs.setString(_tokenKey, token);
      AppLogger.info('令牌保存成功');
    } catch (e) {
      AppLogger.error('保存令牌失败', e);
    }
  }

  /// 获取登录令牌
  String? getToken() {
    try {
      return _prefs.getString(_tokenKey);
    } catch (e) {
      AppLogger.error('获取令牌失败', e);
      return null;
    }
  }

  /// 检查是否已登录（有token且用户信息存在）
  bool isLoggedIn() {
    final token = getToken();
    final user = getUser();
    return token != null && token.isNotEmpty && user != null;
  }

  /// 保存用户数据
  Future<bool> saveUser(UserModel user) async {
    try {
      final userJson = jsonEncode(user.toJson());
      final result = await _prefs.setString(_userKey, userJson);
      if (result) {
        AppLogger.info('用户数据保存成功: ${user.userName}');
      }
      return result;
    } catch (e) {
      AppLogger.error('保存用户数据失败', e);
      return false;
    }
  }

  /// 获取用户数据
  UserModel? getUser() {
    try {
      final userJson = _prefs.getString(_userKey);
      if (userJson != null && userJson.isNotEmpty) {
        final Map<String, dynamic> userMap = jsonDecode(userJson);
        return UserModel.fromJson(userMap);
      }
      return null;
    } catch (e) {
      AppLogger.error('获取用户数据失败', e);
      return null;
    }
  }

  /// 保存设备ID
  Future<bool> saveDeviceId(String deviceId) async {
    return await _prefs.setString(_deviceIdKey, deviceId);
  }

  /// 获取设备ID
  String? getDeviceId() {
    return _prefs.getString(_deviceIdKey);
  }

  /// 保存主题模式 (light/dark)
  Future<bool> saveThemeMode(String mode) async {
    return await _prefs.setString(_themeKey, mode);
  }

  /// 获取主题模式
  String? getThemeMode() {
    return _prefs.getString(_themeKey);
  }

  /// 保存语言设置
  Future<bool> saveLanguage(String languageCode) async {
    return await _prefs.setString(_languageKey, languageCode);
  }

  /// 获取语言设置
  String? getLanguage() {
    return _prefs.getString(_languageKey);
  }

  /// 保存记住登录状态
  Future<bool> saveRememberMe(bool remember) async {
    try {
      return await _prefs.setBool(_rememberMeKey, remember);
    } catch (e) {
      AppLogger.error('保存记住登录状态失败', e);
      return false;
    }
  }

  /// 获取记住登录状态
  bool getRememberMe() {
    return _prefs.getBool(_rememberMeKey) ?? false;
  }

  /// 清除所有数据
  Future<void> clearAll() async {
    try {
      await _prefs.clear();
      AppLogger.info('所有存储数据已清除');
    } catch (e) {
      AppLogger.error('清除存储失败', e);
    }
  }

  /// 清除登录相关数据（登出时使用）
  Future<void> clearLoginData() async {
    try {
      await _prefs.remove(_tokenKey);
      await _prefs.remove(_userKey);
      AppLogger.info('登录数据已清除');
    } catch (e) {
      AppLogger.error('清除登录数据失败', e);
    }
  }

  /// 移除令牌（登出时使用）- 兼容旧方法
  Future<void> removeToken() async {
    await clearLoginData();
  }

  /// 清除令牌（登出时使用）- 兼容旧方法
  Future<void> clearToken() async {
    await clearLoginData();
  }

  // 通用存储方法

  /// 保存字符串
  Future<bool> setString(String key, String value) async {
    return await _prefs.setString(key, value);
  }

  /// 获取字符串
  String? getString(String key) {
    return _prefs.getString(key);
  }

  /// 保存布尔值
  Future<bool> setBool(String key, bool value) async {
    return await _prefs.setBool(key, value);
  }

  /// 获取布尔值
  bool? getBool(String key) {
    return _prefs.getBool(key);
  }

  /// 保存双精度浮点数
  Future<bool> setDouble(String key, double value) async {
    return await _prefs.setDouble(key, value);
  }

  /// 获取双精度浮点数
  double? getDouble(String key) {
    return _prefs.getDouble(key);
  }

  /// 保存整数
  Future<bool> setInt(String key, int value) async {
    return await _prefs.setInt(key, value);
  }

  /// 获取整数
  int? getInt(String key) {
    return _prefs.getInt(key);
  }

  /// 保存字符串列表
  Future<bool> setStringList(String key, List<String> value) async {
    return await _prefs.setStringList(key, value);
  }

  /// 获取字符串列表
  List<String>? getStringList(String key) {
    return _prefs.getStringList(key);
  }

  /// 检查键是否存在
  bool containsKey(String key) {
    return _prefs.containsKey(key);
  }

  /// 删除指定键的数据
  Future<bool> remove(String key) async {
    return await _prefs.remove(key);
  }
}

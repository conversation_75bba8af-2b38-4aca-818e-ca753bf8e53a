import 'dart:async';
import 'dart:convert';
import 'package:esp_blufi/esp_blufi.dart';
import 'package:get/get.dart';
import 'package:wifi_scan/wifi_scan.dart';
import '../config/app_constants.dart';
import '../utils/logger.dart';
import '../../data/models/blufi_device.dart';
import '../../data/models/wifi_network.dart';
import '../../data/models/device_status.dart';



/// BluFi配网服务
class BluFiService extends GetxService {
  static BluFiService get to => Get.find();

  final EspBlufi _espBlufi = EspBlufi();

  // 状态管理
  final RxBool isScanning = false.obs;
  final RxBool isConnected = false.obs;
  final RxList<BluFiDevice> discoveredDevices = <BluFiDevice>[].obs;
  final RxList<WifiNetwork> wifiNetworks = <WifiNetwork>[].obs;
  final Rx<DeviceStatus?> deviceStatus = Rx<DeviceStatus?>(null);

  // 当前连接的设备
  BluFiDevice? currentDevice;

  // ✅ 添加配网状态 - 使用响应式状态替代回调
  final RxBool provisionSuccess = false.obs;
  final RxString provisionMessage = ''.obs;

  @override
  void onInit() {
    super.onInit();
    _setupMessageListener();
  }

  /// 设置消息监听
  void _setupMessageListener() {
    _espBlufi.onMessageReceived(
      successCallback: (data) {
        AppLogger.info('BluFi消息接收: $data');
        if (data != null) {
          try {
            final message = jsonDecode(data);
            _handleMessage(message);
          } catch (e) {
            AppLogger.error('解析BluFi消息失败', e);
          }
        }
      },
      errorCallback: (error) {
        AppLogger.error('BluFi消息接收错误: $error');
      },
    );
  }

  /// 处理接收到的消息
  void _handleMessage(Map<String, dynamic> message) {
    final key = message['key'];
    final value = message['value'];

    switch (key) {
      case 'ble_scan_result':
        _handleDeviceScanResult(value);
        break;
      case 'wifi_scan_result':
        _handleWifiScanResult(value);
        break;
      case 'device_status':
        _handleDeviceStatus(value);
        break;
      case 'provision_result':
        _handleProvisionResult(value);
        break;
      case 'configure_params':
        _handleConfigureResult(value);
        break;
      case 'wifi_connection_state':
      case 'device_wifi_connect':
        _handleWifiConnectionState(value);
        break;
      case 'onCharacteristicWrite':
        AppLogger.info('🔵 特征值写入完成');
        break;
      case 'ON_CHARACTERISTICWRITE_GATT_DISCONNECT':
        AppLogger.info('🔵 GATT连接状态: $value');
        break;
      default:
        AppLogger.info('未处理的消息类型: $key');
    }
  }

  /// 处理设备扫描结果
  void _handleDeviceScanResult(dynamic data) {
    print('🔵 处理设备扫描结果: $data');

    if (data is Map<String, dynamic>) {
      // 单个设备数据格式:  {address: 50:78:7D:15:B0:AE, name: TWO_AI_50787d15b0ae, rssi: -23}
      final deviceName = data['name'] ?? '';

      // 只添加以 TWO_AI_ 开头的设备
      if (deviceName
          .toUpperCase()
          .startsWith(AppConstants.device_name_prefix)) {
        final device = BluFiDevice(
          name: deviceName,
          address: data['address'] ?? '',
          rssi: int.tryParse(data['rssi']?.toString() ?? '0') ?? -100,
        );

        // 检查是否已存在，避免重复添加
        if (!discoveredDevices.any((d) => d.address == device.address)) {
          discoveredDevices.add(device);
          print(
              '🔵 已添加设备: ${device.name} (${device.address}) RSSI: ${device.rssi}');
        }
      } else {
        print('🟡 过滤掉非totwoo设备: $deviceName');
      }
    } else if (data is List) {
      // 多个设备数据
      final devices = data
          .map((item) => BluFiDevice.fromJson(item))
          .where((device) => device.name
              .toUpperCase()
              .startsWith(AppConstants.device_name_prefix))
          .toList();
      discoveredDevices.assignAll(devices);
    }
  }

  /// 处理WiFi扫描结果
  void _handleWifiScanResult(dynamic data) {
    if (data is List) {
      final networks = data.map((item) => WifiNetwork.fromJson(item)).toList();
      wifiNetworks.assignAll(networks);
    }
  }

  /// 处理设备状态
  void _handleDeviceStatus(dynamic data) {
    if (data is Map<String, dynamic>) {
      deviceStatus.value = DeviceStatus.fromJson(data);
    }
  }

  /// 处理配网结果
  void _handleProvisionResult(dynamic data) {
    AppLogger.info('配网结果: $data');
    // 配网结果会通过消息流传递给UI层处理
  }

  /// 处理配网参数结果
  void _handleConfigureResult(dynamic data) {
    // 根据ESP官方文档，configure_params返回值：
    // 1 = 配网参数发送成功，但需要等待WiFi连接状态报告确认
    // 0 = 配网参数发送失败
    final paramsSent = data == '1' || data == 1;
    AppLogger.info('配网参数发送结果: ${paramsSent ? '成功' : '失败'}');

    if (!paramsSent) {
      provisionSuccess.value = false;
      provisionMessage.value = '配网失败，重启设备重试';
    }
  }

  /// 处理WiFi连接状态报告
  void _handleWifiConnectionState(dynamic data) {
    // 根据ESP BluFi文档，WiFi连接状态报告格式：
    // data[0]: opmode (0x00=NULL, 0x01=STA, 0x02=SoftAP, 0x03=SoftAP&STA)
    // data[1]: STA连接状态 (0x0=已连接有IP, 0x1=断开, 0x2=连接中, 0x3=已连接无IP)

    final success = data == '0' || data == 0; // 0表示已连接并获得IP地址
    if (success) {
      AppLogger.info('🟢 STA连接状态: 配网成功');
      // ✅ 使用响应式状态通知配网结果
      provisionSuccess.value = true;
      provisionMessage.value = '配网成功，设备已连接到WiFi';
    } else {
      AppLogger.info('🟡 配网失败');
      provisionSuccess.value = false;
      provisionMessage.value = '配网失败';
    }
  }

  /// 扫描BluFi设备
  Future<List<BluFiDevice>> scanDevices({
    String filterPrefix = '',
    Duration timeout =  AppConstants.scan_timeout,
  }) async {
    Timer? scanTimer;

    try {
      isScanning.value = true;
      discoveredDevices.clear();

      AppLogger.info('开始扫描BluFi设备，过滤前缀: $filterPrefix');

      // 开始扫描
      await _espBlufi.scanDeviceInfo(filterString: filterPrefix);

      // 设置超时定时器
      scanTimer = Timer(timeout, () async {
        isScanning.value = false;
        try {
          await _espBlufi.stopScan();
        } catch (e) {
          AppLogger.info('🟡 超时停止扫描时出现错误（可忽略）: $e');
        }
      });

      // 等待扫描结果
      await Future.delayed(timeout);

      // 取消定时器
      scanTimer?.cancel();

      // 停止扫描
      if (isScanning.value) {
        try {
          await _espBlufi.stopScan();
          print('🔍 停止扫描命令已发送');
        } catch (e) {
          print('🟡 停止扫描时出现错误（可忽略）: $e');
        }
      }

      AppLogger.info('扫描完成，发现设备数量: ${discoveredDevices.length}');

      // 打印所有发现的设备
      for (int i = 0; i < discoveredDevices.length; i++) {
        final device = discoveredDevices[i];
        print(
            '🔍 设备 ${i + 1}: ${device.name} (${device.address}) RSSI: ${device.rssi}');
      }

      return discoveredDevices.toList();
    } catch (e) {
      AppLogger.error('扫描设备失败', e);
      rethrow;
    } finally {
      scanTimer?.cancel();
      isScanning.value = false;
      print('🔍 扫描状态已重置');
    }
  }

  /// 停止扫描设备
  Future<void> stopScan() async {
    try {
      if (!isScanning.value) {
        print('🔍 当前未在扫描，无需停止');
        return;
      }

      print('🔍 手动停止扫描');
      isScanning.value = false;
      await _espBlufi.stopScan();
    } catch (e) {
      print('🟡 停止扫描时出现错误（可忽略）: $e');
    }
  }

  /// 连接到BluFi设备
  Future<bool> connectToDevice(BluFiDevice device) async {
    try {
      isConnected.value = false;

      AppLogger.info('连接到设备: ${device.name} (${device.address})');

      await _espBlufi.connectPeripheral(peripheralAddress: device.address);

      // 等待连接确认
      await Future.delayed(const Duration(seconds: 3));

      currentDevice = device;
      isConnected.value = true;

      AppLogger.info('设备连接成功');
      return true;
    } catch (e) {
      AppLogger.error('连接设备失败', e);
      isConnected.value = false;
      currentDevice = null;
      return false;
    }
  }

  /// 断开设备连接
  Future<void> disconnect() async {
    try {
      await _espBlufi.requestCloseConnection();
      currentDevice = null;
      isConnected.value = false;
      deviceStatus.value = null;
      wifiNetworks.clear();
      AppLogger.info('设备连接已断开');
    } catch (e) {
      AppLogger.error('断开连接失败', e);
    }
  }

  /// 使用系统WiFi管理器扫描WiFi网络（ESP官方推荐方式）
  Future<List<WifiNetwork>> scanWifiNetworks() async {
    try {
      AppLogger.info('开始扫描WiFi网络');
      wifiNetworks.clear();

      // 使用系统WiFi扫描（这是ESP官方推荐的方式）
      final networks = await _scanSystemWifiNetworks();

      if (networks.isNotEmpty) {
        wifiNetworks.assignAll(networks);
        print('🟢 WiFi扫描完成，发现网络数量: ${networks.length}');

        // WiFi扫描结果直接通过返回值提供，不需要消息流
      } else {
        print('🟡 未发现WiFi网络');
      }

      AppLogger.info('WiFi扫描完成，发现网络数量: ${networks.length}');
      return networks;
    } catch (e) {
      AppLogger.error('扫描WiFi网络失败', e);
      rethrow;
    }
  }

  /// 使用系统WiFi管理器扫描网络
  Future<List<WifiNetwork>> _scanSystemWifiNetworks() async {
    try {
      print('🔍 调用系统WiFi扫描...');

      // 检查WiFi扫描权限
      final canScanResult = await WiFiScan.instance.canGetScannedResults();
      if (canScanResult != CanGetScannedResults.yes) {
        print('🟡 没有WiFi扫描权限: $canScanResult，返回模拟数据');
        return _getMockWifiNetworks();
      }

      // 开始WiFi扫描
      await WiFiScan.instance.startScan();
      print('🔍 WiFi扫描已启动，等待结果...');

      // 等待扫描完成
      await Future.delayed(const Duration(seconds: 3));

      // 获取扫描结果
      final results = await WiFiScan.instance.getScannedResults();
      print('🔍 获取到${results.length}个WiFi网络');

      // 转换为WifiNetwork格式
      final networks = results
          .map((result) => WifiNetwork(
                ssid: result.ssid,
                rssi: result.level,
                isSecured: result.capabilities.contains('WPA') ||
                    result.capabilities.contains('WEP'),
              ))
          .toList();

      // 过滤掉空SSID和重复的网络
      final filteredNetworks = <WifiNetwork>[];
      final seenSSIDs = <String>{};

      for (final network in networks) {
        if (network.ssid.isNotEmpty && !seenSSIDs.contains(network.ssid)) {
          filteredNetworks.add(network);
          seenSSIDs.add(network.ssid);
        }
      }

      print('🔍 系统WiFi扫描完成，发现${filteredNetworks.length}个有效网络');
      return filteredNetworks;
    } catch (e) {
      print('🔴 系统WiFi扫描失败: $e，返回模拟数据');
      return _getMockWifiNetworks();
    }
  }

  /// 获取模拟WiFi网络数据
  List<WifiNetwork> _getMockWifiNetworks() {
    return [
      WifiNetwork(ssid: 'TP-LINK_2.4G', rssi: -45, isSecured: true),
      WifiNetwork(ssid: 'ChinaNet-WiFi', rssi: -52, isSecured: true),
      WifiNetwork(ssid: 'CMCC-Guest', rssi: -68, isSecured: false),
      WifiNetwork(ssid: 'Xiaomi_Router', rssi: -58, isSecured: true),
      WifiNetwork(ssid: 'Office_WiFi', rssi: -62, isSecured: true),
      WifiNetwork(ssid: 'Home_5G', rssi: -55, isSecured: true),
    ];
  }

  /// 配置WiFi网络（ESP官方推荐方式）
  Future<bool> configureWifi({
    required String ssid,
    required String password,
  }) async {
    try {
      if (!isConnected.value) {
        throw Exception('设备未连接');
      }

      AppLogger.info('开始配置WiFi: $ssid');

      // 使用ESP BluFi插件的configProvision方法
      await _espBlufi.configProvision(
        username: ssid,
        password: password,
      );

      AppLogger.info('WiFi配置命令已发送');
      return true;
    } catch (e) {
      AppLogger.error('配置WiFi失败', e);
      return false;
    }
  }

  /// 获取设备状态
  Future<DeviceStatus?> getDeviceStatus() async {
    try {
      if (!isConnected.value) {
        throw Exception('设备未连接');
      }

      await _espBlufi.requestDeviceStatus();

      // 等待状态响应
      await Future.delayed(const Duration(seconds: 2));

      return deviceStatus.value;
    } catch (e) {
      AppLogger.error('获取设备状态失败', e);
      return null;
    }
  }

  /// 发送自定义数据
  Future<void> sendCustomData(String data) async {
    try {
      if (!isConnected.value) {
        throw Exception('设备未连接');
      }

      await _espBlufi.sendCustomData(data: data);
      AppLogger.info('自定义数据已发送: $data');
    } catch (e) {
      AppLogger.error('发送自定义数据失败', e);
      rethrow;
    }
  }

  /// 获取所有已配对的设备
  Future<List<BluFiDevice>> getPairedDevices() async {
    try {
      await _espBlufi.getAllPairedDevice();
      // 结果会通过消息流返回
      return discoveredDevices.toList();
    } catch (e) {
      AppLogger.error('获取已配对设备失败', e);
      return [];
    }
  }

  /// 开始扫描设备 - 新的简化方法
  Future<void> startScan({
    String namePrefix = '',
    Duration timeout = AppConstants.scan_timeout,
  }) async {
    try {
      isScanning.value = true;
      discoveredDevices.clear();

      AppLogger.info('开始扫描设备，前缀: $namePrefix, 超时: ${timeout.inSeconds}秒');

      // 调用现有的扫描方法
      await scanDevices(
        filterPrefix: namePrefix,
        timeout: timeout,
      );
    } catch (e) {
      AppLogger.error('扫描设备失败', e);
      throw Exception('扫描设备失败: ${e.toString()}');
    } finally {
      isScanning.value = false;
    }
  }

  /// 连接到设备 - 新的简化方法
  Future<bool> connect(BluFiDevice device) async {
    try {
      AppLogger.info('连接到设备: ${device.name} (${device.address})');

      // 调用现有的连接方法
      final success = await connectToDevice(device);

      if (success) {
        currentDevice = device;
        isConnected.value = true;
        AppLogger.info('设备连接成功');
      } else {
        AppLogger.error('设备连接失败', null);
      }

      return success;
    } catch (e) {
      AppLogger.error('连接设备失败', e);
      return false;
    }
  }
}

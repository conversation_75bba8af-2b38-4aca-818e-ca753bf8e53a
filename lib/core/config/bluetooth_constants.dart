/// 蓝牙相关常量
class BluetoothConstants {
  // 设备标识
  static const String DEVICE_NAME_PREFIX = 'TWO90';
  static const String DEVICE_NAME_ALT_PREFIX = 'AI人偶';

  // 扫描设置
  static const int SCAN_TIMEOUT_SECONDS = 10;
  static const int CONNECTION_TIMEOUT_SECONDS = 15;
  static const int MAX_RETRY_COUNT = 3;
  static const int RECONNECT_DELAY_SECONDS = 2;

  // BluFi服务和特征值UUID
  static const String BLUFI_SERVICE_UUID =
      "0000ffff-0000-1000-8000-00805f9b34fb";
  static const String BLUFI_CHAR_WRITE_UUID =
      "0000ff01-0000-1000-8000-00805f9b34fb";
  static const String BLUFI_CHAR_NOTIFY_UUID =
      "0000ff02-0000-1000-8000-00805f9b34fb";

  // 命令类型常量
  static const int CMD_TYPE_WIFI_CONFIG = 1;
  static const int CMD_TYPE_DEVICE_INFO = 2;
  static const int CMD_TYPE_VOLUME_CONTROL = 3;
  static const int CMD_TYPE_MODE_SWITCH = 4;

  // 错误码
  static const int ERR_TIMEOUT = 100;
  static const int ERR_CONNECTION_FAILED = 101;
  static const int ERR_DEVICE_NOT_FOUND = 102;
  static const int ERR_SERVICE_NOT_FOUND = 103;
  static const int ERR_CHARACTERISTIC_NOT_FOUND = 104;

  // 设备模式
  static const String MODE_NORMAL = "normal";
  static const String MODE_DO_NOT_DISTURB = "do_not_disturb";
  static const String MODE_NIGHT_LIGHT = "night_light";

  // 过滤设备名称，判断是否为目标设备
  static bool isTargetDevice(String deviceName) {
    if (deviceName.isEmpty) return false;

    return deviceName.startsWith(DEVICE_NAME_PREFIX) ||
        deviceName.contains(DEVICE_NAME_ALT_PREFIX);
  }
}

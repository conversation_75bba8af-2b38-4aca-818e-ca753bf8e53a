/// 应用常量配置
class AppConstants {
  /// 应用名称
  static const String appName = '智能穿戴';

  /// 应用构建号
  static const String device_name_prefix = 'TWO_AI_';
  /// 扫描时间
  static const Duration scan_timeout = Duration(seconds: 5);

  /// 应用版本
  static const String appVersion = '1.0.0';

  /// 应用构建号
  static const int appBuildNumber = 1;

  /// 应用包名
  static const String packageName = 'com.example.smartai';

  /// API相关常量
  static const int connectTimeout = 30000;
  static const int receiveTimeout = 30000;
  static const int sendTimeout = 30000;

  /// 缓存相关常量
  static const int maxCacheSize = 10 * 1024 * 1024; // 10MB
  static const int maxCacheAge = 7 * 24 * 60 * 60; // 7天

  /// 蓝牙相关常量
  static const int scanTimeout = 10000; // 10秒
  static const int mtu = 512;

  /// 设备相关常量
  static const String deviceNamePrefix = 'SmartAI-';
  static const String deviceServiceUuid =
      '0000180a-0000-1000-8000-00805f9b34fb';
  static const String deviceCharacteristicUuid =
      '00002a29-0000-1000-8000-00805f9b34fb';

  /// 动画相关常量
  static const int animationDuration = 300; // 毫秒

  /// 分页相关常量
  static const int pageSize = 20;

  /// 图片相关常量
  static const double imageThumbnailSize = 100;
  static const double imagePreviewSize = 300;
  static const double imageFullSize = 1080;

  /// 文件相关常量
  static const int maxFileSize = 10 * 1024 * 1024; // 10MB

  /// 隐私政策和用户协议
  static const String privacyPolicyUrl = 'https://example.com/privacy-policy';
  static const String termsOfServiceUrl =
      'https://example.com/terms-of-service';

  /// 联系方式
  static const String contactEmail = '<EMAIL>';
  static const String contactPhone = '+86 123 4567 8901';

  /// 社交媒体
  static const String websiteUrl = 'https://example.com';
  static const String facebookUrl = 'https://facebook.com/example';
  static const String twitterUrl = 'https://twitter.com/example';
  static const String instagramUrl = 'https://instagram.com/example';

  /// 路由相关常量
  static const int pageTransitionDuration = 250; // 毫秒
  static const bool preventDuplicateRouting = true; // 防止重复路由

  /// HTTP请求相关常量
  static const int requestRetryCount = 1; // 请求失败重试次数
  static const int requestRetryDelay = 500; // 重试延迟，毫秒
  static const bool showLoadingByDefault = true; // 默认请求时显示加载提示
}

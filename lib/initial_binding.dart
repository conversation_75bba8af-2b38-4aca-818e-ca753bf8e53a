import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:smartai/core/controllers/language_controller.dart';
import 'package:smartai/core/controllers/theme_controller.dart';

import 'package:smartai/core/services/storage_service.dart';
import 'package:smartai/core/services/app_state_service.dart';
import 'package:smartai/core/services/blufi_service.dart';
import 'package:smartai/data/repositories/auth_repository.dart';
import 'package:smartai/data/repositories/device_repository.dart';
import 'package:smartai/data/repositories/chat_repository.dart';
import 'package:smartai/data/repositories/user_repository.dart';
import 'package:smartai/features/auth/controllers/auth_controller.dart';

/// 在应用启动时注册核心服务和控制器
class InitialBinding implements Bindings {
  @override
  Future<void> dependencies() async {
    // 核心服务
    // 使用lazyPut懒加载，仅在需要时才实例化

    // 初始化共享偏好设置
    final prefs = await SharedPreferences.getInstance();

    // 本地存储服务 - 简化版本，只使用 SharedPreferences
    Get.put<StorageService>(
      StorageService(prefs: prefs),
      permanent: true,
    );



    // 数据仓库 - 必须在服务之前初始化
    Get.put<AuthRepository>(AuthRepository(), permanent: true);
    Get.put<DeviceRepository>(DeviceRepository(), permanent: true);
    Get.put<ChatRepository>(ChatRepository(), permanent: true);
    Get.put<UserRepository>(UserRepository(), permanent: true);

    // 应用状态服务 - 核心业务状态管理
    Get.put<AppStateService>(AppStateService(), permanent: true);


    // BluFi配网服务
    Get.put<BluFiService>(BluFiService(), permanent: true);

    // 控制器
    Get.lazyPut<ThemeController>(
      () => ThemeController(),
      fenix: true,
    );

    Get.lazyPut<LanguageController>(
      () => LanguageController(),
      fenix: true,
    );

    // 认证控制器 - 使用新的简化版本
    Get.put<AuthController>(AuthController(), permanent: true);
  }
}



import 'package:get/get.dart';

/// 应用翻译
/// 包含应用中所有需要国际化的文本
class AppTranslations extends Translations {
  @override
  Map<String, Map<String, String>> get keys => {
        // 中文
        'zh_CN': {
          // 通用
          'app_name': '智能穿戴',
          'ok': '确定',
          'cancel': '取消',
          'confirm': '确认',
          'loading': '加载中',
          'retry': '重试',
          'refresh': '刷新',
          'save': '保存',
          'delete': '删除',
          'edit': '编辑',
          'success': '成功',
          'failed': '失败',
          'error': '错误',
          'warning': '警告',
          'no_data': '暂无数据',
          'unknown': '未知',
          'connect': '连接',
          'connect_to': '连接到',
          'tap_to_connect': '点击连接',

          // 认证
          'login': '登录',
          'register': '注册',
          'logout': '退出登录',
          'email': '邮箱',
          'phone': '手机号',
          'password': '密码',
          'confirm_password': '确认密码',
          'forgot_password': '忘记密码？',
          'login_success': '登录成功',
          'register_success': '注册成功',
          'verify_code': '验证码',
          'get_verify_code': '获取验证码',
          'resend': '重新发送',
          'agreement': '已阅读并同意《用户协议》和《隐私政策》',
          'password_hint': '请输入6-20位密码',
          'password_not_match': '两次密码不一致',
          'account_hint': '请输入手机号/邮箱',
          'verify_code_sent': '验证码已发送',

          // 设备
          'device': '设备',
          'device_management': '设备管理',
          'add_device': '添加设备',
          'device_detail': '设备详情',
          'device_name': '设备名称',
          'device_id': '设备ID',
          'device_status': '设备状态',
          'device_connection': '设备连接',
          'device_battery': '电量',
          'device_firmware': '固件版本',
          'device_mac': 'MAC地址',
          'device_signal': '信号强度',
          'connected': '已连接',
          'disconnected': '未连接',
          'scanning': '正在扫描',
          'scan_device': '扫描设备',
          'stop_scan': '停止扫描',
          'no_device_found': '未发现设备',
          'connect_success': '连接成功',
          'connect_failed': '连接失败',
          'disconnect_success': '断开连接成功',
          'disconnect_device': '断开设备',
          'unbind_device': '解绑设备',
          'unbind_confirm': '确定要解绑此设备吗？',

          // 蓝牙
          'bluetooth': '蓝牙设备',
          'bluetooth_not_available': '蓝牙不可用',
          'bluetooth_disabled': '蓝牙未开启，请确认AI人偶已开机，并保持电量充足',
          'bluetooth_permission_required': '需要蓝牙权限',
          'turn_on_bluetooth': '请打开蓝牙',
          'connecting': '连接中...',
          'searching': '搜索中...',

          // WiFi
          'wifi': 'WiFi',
          'wifi_config': 'WiFi配置',
          'wifi_name': 'WiFi名称',
          'wifi_password': 'WiFi密码',
          'wifi_connection': 'WiFi连接',
          'wifi_list': 'WiFi列表',
          'wifi_password_hint': '请输入WiFi密码',
          'wifi_connecting': '正在连接WiFi...',
          'wifi_connect_success': 'WiFi连接成功',
          'wifi_connect_failed': 'WiFi连接失败',
          'wifi_signal': '信号强度',
          'password_required': '请输入密码',

          // 首页
          'home': '首页',
          'message': '消息',
          'profile': '我的',
          'settings': '设置',
          'my_device': '我的设备',
          'my_avatar': '我的分身',
          'welcome': '欢迎使用',
          'today': '今天',
          'yesterday': '昨天',
          'minute_ago': '分钟前',
          'hour_ago': '小时前',

          // 设置
          'system_settings': '系统设置',
          'language_settings': '语言设置',
          'notification_settings': '通知设置',
          'about_us': '关于我们',
          'privacy_policy': '隐私政策',
          'user_agreement': '用户协议',
          'contact_us': '联系我们',
          'app_version': '应用版本',
          'check_update': '检查更新',
          'latest_version': '已是最新版本',

          // 语言
          'language': '语言',
          'chinese': '简体中文',
          'english': '英文',
          'choose_language': '选择您偏好的语言',

          // 分身
          'avatar': '分身',
          'create_avatar': '创建分身',
          'select_avatar': '选择分身',
          'avatar_name': '分身名称',
          'avatar_type': '分身类型',
          'avatar_voice': '分身声音',
          'record_voice': '录制声音',
          'play_voice': '播放声音',
          'stop_voice': '停止播放',
          'voice_duration': '录制时长',
          'record_guide': '请对着麦克风说话',
          'recording': '录制中...',
          'voice_too_short': '录音时间过短',
          'invite_code': '邀请码',
          'generate_invite': '生成邀请码',
          'avatar_completed': '分身创建完成',
          'avatar_preview': '分身预览',

          // 用户
          'user_info': '用户信息',
          'nickname': '昵称',
          'gender': '性别',
          'male': '男',
          'female': '女',
          'birthday': '生日',
          'relationship': '关系',
          'family_member': '家人',
          'friend': '朋友',
          'set_profile_photo': '设置头像',
          'edit_profile': '编辑资料',

          // 成员管理
          'member_management': '绑定成员管理',
          'add_member': '添加成员',
          'intimate_member': '亲密陪伴者',
          'invite_intimate': '邀请亲密陪伴者',
          'regular_member': '普通陪伴者',
          'add_regular': '添加普通陪伴者',
          'member_detail': '成员详情',
          'member_name': '成员名称',
          'member_type': '成员类型',
          'remove_member': '移除成员',
          'remove_confirm': '确定要移除该成员吗？',

          // 消息
          'voice_message': '语音消息',
          'text_message': '文本消息',
          'press_to_talk': '按住 说话',
          'release_to_send': '松开 发送',
          'slide_to_cancel': '滑动取消',
          'message_deleted': '消息已删除',
          'clear_messages': '清空聊天记录',
          'clear_confirm': '确定要清空所有聊天记录吗？',
          'no_messages': '暂无消息',
          'message_failed': '发送失败',
          'resend': '重新发送',
          'send': '发送',
          'type_message': '输入消息...',
        },

        // 英文
        'en_US': {
          // General
          'app_name': 'Smart Wearable',
          'ok': 'OK',
          'cancel': 'Cancel',
          'confirm': 'Confirm',
          'loading': 'Loading',
          'retry': 'Retry',
          'refresh': 'Refresh',
          'save': 'Save',
          'delete': 'Delete',
          'edit': 'Edit',
          'success': 'Success',
          'failed': 'Failed',
          'error': 'Error',
          'warning': 'Warning',
          'no_data': 'No Data',
          'unknown': 'Unknown',
          'connect': 'Connect',
          'connect_to': 'Connect to',
          'tap_to_connect': 'Tap to connect',

          // Authentication
          'login': 'Login',
          'register': 'Register',
          'logout': 'Logout',
          'email': 'Email',
          'phone': 'Phone',
          'password': 'Password',
          'confirm_password': 'Confirm Password',
          'forgot_password': 'Forgot Password?',
          'login_success': 'Login Successful',
          'register_success': 'Registration Successful',
          'verify_code': 'Verification Code',
          'get_verify_code': 'Get Code',
          'resend': 'Resend',
          'agreement':
              'I have read and agree to the User Agreement and Privacy Policy',
          'password_hint': 'Enter 6-20 characters password',
          'password_not_match': 'Passwords do not match',
          'account_hint': 'Enter phone number/email',
          'verify_code_sent': 'Verification code sent',

          // Device
          'device': 'Device',
          'device_management': 'Device Management',
          'add_device': 'Add Device',
          'device_detail': 'Device Details',
          'device_name': 'Device Name',
          'device_id': 'Device ID',
          'device_status': 'Device Status',
          'device_connection': 'Connection',
          'device_battery': 'Battery',
          'device_firmware': 'Firmware Version',
          'device_mac': 'MAC Address',
          'device_signal': 'Signal Strength',
          'connected': 'Connected',
          'disconnected': 'Disconnected',
          'scanning': 'Scanning',
          'scan_device': 'Scan Devices',
          'stop_scan': 'Stop Scan',
          'no_device_found': 'No Device Found',
          'connect_success': 'Connection Successful',
          'connect_failed': 'Connection Failed',
          'disconnect_success': 'Disconnection Successful',
          'disconnect_device': 'Disconnect Device',
          'unbind_device': 'Unbind Device',
          'unbind_confirm': 'Are you sure you want to unbind this device?',

          // Bluetooth
          'bluetooth': 'Bluetooth Devices',
          'bluetooth_not_available': 'Bluetooth Not Available',
          'bluetooth_disabled':
              'Bluetooth is off. Please make sure the AI doll is turned on with sufficient battery.',
          'bluetooth_permission_required': 'Bluetooth Permission Required',
          'turn_on_bluetooth': 'Turn On Bluetooth',
          'connecting': 'Connecting...',
          'searching': 'Searching...',

          // WiFi
          'wifi': 'WiFi',
          'wifi_config': 'WiFi Configuration',
          'wifi_name': 'WiFi Name',
          'wifi_password': 'WiFi Password',
          'wifi_connection': 'WiFi Connection',
          'wifi_list': 'WiFi List',
          'wifi_password_hint': 'Enter WiFi password',
          'wifi_connecting': 'Connecting to WiFi...',
          'wifi_connect_success': 'WiFi Connection Successful',
          'wifi_connect_failed': 'WiFi Connection Failed',
          'wifi_signal': 'Signal Strength',
          'password_required': 'Password Required',

          // Home
          'home': 'Home',
          'message': 'Messages',
          'profile': 'Profile',
          'settings': 'Settings',
          'my_device': 'My Device',
          'my_avatar': 'My Avatar',
          'welcome': 'Welcome',
          'today': 'Today',
          'yesterday': 'Yesterday',
          'minute_ago': 'min ago',
          'hour_ago': 'hours ago',

          // Settings
          'system_settings': 'System Settings',
          'language_settings': 'Language Settings',
          'notification_settings': 'Notification Settings',
          'about_us': 'About Us',
          'privacy_policy': 'Privacy Policy',
          'user_agreement': 'User Agreement',
          'contact_us': 'Contact Us',
          'app_version': 'App Version',
          'check_update': 'Check for Updates',
          'latest_version': 'Latest Version',

          // Language
          'language': 'Language',
          'chinese': 'Chinese (Simplified)',
          'english': 'English',
          'choose_language': 'Choose your preferred language',

          // Avatar
          'avatar': 'Avatar',
          'create_avatar': 'Create Avatar',
          'select_avatar': 'Select Avatar',
          'avatar_name': 'Avatar Name',
          'avatar_type': 'Avatar Type',
          'avatar_voice': 'Avatar Voice',
          'record_voice': 'Record Voice',
          'play_voice': 'Play Voice',
          'stop_voice': 'Stop Playback',
          'voice_duration': 'Recording Duration',
          'record_guide': 'Please speak into the microphone',
          'recording': 'Recording...',
          'voice_too_short': 'Recording too short',
          'invite_code': 'Invitation Code',
          'generate_invite': 'Generate Invitation Code',
          'avatar_completed': 'Avatar Creation Completed',
          'avatar_preview': 'Avatar Preview',

          // User
          'user_info': 'User Information',
          'nickname': 'Nickname',
          'gender': 'Gender',
          'male': 'Male',
          'female': 'Female',
          'birthday': 'Birthday',
          'relationship': 'Relationship',
          'family_member': 'Family Member',
          'friend': 'Friend',
          'set_profile_photo': 'Set Profile Photo',
          'edit_profile': 'Edit Profile',

          // Member Management
          'member_management': 'Member Management',
          'add_member': 'Add Member',
          'intimate_member': 'Intimate Companion',
          'invite_intimate': 'Invite Intimate Companion',
          'regular_member': 'Regular Companion',
          'add_regular': 'Add Regular Companion',
          'member_detail': 'Member Details',
          'member_name': 'Member Name',
          'member_type': 'Member Type',
          'remove_member': 'Remove Member',
          'remove_confirm': 'Are you sure you want to remove this member?',

          // Messages
          'voice_message': 'Voice Message',
          'text_message': 'Text Message',
          'press_to_talk': 'Hold to Talk',
          'release_to_send': 'Release to Send',
          'slide_to_cancel': 'Slide to Cancel',
          'message_deleted': 'Message Deleted',
          'clear_messages': 'Clear Chat History',
          'clear_confirm': 'Are you sure you want to clear all messages?',
          'no_messages': 'No Messages',
          'message_failed': 'Send Failed',
          'resend': 'Resend',
          'send': 'Send',
          'type_message': 'Type a message...',
        },
      };
}

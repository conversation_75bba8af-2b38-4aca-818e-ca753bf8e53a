# 智能穿戴设备应用

一个基于Flutter的智能穿戴设备管理应用，支持多平台部署（Android、iOS、Web）。

## 项目架构

项目采用Clean Architecture + 特性驱动开发(FDD)风格，结构如下：

```
lib/
├── core/                 # 核心功能
│   ├── config/           # 应用配置
│   ├── errors/           # 错误处理
│   ├── network/          # 网络请求
│   ├── services/         # 核心服务
│   └── utils/            # 工具函数
├── data/                 # 数据层
│   ├── models/           # 数据模型
│   └── repositories/     # 数据仓库
├── features/             # 功能模块
│   ├── auth/             # 认证模块
│   ├── device/           # 设备模块
│   ├── home/             # 首页模块
│   ├── profile/          # 个人资料模块
│   └── settings/         # 设置模块
├── routes/               # 路由管理
│   ├── app_pages.dart    # 页面路由
│   └── app_routes.dart   # 路由常量
├── shared/               # 共享组件
│   ├── bluetooth/        # 蓝牙服务
│   └── widgets/          # 共享UI组件
└── main.dart             # 应用入口
```

## 开发规范

### 1. 目录结构规范

每个功能模块（feature）按照以下结构组织：

```
features/{feature_name}/
├── bindings/             # 依赖注入
├── controllers/          # 控制器
├── views/                # 视图
└── widgets/              # 当前模块的私有组件
```

### 2. 命名规范

- **文件命名**：全部小写，单词间用下划线连接（snake_case）
- **类命名**：首字母大写（PascalCase）
- **变量/方法命名**：小驼峰命名法（camelCase）
- **常量命名**：全大写，单词间用下划线连接（SCREAMING_SNAKE_CASE）

### 3. 代码风格

- 使用`lint`规则保证代码风格统一
- 每个文件顶部需要添加文件说明注释
- 公开的API必须添加文档注释

### 4. 状态管理

- 使用GetX进行状态管理和路由控制
- 控制器（Controller）负责业务逻辑
- 视图（View）只负责UI展示

### 5. 网络请求

- 使用Dio进行网络请求
- API请求封装在`ApiClient`中
- 数据处理在Repository层进行

### 6. 错误处理

- 使用统一的错误处理机制（ErrorHandler）
- 网络错误、业务逻辑错误分类处理
- 适当的用户提示

### 7. 本地存储

- 轻量数据使用SharedPreferences
- 需考虑数据加密和安全性

## 蓝牙通信

项目支持跨平台蓝牙通信：

- **移动端**：使用`flutter_blue_plus`
- **Web端**：使用`flutter_web_bluetooth`

蓝牙服务通过工厂模式实现平台兼容。

## 开发环境设置

确保您的开发环境满足以下要求：

- Flutter SDK >= 3.4.0
- Dart SDK >= 3.4.0
- Android Studio / VS Code 最新版

## 运行项目

```bash
# 获取依赖
flutter pub get

# 运行应用（调试模式）
flutter run

# 构建Web版本
flutter build web

# 构建Android版本
flutter build apk

# 构建iOS版本
flutter build ios
```


## 协议

版权所有 © 2023-2026 totwoo智能穿戴设备团队

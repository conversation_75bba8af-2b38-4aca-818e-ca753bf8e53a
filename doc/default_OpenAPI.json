{"openapi": "3.0.1", "info": {"title": "API文档", "description": "SpringBoot集成Knife4j 4.6.0示例", "version": "1.0"}, "servers": [{"url": "http://************:8060", "description": "Generated server url"}], "paths": {"/xiaozhi/ota/": {"post": {"tags": ["ota-controller"], "operationId": "ota", "parameters": [{"name": "Device-Id", "in": "header", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "string"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "string", "format": "byte"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/vl/chat": {"post": {"tags": ["vl-chat-controller"], "operationId": "vlChat", "parameters": [{"name": "question", "in": "query", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"required": ["file"], "type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}}}}, "responses": {"200": {"description": "OK", "content": {"application/json;charset=UTF-8": {"schema": {"type": "string"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/app/uploadVoicePrint": {"post": {"tags": ["App相关接口"], "summary": "上传录制声纹", "operationId": "uploadVoicePrint", "parameters": [{"name": "dollProfileUserId", "in": "query", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultMapStringBoolean"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/app/uploadVoiceCloneFile": {"post": {"tags": ["App相关接口"], "summary": "上传音频文件进行音色克隆", "description": "这个接口不要随表调用,挺贵的,一次9.9块钱,type=1选择音色,2复刻音色,3上传音色", "operationId": "uploadVoiceCloneFile", "parameters": [{"name": "agentId", "in": "query", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "type", "in": "query", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "userId", "in": "query", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "ttsVoice", "in": "query", "required": false, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultMapStringBoolean"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/app/uploadChatFile": {"post": {"tags": ["App相关接口"], "summary": "上传聊天记录", "operationId": "uploadChatFile", "parameters": [{"name": "userId", "in": "query", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"required": ["file"], "type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultMapStringBoolean"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/app/updateDeviceInfo": {"post": {"tags": ["App相关接口"], "summary": "更新设备信息", "operationId": "updateDeviceInfo", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeviceInfoReq"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultBoolean"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/app/updateCloneInfo": {"post": {"tags": ["App相关接口"], "summary": "更新分身接口", "operationId": "updateCloneInfo", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DollProfileInfo"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultBoolean"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/app/unbind": {"post": {"tags": ["App相关接口"], "summary": "解绑设备", "operationId": "unbindDevice", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BindReq"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultBoolean"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/app/register": {"post": {"tags": ["App相关接口"], "summary": "注册接口", "operationId": "register", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultUser"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/app/inviteIntimateCompanionInfo": {"post": {"tags": ["App相关接口"], "summary": "邀请亲密陪伴者接口", "operationId": "inviteIntimateCompanionInfo", "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Partner"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultMapStringBoolean"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/app/invitation": {"post": {"tags": ["App相关接口"], "summary": "邀请他人创建分身", "operationId": "invitation", "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/InvitationReq"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultBoolean"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/app/getCloneInfoByAgentId": {"post": {"tags": ["App相关接口"], "summary": "拉取分身信息", "operationId": "getCloneInfoByAgentId", "parameters": [{"name": "agentId", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultDollProfileInfo"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/app/editVoiceCloneFile": {"post": {"tags": ["App相关接口"], "summary": "更新克隆音色", "description": "这个接口不要随表调用,挺贵的,一次9.9块钱,type=1选择音色,2复刻音色,3上传音色", "operationId": "editVoiceCloneFile", "parameters": [{"name": "agentId", "in": "query", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "type", "in": "query", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "voiceId", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "userId", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "id", "in": "query", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "ttsVoice", "in": "query", "required": false, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultMapStringBoolean"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/app/delUploadChatFile": {"post": {"tags": ["App相关接口"], "summary": "删除", "operationId": "delUploadChatFile", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FileReq"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultMapStringBoolean"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/app/delIntimateCompanionInfo": {"post": {"tags": ["App相关接口"], "summary": "删除初始创建者,亲密陪伴者,普通陪伴者接口", "operationId": "delIntimateCompanionInfo", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DollUserProfileInfo"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultMapStringBoolean"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/app/createIntimateCompanionInfo": {"post": {"tags": ["App相关接口"], "summary": "创建初始创建者,亲密陪伴者,普通陪伴者接口", "operationId": "createIntimateCompanionInfo", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DollUserProfileInfoDTO"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultDollUserProfileInfo"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/app/createCloneInfo": {"post": {"tags": ["App相关接口"], "summary": "创建分身接口", "operationId": "createCloneInfo", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DollProfileInfo"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultDollProfileInfo"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/app/cancelInviteIntimateCompanionInfo": {"post": {"tags": ["App相关接口"], "summary": "取消亲密陪伴者邀请", "operationId": "cancelInviteIntimateCompanionInfo", "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Partner"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultMapStringBoolean"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/app/bind": {"post": {"tags": ["App相关接口"], "summary": "设备绑定", "operationId": "bind", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BindReq"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultBoolean"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/api/user/update": {"post": {"tags": ["user-controller"], "operationId": "update", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"type": "object"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/api/user/sendEmailCaptcha": {"post": {"tags": ["user-controller"], "operationId": "sendEmailCaptcha", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/api/user/login": {"post": {"tags": ["user-controller"], "operationId": "login", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"type": "object"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/api/user/add": {"post": {"tags": ["user-controller"], "operationId": "add", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"type": "object"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/api/template/update": {"post": {"tags": ["template-controller"], "operationId": "update_1", "parameters": [{"name": "template", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/SysTemplate"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/api/template/add": {"post": {"tags": ["template-controller"], "operationId": "add_1", "parameters": [{"name": "template", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/SysTemplate"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/api/role/update": {"post": {"tags": ["role-controller"], "operationId": "update_2", "parameters": [{"name": "role", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/SysRole"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/api/role/add": {"post": {"tags": ["role-controller"], "operationId": "add_2", "parameters": [{"name": "role", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/SysRole"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/api/message/delete": {"post": {"tags": ["message-controller"], "operationId": "delete", "parameters": [{"name": "message", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/SysMessage"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/api/file/upload": {"post": {"tags": ["file-upload-controller"], "operationId": "uploadFile", "parameters": [{"name": "type", "in": "query", "required": false, "schema": {"type": "string", "default": "common"}}], "requestBody": {"content": {"application/json": {"schema": {"required": ["file"], "type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/api/device/update": {"post": {"tags": ["device-controller"], "operationId": "update_3", "parameters": [{"name": "device", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/SysDevice"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/api/device/ota": {"post": {"tags": ["device-controller"], "operationId": "ota_1", "parameters": [{"name": "Device-Id", "in": "header", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "string"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "string", "format": "byte"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/api/device/ota/activate": {"post": {"tags": ["device-controller"], "operationId": "otaActivate", "parameters": [{"name": "Device-Id", "in": "header", "description": "设备唯一标识", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "string"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/api/device/delete": {"post": {"tags": ["device-controller"], "operationId": "delete_1", "parameters": [{"name": "device", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/SysDevice"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/api/device/add": {"post": {"tags": ["device-controller"], "operationId": "add_3", "parameters": [{"name": "code", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/api/config/update": {"post": {"tags": ["config-controller"], "operationId": "update_4", "parameters": [{"name": "config", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/SysConfig"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/api/config/getModels": {"post": {"tags": ["config-controller"], "operationId": "getModels", "parameters": [{"name": "config", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/SysConfig"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/api/config/add": {"post": {"tags": ["config-controller"], "operationId": "add_4", "parameters": [{"name": "config", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/SysConfig"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/api/agent/update": {"post": {"tags": ["agent-controller"], "operationId": "update_5", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SysAgent"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/api/agent/delete": {"post": {"tags": ["agent-controller"], "operationId": "delete_2", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SysAgent"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/api/agent/add": {"post": {"tags": ["agent-controller"], "operationId": "add_5", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SysAgent"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/app/login": {"get": {"tags": ["App相关接口"], "summary": "登陆接口", "operationId": "login_1", "parameters": [{"name": "email", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "password", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultUser"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/app/getVoiceList": {"get": {"tags": ["App相关接口"], "summary": "获取音色列表", "operationId": "getVoiceList", "parameters": [{"name": "type", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultJSONArray"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/app/getVoiceCloneFile": {"get": {"tags": ["App相关接口"], "summary": "获取克隆音色", "description": "这个接口不要随表调用,挺贵的,一次9.9块钱,type=1选择音色,2复刻音色,3上传音色", "operationId": "getVoiceCloneFile", "parameters": [{"name": "agentId", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultTtsVoice"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/app/getUploadChatFiles": {"get": {"tags": ["App相关接口"], "summary": "获取文件上传列表", "operationId": "getUploadChatFiles", "parameters": [{"name": "userId", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultListUserFile"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/app/getReadingContent": {"get": {"tags": ["App相关接口"], "summary": "获取朗读内容", "operationId": "getReadingContent", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultListString"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/app/getInviteIntimateCompanionInfo": {"get": {"tags": ["App相关接口"], "summary": "获取亲密陪伴者邀请列表", "operationId": "getInviteIntimateCompanionInfo", "parameters": [{"name": "userId", "in": "query", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultListPartner"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/app/getInvitationList": {"get": {"tags": ["App相关接口"], "summary": "获取分身邀请列表", "operationId": "getInvitationList", "parameters": [{"name": "emails", "in": "query", "required": true, "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultMapStringBoolean"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/app/getIntimateCompanionInfos": {"get": {"tags": ["App相关接口"], "summary": "获取陪伴者列表", "operationId": "getIntimateCompanionInfos", "parameters": [{"name": "agentId", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "intimacyType", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultListDollUserProfileInfo"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/app/getDeviceByAgentId": {"get": {"tags": ["App相关接口"], "summary": "设备详情", "operationId": "getDeviceByAgentId", "parameters": [{"name": "agentId", "in": "query", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultSysDeviceReq"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/app/getCurrentInfo": {"get": {"tags": ["App相关接口"], "summary": "获取用户进度", "operationId": "getCurrentInfo", "parameters": [{"name": "userId", "in": "query", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultCurrentInfo"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/app/getCloneList": {"get": {"tags": ["App相关接口"], "summary": "获取我的分身设备", "operationId": "getCloneList", "parameters": [{"name": "userId", "in": "query", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultListDollProfileInfo"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/app/getBaseInfo": {"get": {"tags": ["App相关接口"], "summary": "获取首页基本信息", "operationId": "getBaseInfo", "parameters": [{"name": "userId", "in": "query", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultSysDevice"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/app/cancelInvitation": {"get": {"tags": ["App相关接口"], "summary": "取消分身邀请", "operationId": "cancelInvitation", "parameters": [{"name": "emails", "in": "query", "required": true, "schema": {"type": "array", "items": {"type": "string"}}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ResultMapStringBoolean"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/api/user/query": {"get": {"tags": ["user-controller"], "operationId": "query", "parameters": [{"name": "username", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/api/user/queryUsers": {"get": {"tags": ["user-controller"], "operationId": "queryUsers", "parameters": [{"name": "user", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/SysUser"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/api/user/checkUser": {"get": {"tags": ["user-controller"], "operationId": "checkUser", "parameters": [{"name": "username", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "email", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/api/user/checkCaptcha": {"get": {"tags": ["user-controller"], "operationId": "checkCaptcha", "parameters": [{"name": "code", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "email", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/api/template/query": {"get": {"tags": ["template-controller"], "operationId": "query_1", "parameters": [{"name": "template", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/SysTemplate"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/api/role/testVoice": {"get": {"tags": ["role-controller"], "operationId": "testAudio", "parameters": [{"name": "message", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "provider", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "ttsId", "in": "query", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "voiceName", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/api/role/query": {"get": {"tags": ["role-controller"], "operationId": "query_2", "parameters": [{"name": "role", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/SysRole"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/api/message/query": {"get": {"tags": ["message-controller"], "operationId": "query_3", "parameters": [{"name": "message", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/SysMessage"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/api/device/query": {"get": {"tags": ["device-controller"], "operationId": "query_4", "parameters": [{"name": "device", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/SysDevice"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/api/config/query": {"get": {"tags": ["config-controller"], "operationId": "query_5", "parameters": [{"name": "config", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/SysConfig"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}, "/api/agent/query": {"get": {"tags": ["agent-controller"], "operationId": "query_6", "parameters": [{"name": "agent", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/SysAgent"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/AjaxResult"}}}}}}}}, "components": {"schemas": {"AjaxResult": {"type": "object", "properties": {"message": {"type": "string"}, "data": {"type": "object"}, "code": {"type": "integer", "format": "int32"}, "empty": {"type": "boolean"}}, "additionalProperties": {"type": "object"}}, "ResultMapStringBoolean": {"type": "object", "properties": {"code": {"type": "integer", "description": "编码：0表示成功，其他值表示失败", "format": "int32"}, "msg": {"type": "string", "description": "消息内容"}, "data": {"type": "object", "additionalProperties": {"type": "boolean", "description": "响应数据"}, "description": "响应数据"}}, "description": "响应"}, "DeviceInfoReq": {"type": "object", "properties": {"macAddress": {"type": "string"}}}, "ResultBoolean": {"type": "object", "properties": {"code": {"type": "integer", "description": "编码：0表示成功，其他值表示失败", "format": "int32"}, "msg": {"type": "string", "description": "消息内容"}, "data": {"type": "boolean", "description": "响应数据"}}, "description": "响应"}, "DollProfileInfo": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键ID", "format": "int32"}, "agentId": {"type": "string", "description": "智能体ID"}, "dollName": {"type": "string", "description": "姓名"}, "dollNickName": {"type": "string", "description": "昵称"}, "dollGender": {"type": "string", "description": "性别"}, "dollAge": {"type": "string", "description": "年龄"}, "dollBirthday": {"type": "string", "description": "出生日期"}, "dollBirthdayTime": {"type": "string", "description": "出生时间"}, "dollCity": {"type": "string", "description": "出生城市"}, "dollZodiacSign": {"type": "string", "description": "星座"}, "dollProfession": {"type": "string", "description": "职业"}, "dollCharacterDescription": {"type": "string", "description": "性格特征"}, "dollLanguageCharacteristic": {"type": "string", "description": "语言特点"}, "createBy": {"type": "string", "description": "创建人ID"}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time"}, "updateBy": {"type": "string", "description": "修改人ID"}, "updateTime": {"type": "string", "description": "修改时间", "format": "date-time"}, "email": {"type": "string", "description": "邮箱"}, "userId": {"type": "integer", "description": "用户id", "format": "int32"}, "userName": {"type": "string", "description": "用户名"}, "profileBrief": {"type": "string", "description": "性格与背景预览"}, "delFlag": {"type": "integer", "description": "删除逻辑 0:正常 1:删除", "format": "int32"}, "superPower": {"type": "string", "description": "超级技能"}, "chatTopics": {"type": "string", "description": "聊天话题"}, "example_conversation_1": {"type": "string", "description": "当“他”情绪低落或说“我最近很糟糕”时"}, "example_conversation_2": {"type": "string", "description": "当“你”想表达“我好想你”，你会怎么说？"}, "example_conversation_3": {"type": "string", "description": "你”通常怎么说“我爱你”？"}, "example_conversation_4": {"type": "string", "description": "当“你”不高兴或生气时，你会怎么表现？"}, "example_conversation_5": {"type": "string", "description": "当你担心他身体或状态不好时，你通常怎么说"}}, "description": "AI玩偶角色基础信息"}, "BindReq": {"type": "object", "properties": {"macAddress": {"type": "string", "description": "<PERSON><PERSON><PERSON><PERSON>"}, "userId": {"type": "integer", "description": "用户Id", "format": "int32"}}}, "User": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "email": {"type": "string"}, "userName": {"type": "string"}, "password": {"type": "string"}, "avatar": {"type": "string"}, "status": {"type": "integer", "format": "int32"}, "createTime": {"type": "integer", "format": "int64"}, "updateTime": {"type": "integer", "format": "int64"}, "remark": {"type": "string"}, "token": {"type": "string"}}}, "ResultUser": {"type": "object", "properties": {"code": {"type": "integer", "description": "编码：0表示成功，其他值表示失败", "format": "int32"}, "msg": {"type": "string", "description": "消息内容"}, "data": {"$ref": "#/components/schemas/User"}}, "description": "响应"}, "Partner": {"type": "object", "properties": {"id": {"type": "integer", "description": "id", "format": "int32"}, "userId": {"type": "integer", "description": "邀请用户", "format": "int32"}, "email": {"type": "string", "description": "用户邮箱"}, "status": {"type": "integer", "description": "状态:1.邀请中,2.已创建,3.已取消", "format": "int32"}, "intimacyType": {"type": "string", "description": "用户类型:100亲密陪伴者,200普通陪伴者,300创建者"}, "createTime": {"type": "integer", "description": "创建时间", "format": "int64"}, "updateTime": {"type": "integer", "description": "更新时间", "format": "int64"}}}, "InvitationReq": {"type": "object", "properties": {"email": {"type": "string", "description": "邮箱"}, "userId": {"type": "integer", "description": "用户ID", "format": "int32"}, "type": {"type": "integer", "description": "100亲密陪伴者 200普通陪伴者 300创建者", "format": "int32"}}}, "ResultDollProfileInfo": {"type": "object", "properties": {"code": {"type": "integer", "description": "编码：0表示成功，其他值表示失败", "format": "int32"}, "msg": {"type": "string", "description": "消息内容"}, "data": {"$ref": "#/components/schemas/DollProfileInfo"}}, "description": "响应"}, "FileReq": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}}}, "DollUserProfileInfo": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键ID", "format": "int64"}, "agentId": {"type": "string", "description": "智能体ID"}, "dollId": {"type": "string", "description": "玩偶ID"}, "intimateRelation": {"type": "string", "description": "亲密关系"}, "intimacyType": {"type": "integer", "description": "亲密程度", "format": "int32"}, "isDefault": {"type": "integer", "description": "是否默认用户 0:否 1：是", "format": "int32"}, "relationStatus": {"type": "string", "description": "关系现状"}, "interactiveFeatures": {"type": "string", "description": "互动特征"}, "userName": {"type": "string", "description": "姓名"}, "userNickName": {"type": "string", "description": "昵称"}, "userGender": {"type": "string", "description": "性别"}, "userAge": {"type": "integer", "description": "年龄", "format": "int32"}, "userBirthday": {"type": "integer", "description": "出生日期", "format": "int64"}, "userBirthdayTime": {"type": "integer", "description": "出生时间", "format": "int64"}, "userCity": {"type": "string", "description": "出生城市"}, "userZodiacSign": {"type": "string", "description": "星座"}, "userProfession": {"type": "string", "description": "职业"}, "userCharacterFeature": {"type": "string", "description": "性格特征"}, "chatTopics": {"type": "string", "description": "聊天话题"}, "createBy": {"type": "string", "description": "创建人ID"}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time"}, "updateBy": {"type": "string", "description": "修改人ID"}, "updateTime": {"type": "string", "description": "修改时间", "format": "date-time"}, "delFlag": {"type": "integer", "description": "删除逻辑 0:正常 1:删除", "format": "int32"}, "memory": {"type": "string", "description": "用户记忆"}, "email": {"type": "string", "description": "用户邮箱"}}, "description": "AI玩偶使用者基础信息"}, "DollUserProfileInfoDTO": {"required": ["agentId", "dollId"], "type": "object", "properties": {"id": {"type": "string", "description": "主键ID"}, "agentId": {"type": "string", "description": "智能体ID"}, "dollId": {"type": "string", "description": "玩偶ID"}, "intimateRelation": {"type": "string", "description": "亲密关系"}, "intimacyType": {"type": "integer", "description": "亲密程度: 100亲密陪伴者 200普通陪伴者 300创建者", "format": "int32"}, "relationStatus": {"type": "string", "description": "关系现状"}, "interactiveFeatures": {"type": "string", "description": "互动特征"}, "userName": {"type": "string", "description": "姓名"}, "userNickName": {"type": "string", "description": "昵称"}, "userGender": {"type": "string", "description": "性别"}, "userAge": {"type": "integer", "description": "年龄", "format": "int32"}, "userBirthday": {"type": "integer", "description": "出生日期", "format": "int64"}, "birthDateTime": {"type": "integer", "description": "出生时间", "format": "int64"}, "userCity": {"type": "string", "description": "出生城市"}, "userZodiacSign": {"type": "string", "description": "星座"}, "userProfession": {"type": "string", "description": "职业"}, "userCharacterFeature": {"type": "string", "description": "性格特征"}, "userId": {"type": "integer", "description": "用户ID", "format": "int64"}, "chatTopics": {"type": "string", "description": "聊天话题"}, "email": {"type": "string", "description": "用户邮箱"}}, "description": "AI玩偶使用者基础信息"}, "ResultDollUserProfileInfo": {"type": "object", "properties": {"code": {"type": "integer", "description": "编码：0表示成功，其他值表示失败", "format": "int32"}, "msg": {"type": "string", "description": "消息内容"}, "data": {"$ref": "#/components/schemas/DollUserProfileInfo"}}, "description": "响应"}, "SysTemplate": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "createTime": {"type": "string", "format": "date-time"}, "updateTime": {"type": "string", "format": "date-time"}, "templateId": {"type": "integer", "format": "int32"}, "templateName": {"type": "string"}, "templateDesc": {"type": "string"}, "templateContent": {"type": "string"}, "category": {"type": "string"}, "isDefault": {"type": "string"}, "state": {"type": "string"}}}, "SysRole": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "createTime": {"type": "string", "format": "date-time"}, "updateTime": {"type": "string", "format": "date-time"}, "userId": {"type": "integer", "format": "int32"}, "startTime": {"type": "string", "format": "date-time"}, "endTime": {"type": "string", "format": "date-time"}, "roleId": {"type": "integer", "description": "角色ID", "format": "int32"}, "avatar": {"type": "string", "description": "角色头像"}, "roleName": {"type": "string", "description": "角色名称"}, "roleDesc": {"type": "string", "description": "角色描述"}, "voiceName": {"type": "string", "description": "语音名称"}, "state": {"type": "string", "description": "状态(1启用 0禁用)"}, "ttsId": {"type": "integer", "description": "TTS服务ID", "format": "int32"}, "modelId": {"type": "integer", "description": "模型ID", "format": "int32"}, "modelName": {"type": "string", "description": "模型名称"}, "sttId": {"type": "integer", "description": "STT服务ID", "format": "int32"}, "temperature": {"type": "number", "description": "温度参数，控制输出的随机性", "format": "double"}, "topP": {"type": "number", "description": "Top-P参数，控制输出的多样性", "format": "double"}, "vadEnergyTh": {"type": "number", "description": "语音活动检测-能量阈值", "format": "float"}, "vadSpeechTh": {"type": "number", "description": "语音活动检测-语音阈值", "format": "float"}, "vadSilenceTh": {"type": "number", "description": "语音活动检测-静音阈值", "format": "float"}, "vadSilenceMs": {"type": "integer", "description": "语音活动检测-静音毫秒数", "format": "int32"}, "modelProvider": {"type": "string", "description": "模型提供商"}, "ttsProvider": {"type": "string", "description": "TTS服务提供商"}, "isDefault": {"type": "string", "description": "是否默认角色(1是 0否)"}, "totalDevice": {"type": "integer", "description": "总设备数", "format": "int32"}, "summaryMemory": {"type": "string", "description": "短期记忆"}, "ttsVoice": {"type": "string", "description": "TTS语音"}}}, "SysMessage": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "createTime": {"type": "string", "format": "date-time"}, "updateTime": {"type": "string", "format": "date-time"}, "messageId": {"type": "integer", "format": "int32"}, "deviceId": {"type": "string"}, "sender": {"type": "string"}, "message": {"type": "string"}, "audioPath": {"type": "string"}, "state": {"type": "string"}, "messageType": {"type": "string"}, "sessionId": {"type": "string"}, "roleId": {"type": "integer", "format": "int32"}, "roleName": {"type": "string"}, "deviceName": {"type": "string"}}}, "SysDevice": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "createTime": {"type": "string", "format": "date-time"}, "updateTime": {"type": "string", "format": "date-time"}, "userId": {"type": "integer", "description": "设备用户id", "format": "int32"}, "roleId": {"type": "integer", "description": "角色ID", "format": "int32"}, "avatar": {"type": "string", "description": "角色头像"}, "roleName": {"type": "string", "description": "角色名称"}, "roleDesc": {"type": "string", "description": "角色描述"}, "voiceName": {"type": "string", "description": "语音名称"}, "state": {"type": "string", "description": "设备状态:1.在线,0,离线"}, "ttsId": {"type": "integer", "description": "TTS服务ID", "format": "int32"}, "modelId": {"type": "integer", "description": "模型ID", "format": "int32"}, "modelName": {"type": "string", "description": "模型名称"}, "sttId": {"type": "integer", "description": "STT服务ID", "format": "int32"}, "temperature": {"type": "number", "description": "温度参数，控制输出的随机性", "format": "double"}, "topP": {"type": "number", "description": "Top-P参数，控制输出的多样性", "format": "double"}, "vadEnergyTh": {"type": "number", "description": "语音活动检测-能量阈值", "format": "float"}, "vadSpeechTh": {"type": "number", "description": "语音活动检测-语音阈值", "format": "float"}, "vadSilenceTh": {"type": "number", "description": "语音活动检测-静音阈值", "format": "float"}, "vadSilenceMs": {"type": "integer", "description": "语音活动检测-静音毫秒数", "format": "int32"}, "modelProvider": {"type": "string", "description": "模型提供商"}, "ttsProvider": {"type": "string", "description": "TTS服务提供商"}, "isDefault": {"type": "string", "description": "是否默认角色(1是 0否)"}, "totalDevice": {"type": "integer", "description": "总设备数", "format": "int32"}, "summaryMemory": {"type": "string", "description": "短期记忆"}, "ttsVoice": {"type": "string", "description": "TTS语音"}, "deviceId": {"type": "string", "description": "设备ID,其实就是macAddress"}, "sessionId": {"type": "string", "description": "会话ID"}, "deviceName": {"type": "string", "description": "设备名称"}, "deviceType": {"type": "integer", "description": "1.web,2.app,3.硬件", "format": "int32"}, "totalMessage": {"type": "integer", "description": "设备对话次数", "format": "int32"}, "audioPath": {"type": "string", "description": "音频文件"}, "lastLogin": {"type": "string", "description": "最后在线时间"}, "wifiName": {"type": "string", "description": "WiFi名称"}, "ip": {"type": "string", "description": "IP"}, "chipModelName": {"type": "string", "description": "芯片型号"}, "type": {"type": "string", "description": "类型"}, "version": {"type": "string", "description": "固件版本"}, "functionNames": {"type": "string", "description": "用全局function的名称列表"}, "dollName": {"type": "string", "description": "娃娃名称"}, "network": {"type": "integer", "description": "网络情况:1.已连接,0.未连接", "format": "int32"}, "battery": {"type": "integer", "description": "电量:40", "format": "int32"}, "volume": {"type": "integer", "description": "音量:20", "format": "int32"}, "deviceMode": {"type": "integer", "description": "设备模式:1.已开启,2.未开启(免打扰)", "format": "int32"}, "nightLight": {"type": "integer", "description": "设备状态:1.已开启,2.未开启(夜灯模式)", "format": "int32"}, "hwName": {"type": "string", "description": "主板名称"}, "hwVer": {"type": "string", "description": "主板序列号"}, "btName": {"type": "string", "description": "蓝牙名称"}, "sn": {"type": "string", "description": "序列号"}}}, "SysConfig": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "createTime": {"type": "string", "format": "date-time"}, "updateTime": {"type": "string", "format": "date-time"}, "configId": {"type": "integer", "format": "int32"}, "deviceId": {"type": "string"}, "roleId": {"type": "integer", "format": "int32"}, "configName": {"type": "string"}, "configDesc": {"type": "string"}, "configType": {"type": "string"}, "modelType": {"type": "string"}, "provider": {"type": "string"}, "appId": {"type": "string"}, "apiKey": {"type": "string"}, "apiSecret": {"type": "string"}, "ak": {"type": "string"}, "sk": {"type": "string"}, "apiUrl": {"type": "string"}, "state": {"type": "string"}, "isDefault": {"type": "string"}}}, "SysAgent": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "createTime": {"type": "string", "format": "date-time"}, "updateTime": {"type": "string", "format": "date-time"}, "configId": {"type": "integer", "format": "int32"}, "deviceId": {"type": "string"}, "roleId": {"type": "integer", "format": "int32"}, "configName": {"type": "string"}, "configDesc": {"type": "string"}, "configType": {"type": "string"}, "modelType": {"type": "string"}, "provider": {"type": "string"}, "appId": {"type": "string"}, "apiKey": {"type": "string"}, "apiSecret": {"type": "string"}, "ak": {"type": "string"}, "sk": {"type": "string"}, "apiUrl": {"type": "string"}, "state": {"type": "string"}, "isDefault": {"type": "string"}, "agentId": {"type": "integer", "format": "int32"}, "agentName": {"type": "string"}, "botId": {"type": "string"}, "agentDesc": {"type": "string"}, "iconUrl": {"type": "string"}, "publishTime": {"type": "string", "format": "date-time"}}}, "ResultJSONArray": {"type": "object", "properties": {"code": {"type": "integer", "description": "编码：0表示成功，其他值表示失败", "format": "int32"}, "msg": {"type": "string", "description": "消息内容"}, "data": {"type": "array", "properties": {"empty": {"type": "boolean"}, "componentType": {"type": "object", "properties": {"typeName": {"type": "string"}}}, "relatedArray": {"type": "object"}, "first": {"type": "object"}, "last": {"type": "object"}}, "description": "响应数据", "items": {"type": "object", "description": "响应数据"}}}, "description": "响应"}, "ResultTtsVoice": {"type": "object", "properties": {"code": {"type": "integer", "description": "编码：0表示成功，其他值表示失败", "format": "int32"}, "msg": {"type": "string", "description": "消息内容"}, "data": {"$ref": "#/components/schemas/TtsVoice"}}, "description": "响应"}, "TtsVoice": {"type": "object", "properties": {"id": {"type": "integer", "description": "id", "format": "int32", "refType": null}, "languages": {"type": "string", "description": "语言", "refType": null}, "name": {"type": "string", "description": "音色名称", "refType": null}, "remark": {"type": "string", "description": "备注", "refType": null}, "referenceAudio": {"type": "string", "description": "参考音频路径", "refType": null}, "referenceText": {"type": "string", "description": "參考文本", "refType": null}, "sort": {"type": "integer", "description": "排序", "format": "int64", "refType": null}, "ttsModelId": {"type": "string", "description": "对应 TTS 模型主键", "refType": null}, "ttsVoice": {"type": "string", "description": "音色编码", "refType": null}, "voiceDemo": {"type": "string", "description": "音频播放地址", "refType": null}, "voiceType": {"type": "string", "description": "音色类型", "enum": ["SYSTEM_VOICE", "CLONE_VOICE"], "refType": null}, "voiceFileId": {"type": "string", "description": "音色文件ID", "refType": null}, "updater": {"type": "integer", "description": "更新者", "format": "int64", "refType": null}, "updateDate": {"type": "string", "description": "更新时间", "format": "date-time", "refType": null}, "creator": {"type": "integer", "description": "创建者", "format": "int64", "refType": null}, "createDate": {"type": "string", "description": "创建时间", "format": "date-time", "refType": null}, "delFlag": {"type": "integer", "description": "删除标志（0-正常，1-删除）", "format": "int32", "refType": null}, "roleId": {"type": "integer", "description": "分身Id", "format": "int32", "refType": null}, "type": {"type": "integer", "description": "type=1选择音色,2复刻音色,3上传音色", "format": "int32", "refType": null}}, "description": "音色信息"}, "ResultListUserFile": {"type": "object", "properties": {"code": {"type": "integer", "description": "编码：0表示成功，其他值表示失败", "format": "int32"}, "msg": {"type": "string", "description": "消息内容"}, "data": {"type": "array", "description": "响应数据", "items": {"$ref": "#/components/schemas/UserFile"}}}, "description": "响应"}, "UserFile": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "fileUrl": {"type": "string"}, "userId": {"type": "integer", "format": "int32"}, "createTime": {"type": "integer", "format": "int64"}, "updateTime": {"type": "integer", "format": "int64"}}, "description": "响应数据"}, "ResultListString": {"type": "object", "properties": {"code": {"type": "integer", "description": "编码：0表示成功，其他值表示失败", "format": "int32"}, "msg": {"type": "string", "description": "消息内容"}, "data": {"type": "array", "description": "响应数据", "items": {"type": "string", "description": "响应数据"}}}, "description": "响应"}, "ResultListPartner": {"type": "object", "properties": {"code": {"type": "integer", "description": "编码：0表示成功，其他值表示失败", "format": "int32"}, "msg": {"type": "string", "description": "消息内容"}, "data": {"type": "array", "description": "响应数据", "items": {"$ref": "#/components/schemas/Partner"}}}, "description": "响应"}, "ResultListDollUserProfileInfo": {"type": "object", "properties": {"code": {"type": "integer", "description": "编码：0表示成功，其他值表示失败", "format": "int32"}, "msg": {"type": "string", "description": "消息内容"}, "data": {"type": "array", "description": "响应数据", "items": {"$ref": "#/components/schemas/DollUserProfileInfo"}}}, "description": "响应"}, "ResultSysDeviceReq": {"type": "object", "properties": {"code": {"type": "integer", "description": "编码：0表示成功，其他值表示失败", "format": "int32"}, "msg": {"type": "string", "description": "消息内容"}, "data": {"$ref": "#/components/schemas/SysDeviceReq"}}, "description": "响应"}, "SysDeviceReq": {"type": "object", "properties": {"deviceId": {"type": "string", "description": "设备ID,其实就是macAddress"}, "macAddress": {"type": "string", "description": "mac地址"}, "deviceName": {"type": "string", "description": "设备名称"}, "userId": {"type": "integer", "description": "设备用户id", "format": "int32"}, "state": {"type": "string", "description": "设备状态:1.在线,0,离线"}, "deviceType": {"type": "integer", "description": "1.web,2.app,3.硬件", "format": "int32"}, "lastLogin": {"type": "string", "description": "最后在线时间"}, "wifiName": {"type": "string", "description": "WiFi名称"}, "ip": {"type": "string", "description": "IP"}, "chipModelName": {"type": "string", "description": "芯片型号"}, "type": {"type": "string", "description": "类型"}, "version": {"type": "string", "description": "固件版本"}, "psn": {"type": "string", "description": "PSN"}, "dollName": {"type": "string", "description": "娃娃名称"}, "network": {"type": "integer", "description": "网络情况:1.已连接,0.未连接", "format": "int32"}, "battery": {"type": "integer", "description": "电量:40", "format": "int32"}, "volume": {"type": "integer", "description": "音量:20", "format": "int32"}, "deviceMode": {"type": "integer", "description": "设备模式:1.已开启,2.未开启(免打扰)", "format": "int32"}, "nightLight": {"type": "integer", "description": "设备状态:1.已开启,2.未开启(夜灯模式)", "format": "int32"}, "hwName": {"type": "string", "description": "主板名称"}, "hwVer": {"type": "string", "description": "主板版本号"}, "btName": {"type": "string", "description": "蓝牙名称"}, "sn": {"type": "string", "description": "SN"}}, "description": "响应数据"}, "CurrentInfo": {"type": "object", "properties": {"userId": {"type": "integer", "description": "用户id", "format": "int32"}, "deviceId": {"type": "string", "description": "设备id"}, "agentId": {"type": "integer", "description": "分身id", "format": "int32"}, "fileIds": {"type": "array", "description": "文件id", "items": {"type": "integer", "description": "文件id", "format": "int64"}}, "cloneVoiceId": {"type": "string", "description": "分身克隆音色id"}, "dollUserId": {"type": "array", "description": "亲密陪伴者id", "items": {"type": "integer", "description": "亲密陪伴者id", "format": "int64"}}, "voicePrintIds": {"type": "array", "description": "陪伴者声纹id", "items": {"type": "string", "description": "陪伴者声纹id"}}}, "description": "响应数据"}, "ResultCurrentInfo": {"type": "object", "properties": {"code": {"type": "integer", "description": "编码：0表示成功，其他值表示失败", "format": "int32"}, "msg": {"type": "string", "description": "消息内容"}, "data": {"$ref": "#/components/schemas/CurrentInfo"}}, "description": "响应"}, "ResultListDollProfileInfo": {"type": "object", "properties": {"code": {"type": "integer", "description": "编码：0表示成功，其他值表示失败", "format": "int32"}, "msg": {"type": "string", "description": "消息内容"}, "data": {"type": "array", "description": "响应数据", "items": {"$ref": "#/components/schemas/DollProfileInfo"}}}, "description": "响应"}, "ResultSysDevice": {"type": "object", "properties": {"code": {"type": "integer", "description": "编码：0表示成功，其他值表示失败", "format": "int32"}, "msg": {"type": "string", "description": "消息内容"}, "data": {"$ref": "#/components/schemas/SysDevice"}}, "description": "响应"}, "SysUser": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "createTime": {"type": "string", "format": "date-time"}, "updateTime": {"type": "string", "format": "date-time"}, "userId": {"type": "integer", "format": "int32"}, "startTime": {"type": "string", "format": "date-time"}, "endTime": {"type": "string", "format": "date-time"}, "username": {"type": "string"}, "name": {"type": "string"}, "totalMessage": {"type": "integer", "format": "int32"}, "aliveNumber": {"type": "integer", "format": "int32"}, "totalDevice": {"type": "integer", "format": "int32"}, "avatar": {"type": "string"}, "state": {"type": "string"}, "isAdmin": {"type": "string"}, "roleId": {"type": "integer", "format": "int32"}, "tel": {"type": "string"}, "email": {"type": "string"}, "loginIp": {"type": "string"}, "code": {"type": "string"}, "loginTime": {"type": "string", "format": "date-time"}}}}}, "x-openapi": {"x-setting": {"customCode": 200, "language": "zh-CN", "enableSwaggerModels": true, "swaggerModelName": "Swagger Models", "enableReloadCacheParameter": false, "enableAfterScript": true, "enableDocumentManage": true, "enableVersion": false, "enableRequestCache": true, "enableFilterMultipartApis": false, "enableFilterMultipartApiMethodType": "POST", "enableHost": false, "enableHostText": "", "enableDynamicParameter": false, "enableDebug": true, "enableFooter": false, "enableFooterCustom": false, "enableSearch": true, "enableOpenApi": true, "enableHomeCustom": false, "enableGroup": true, "enableResponseCode": true}, "x-markdownFiles": []}}
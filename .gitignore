# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.buildlog/
.history
.svn/
migrate_working_dir/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.pub-cache/
.pub/
/build/

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release

.packages
build/
*.lock
/ios/.generated/
/macos/.generated/
/linux/.generated/
/windows/.generated/
*.g.dart
*.freezed.dart
*.config.dart
*.gr.dart
/coverage/
/lib/generated/

# Environment
.env
*.env

# Android
/android/key.properties
/android/local.properties
/android/app/build/
/android/gradle.properties
# iOS
/ios/Runner.xcworkspace
/ios/Runner.xcodeproj/project.xcworkspace
/ios/Runner.xcodeproj/xcshareddata/xcschemes

# VS Code
.vscode/

# Miscellaneous
*.swo

# FVM Version Cache
.fvm/
.fvmrc


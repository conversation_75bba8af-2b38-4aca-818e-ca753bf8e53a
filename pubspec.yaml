name: smartai
description: "智能穿戴设备应用"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: '>=3.4.0 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # 基础UI
  cupertino_icons: ^1.0.6
  flutter_screenutil: ^5.9.3
  
  # 状态管理与路由
  get: ^4.6.6

  esp_blufi: ^0.1.2

  # WiFi扫描
  wifi_scan: ^0.4.1

  # 网络请求
  dio: ^5.4.1
  pretty_dio_logger: ^1.3.1
  
  # 本地存储
  shared_preferences: ^2.2.2
  
  # 权限管理
  permission_handler: ^11.3.1
  
  # 录音和音频播放
  # 使用较低版本的record包，避免Linux平台问题
  record: ^6.0.0
  audioplayers: ^6.0.0
  
  # 蓝牙连接
  flutter_blue_plus: ^1.31.18
  flutter_web_bluetooth: ^1.1.0  # Web平台蓝牙支持
  
  # 图片加载与缓存
  cached_network_image: ^3.3.1
  
  # WebView
  webview_flutter: ^4.7.0
  
  # 实用工具
  flutter_easyloading: ^3.0.5
  fluttertoast: ^8.2.4
  logger: ^2.0.2+1
  device_info_plus: ^11.3.0
  package_info_plus: ^8.3.0
  url_launcher: ^6.2.5
  flutter_native_splash: ^2.3.13
  flutter_launcher_icons: ^0.13.1

  file_picker: ^8.1.6
  http: ^1.2.1
  http_parser: ^4.0.2
  path: ^1.9.0
  just_audio: ^0.9.46
  path_provider: ^2.1.5
  flutter_sound: ^9.2.13
  flutter_secure_storage: ^8.1.0
  qr_flutter: ^4.1.0
  connectivity_plus: ^6.1.4
  
dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0
  build_runner: ^2.4.8

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/icons/
    - assets/audio/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: PingFang
  #     fonts:
  #       - asset: assets/fonts/PingFang-Regular.ttf
  #       - asset: assets/fonts/PingFang-Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
